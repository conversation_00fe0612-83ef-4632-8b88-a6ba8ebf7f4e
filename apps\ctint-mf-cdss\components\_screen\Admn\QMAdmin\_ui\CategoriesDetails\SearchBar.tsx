'use client';

import React from 'react';
import Input from '@cdss-modules/design-system/components/_ui/Input';
import { Search } from 'lucide-react';

interface SearchBarProps {
  searchValue: string;
  onSearchChange: (value: string) => void;
  placeholder?: string;
}

export const SearchBar = React.memo(
  ({
    searchValue,
    onSearchChange,
    placeholder = 'Search...',
  }: SearchBarProps) => {
    return (
      <div className="relative flex-1">
        <Input
          placeholder={placeholder}
          value={searchValue}
          onChange={(value) => onSearchChange(value as string)}
          className="pl-10"
          size="s"
        />
        <Search
          className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
          size={20}
        />
      </div>
    );
  }
);

SearchBar.displayName = 'SearchBar';

export default SearchBar;
