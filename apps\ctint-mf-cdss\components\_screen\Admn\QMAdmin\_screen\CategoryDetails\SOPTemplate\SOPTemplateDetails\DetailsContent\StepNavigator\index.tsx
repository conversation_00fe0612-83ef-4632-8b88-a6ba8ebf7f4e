'use client';

import { Plus, PlusCircle, Trash2 } from 'lucide-react';
import { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import { useSOPTemplateStore } from '../../../../../../_store/sopTemplateStore';
import { StepItem } from './StepItem';
import {
  Status,
  usePermissions,
} from '../../../../../../context/PermissionsContext';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';

import { ConfirmationDialog } from '../../../../../../_ui/SOPTemplateDetails/ConfirmationDialog';

export const StepNavigator = () => {
  const { t } = useTranslation();
  const {
    getSteps,
    currentStep,
    currentScenario,
    setCurrentStep,
    setCurrentScenario,
    isLoading: isTemplateDataLoading,
    templateData,
    addStep,
    removeStep,
    addScenario,
    removeScenario,
    isEditing,
  } = useSOPTemplateStore();

  const { canEdit } = usePermissions();
  const containerRef = useRef<HTMLDivElement>(null);

  const steps = getSteps();
  const status = templateData?.status;
  const creatorId = templateData?.creatorId
    ? String(templateData.creatorId)
    : undefined;
  const userCanEdit = canEdit(status as Status, creatorId);

  // State for confirmation dialog
  const [confirmDialog, setConfirmDialog] = useState<{
    isOpen: boolean;
    title: string;
    message: string;
    onConfirm?: () => void;
  }>({
    isOpen: false,
    title: '',
    message: '',
  });

  // Helper function to get all navigable items (steps and scenarios)
  const getNavigableItems = useMemo(() => {
    const items: Array<{
      type: 'step' | 'scenario';
      stepId: number;
      scenarioId?: string;
    }> = [];

    steps.forEach((step) => {
      // Only add the step itself if it has no scenarios
      if (!step.scenarios || step.scenarios.length === 0) {
        items.push({ type: 'step', stepId: step.id });
      } else {
        // If step has scenarios, add all scenarios (skip the step itself)
        step.scenarios.forEach((scenario) => {
          items.push({
            type: 'scenario',
            stepId: step.id,
            scenarioId: scenario.id,
          });
        });
      }
    });

    return items;
  }, [steps]);

  // Helper function to get current item index
  const getCurrentItemIndex = useMemo(() => {
    return getNavigableItems.findIndex((item) => {
      if (item.type === 'step' && !currentScenario) {
        return item.stepId === currentStep;
      }
      if (item.type === 'scenario' && currentScenario) {
        return (
          item.stepId === currentStep && item.scenarioId === currentScenario
        );
      }
      return false;
    });
  }, [getNavigableItems, currentStep, currentScenario]);

  // Navigation function
  const navigateToItem = useCallback(
    (direction: 'up' | 'down') => {
      const currentIndex = getCurrentItemIndex;

      if (currentIndex === -1) return;

      let nextIndex;
      if (direction === 'down') {
        nextIndex = currentIndex + 1;
        if (nextIndex >= getNavigableItems.length) nextIndex = 0; // Wrap to first item
      } else {
        nextIndex = currentIndex - 1;
        if (nextIndex < 0) nextIndex = getNavigableItems.length - 1; // Wrap to last item
      }

      const nextItem = getNavigableItems[nextIndex];
      if (nextItem) {
        setCurrentStep(nextItem.stepId);
        if (nextItem.type === 'scenario' && nextItem.scenarioId) {
          setCurrentScenario(nextItem.scenarioId);
        } else {
          setCurrentScenario('');
        }
      }
    },
    [getCurrentItemIndex, getNavigableItems, setCurrentStep, setCurrentScenario]
  );

  // Keyboard event handler
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Check if the focused element is within the navigator container
      const activeElement = document.activeElement;
      const isWithinNavigator = containerRef.current?.contains(activeElement);

      // Only handle navigation if the focus is within the navigator
      if (!isWithinNavigator) {
        return;
      }

      // Prevent navigation if a dialog is open
      if (confirmDialog.isOpen) {
        return;
      }

      // Check for arrow key navigation
      if (event.key === 'ArrowDown' || event.key === 'ArrowUp') {
        event.preventDefault();
        event.stopPropagation();

        // Navigate and then refocus the container to maintain keyboard navigation
        const direction = event.key === 'ArrowDown' ? 'down' : 'up';
        navigateToItem(direction);

        // Refocus the container to ensure continued keyboard navigation
        setTimeout(() => {
          containerRef.current?.focus();
        }, 0);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [navigateToItem, confirmDialog.isOpen]);

  // Don't disable buttons during mutations to prevent layout shifts
  const isDisabled = isTemplateDataLoading;

  const handleAddStep = () => {
    addStep();
  };

  const handleRemoveStep = (stepId: number, stepName: string) => {
    setConfirmDialog({
      isOpen: true,
      title: t('ctint-mf-cdss.qmAdmin.sopTemplates.stepNavigator.deleteStep'),
      message: `${t('ctint-mf-cdss.qmAdmin.sopTemplates.stepNavigator.deleteStepConfirm')} "${stepName}"? ${t('ctint-mf-cdss.qmAdmin.sopTemplates.stepNavigator.changesOnlySaved')}`,
      onConfirm: () => {
        removeStep(stepId);
      },
    });
  };

  const handleRemoveScenario = (
    stepId: number,
    scenarioId: string,
    scenarioName: string
  ) => {
    setConfirmDialog({
      isOpen: true,
      title: t(
        'ctint-mf-cdss.qmAdmin.sopTemplates.stepNavigator.deleteScenario'
      ),
      message: `${t('ctint-mf-cdss.qmAdmin.sopTemplates.stepNavigator.deleteScenarioConfirm')} "${scenarioName}"? ${t('ctint-mf-cdss.qmAdmin.sopTemplates.stepNavigator.changesOnlySaved')}`,
      onConfirm: () => {
        removeScenario(stepId, scenarioId);
      },
    });
  };

  return (
    <div
      ref={containerRef}
      className=""
      tabIndex={0}
      role="navigation"
      aria-label={t(
        'ctint-mf-cdss.qmAdmin.sopTemplates.stepNavigator.useArrowKeys'
      )}
      onFocus={() => {
        // If no item is selected, select the first one
        if (steps.length > 0 && currentStep === null) {
          setCurrentStep(steps[0].id);
        }
      }}
    >
      {steps.length === 0 ? (
        <div className="text-center py-8">
          <div className="mb-4">
            <div className="w-12 h-12 mx-auto mb-3 bg-gray-100 rounded-full flex items-center justify-center">
              <svg
                className="w-6 h-6 text-gray-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                />
              </svg>
            </div>
            <p className="text-sm text-gray-500 mb-4">
              {t(
                'ctint-mf-cdss.qmAdmin.sopTemplates.stepNavigator.noStepsCreated'
              )}
            </p>
          </div>
          {isEditing && userCanEdit && (
            <button
              type="button"
              onClick={handleAddStep}
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-150"
              disabled={isDisabled}
            >
              <PlusCircle
                size={16}
                className="mr-2"
              />
              {t(
                'ctint-mf-cdss.qmAdmin.sopTemplates.stepNavigator.addFirstStep'
              )}
            </button>
          )}
        </div>
      ) : (
        <>
          <div className="space-y-1">
            {steps.map((step) => (
              <StepItem
                key={step.id}
                step={step}
                isActive={currentStep === step.id}
                currentScenario={currentScenario}
                onStepChange={setCurrentStep}
                onScenarioChange={setCurrentScenario}
                onAddScenario={
                  isEditing && userCanEdit && !isDisabled
                    ? () => {
                        addScenario(step.id);
                      }
                    : undefined
                }
                onRemoveStep={
                  isEditing && userCanEdit && !isDisabled
                    ? () => handleRemoveStep(step.id, step.name)
                    : undefined
                }
                onRemoveScenario={
                  isEditing && userCanEdit && !isDisabled
                    ? (scenarioId, scenarioName) =>
                        handleRemoveScenario(step.id, scenarioId, scenarioName)
                    : undefined
                }
              />
            ))}
          </div>
          <div className="flex justify-between items-center mt-2">
            {isEditing && userCanEdit && (
              <button
                type="button"
                onClick={handleAddStep}
                className="p-1 rounded hover:bg-gray-100 flex flex-row items-center gap-2 disabled:opacity-50 transition-all duration-150"
                aria-label={t(
                  'ctint-mf-cdss.qmAdmin.sopTemplates.stepNavigator.addStep'
                )}
                title={t(
                  'ctint-mf-cdss.qmAdmin.sopTemplates.stepNavigator.addStep'
                )}
                disabled={isDisabled}
              >
                <PlusCircle size={16} />
                <span className="text-sm">
                  {t(
                    'ctint-mf-cdss.qmAdmin.sopTemplates.stepNavigator.addStep'
                  )}
                </span>
              </button>
            )}
          </div>
        </>
      )}

      {/* Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={confirmDialog.isOpen}
        onOpenChange={(isOpen) =>
          setConfirmDialog({ ...confirmDialog, isOpen })
        }
        title={confirmDialog.title}
        message={confirmDialog.message}
        onConfirm={confirmDialog.onConfirm as () => void}
      />
    </div>
  );
};

export default StepNavigator;
