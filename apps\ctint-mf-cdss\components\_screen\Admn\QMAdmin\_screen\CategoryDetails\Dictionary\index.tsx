import React, { useState, useEffect } from 'react';
import { useTab } from '../../../context/TabContext';
import { ReusableTable } from '../../../_ui/CategoriesDetails/ReusableTable';
import { createDictionaryColumns } from '../../../_ui/CategoriesDetails/TableColumnsHelper';
import DictionaryDetails from './DictionaryDetails';
import {
  useDictionaryUnifiedQuery,
  UnifiedCreateRequest,
} from '../../unified-queries';
import {
  useUnifiedCreateDefinitionMutation,
  UnifiedCreateResponse,
} from '../../definition-queries';
import { DictionaryData } from '../../../types';
import { SearchAndFilter } from '../../../_ui/CategoriesDetails/SearchAndFilter';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';

import { ReusablePopup } from '@cdss/components/_ui/ReusablePopup';
import debounce from 'lodash/debounce';
import { useCallback } from 'react';
import { useSorting } from '../../../hooks/useSorting';
import {
  generateDefaultVersion,
  getTranslatedStatusOptions,
} from '../../../_ui/CategoriesDetails/constants';

interface DictionaryTabProps {
  categoryId: string;
}

export const DictionaryTab: React.FC<DictionaryTabProps> = ({ categoryId }) => {
  const { t } = useTranslation();
  const { selectedItem, setSelectedItem } = useTab();
  const [searchValue, setSearchValue] = useState('');
  const [debouncedSearchValue, setDebouncedSearchValue] = useState('');
  const [statusFilter, setStatusFilter] = useState('Status');
  const [currentPage, setCurrentPage] = useState(1);
  const [perPage, setPerPage] = useState(50);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);

  // Use the sorting hook
  const { orderBy, order, handleSortChange, resetSort } = useSorting({
    defaultOrderBy: 'createTime',
    defaultOrder: 'DESC',
  });

  // Reset search and pagination when category changes
  useEffect(() => {
    setSearchValue('');
    setDebouncedSearchValue('');
    setStatusFilter('Status');
    setCurrentPage(1);
    // Reset sorting when category changes
    resetSort();
  }, [categoryId, resetSort]);

  // Debounce search to avoid excessive API calls
  const debouncedSearch = useCallback(
    debounce((value: string) => {
      setDebouncedSearchValue(value);
      setCurrentPage(1); // Reset to first page on new search
    }, 400),
    []
  );

  // Update debounced search when search value changes
  useEffect(() => {
    debouncedSearch(searchValue);
  }, [searchValue, debouncedSearch]);

  // Data fetching with the new unified React Query - using the unified API
  const { data: dictionaryResponse, isLoading: isListLoading } =
    useDictionaryUnifiedQuery(
      categoryId, // parentId as string to match the unified query pattern
      debouncedSearchValue,
      statusFilter === 'Status' ? '' : statusFilter,
      currentPage,
      perPage,
      orderBy, // Pass orderBy
      order // Pass order
    );

  console.log(dictionaryResponse);

  // Add the create mutation hook - using the unified approach
  const createDictionaryMutation = useUnifiedCreateDefinitionMutation();

  // Extract dictionary list and total from the API response - matching categories pattern
  const dictionaryList = dictionaryResponse?.data?.items || [];
  const totalItems = dictionaryResponse?.data?.totalCount || 0;
  const totalPages = dictionaryResponse?.data?.totalPage || 0;

  // Transform the API response to match the expected DictionaryData interface
  const transformedDictionaryList: DictionaryData[] = dictionaryList.map(
    (item: any) => ({
      ...item,
      createdDate: item.createTime,
      lastUpdated: item.updateTime,
      approvedBy: item.approveUser,
      createdBy: item.createUser,
      language: item.language, // Add language property with fallback
    })
  );

  const handleDictionaryClick = (dictionaryItem: DictionaryData) => {
    setSelectedItem({ type: 'dictionary', id: dictionaryItem.id });
  };

  const handleCreateSubmit = (values: Record<string, string>) => {
    const { name, version } = values;

    // Use the unified create mutation
    const request: UnifiedCreateRequest = {
      name,
      relationshipType: 'dictionary',
      status: 'Draft',
      version: version || '1.0',
      parentId: categoryId, // Keep as string to match the unified pattern
    };

    createDictionaryMutation.mutate(request, {
      onSuccess: (response: UnifiedCreateResponse) => {
        // Set the newly created dictionary as selected
        const newDictionary = response.data;
        setSelectedItem({ type: 'dictionary', id: newDictionary.id });
        setIsCreateModalOpen(false);
      },
      onError: () => {
        // Keep the modal open on error so user can retry
        // Error handling is already done in the mutation hook
      },
    });
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handlePerPageChange = (value: number) => {
    setPerPage(value);
    setCurrentPage(1); // Reset to page 1 when changing items per page
  };

  // Handler for sorting changes from table - now uses the hook
  const handleTableSortChange = (
    newOrderBy: string,
    newOrder: 'ASC' | 'DESC' | undefined
  ) => {
    handleSortChange(newOrderBy, newOrder);
    setCurrentPage(1); // Reset to first page on sort change
  };

  const createDictionaryFields = [
    {
      key: 'name',
      label: t('ctint-mf-cdss.qmAdmin.dictionary.dictionaryName'),
      placeholder: t('ctint-mf-cdss.qmAdmin.dictionary.enterDictionaryName'),
      required: true,
    },
    {
      key: 'version',
      label: t('ctint-mf-cdss.qmAdmin.dictionary.version'),
      placeholder: t('ctint-mf-cdss.qmAdmin.dictionary.enterVersion'),
      initialValue: generateDefaultVersion(),
      required: true,
    },
  ];

  // Create columns using the helper
  const columns = createDictionaryColumns<DictionaryData>(
    {
      orderBy,
      order,
      onSort: handleTableSortChange,
    },
    t
  );

  // If we have a selected dictionary item, show the details component
  // We don't pass the dictionary data from the list view - the detail component will fetch its own data
  if (selectedItem.type === 'dictionary') {
    return <DictionaryDetails dictionaryId={selectedItem.id as string} />;
  }

  return (
    <div className="p-6 flex-1 flex flex-col h-full overflow-y-auto">
      <div className="flex justify-between items-center mb-4">
        <SearchAndFilter
          searchValue={searchValue}
          setSearchValue={setSearchValue}
          statusFilter={statusFilter}
          setStatusFilter={setStatusFilter}
          statusOptions={getTranslatedStatusOptions(t)}
        />
        <ReusablePopup
          isOpen={isCreateModalOpen}
          onOpenChange={setIsCreateModalOpen}
          title={t('ctint-mf-cdss.qmAdmin.dictionary.createDictionary')}
          fields={createDictionaryFields}
          onSubmitMultiple={handleCreateSubmit}
          triggerButtonText={t(
            'ctint-mf-cdss.qmAdmin.dictionary.createDictionary'
          )}
          submitButtonText={t('ctint-mf-cdss.qmAdmin.dictionary.create')}
        />
      </div>
      <ReusableTable
        data={transformedDictionaryList}
        columns={columns}
        isLoading={isListLoading}
        onRowClick={handleDictionaryClick}
        currentPage={currentPage}
        perPage={perPage}
        onPageChange={handlePageChange}
        onPerPageChange={handlePerPageChange}
        totalItems={totalItems}
        totalPages={totalPages}
        emptyMessage={t('ctint-mf-cdss.qmAdmin.dictionary.noDataFound')}
      />
    </div>
  );
};
