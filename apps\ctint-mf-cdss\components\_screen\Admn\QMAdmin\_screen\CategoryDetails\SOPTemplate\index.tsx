import React, { useState, useEffect } from 'react';
import { useTab } from '../../../context/TabContext';
import {
  ReusableTable,
  BaseTableData,
} from '../../../_ui/CategoriesDetails/ReusableTable';
import { createSOPFormTemplateColumns } from '../../../_ui/CategoriesDetails/TableColumnsHelper';
import { usePermissions } from '../../../context/PermissionsContext';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';

import { SearchAndFilter } from '../../../_ui/CategoriesDetails/SearchAndFilter';
import Button from '@cdss-modules/design-system/components/_ui/Button';
import debounce from 'lodash/debounce';
import { useCallback } from 'react';
import { ReusablePopup } from '@cdss/components/_ui/ReusablePopup';
import SOPTemplateDetails from './SOPTemplateDetails';
import {
  useSOPTemplateListQuery,
  useCreateSOPTemplateMutation,
  SOPTemplateCreateRequest,
  SOPTemplateFormItem,
} from '../sop-template-queries';
import { useSorting } from '../../../hooks/useSorting';
import {
  STATUS_OPTIONS,
  generateDefaultVersion,
  getTranslatedStatusOptions,
  getTranslatedStatusOptionsSOPTemplate,
} from '../../../_ui/CategoriesDetails/constants';

// ======================================
// Interface for transformed SOP Template data to match ReusableTable expectations
// ======================================
interface TransformedSOPTemplateData extends BaseTableData {
  formId: string;
  formVersion: string;
  product: string;
  createUser: string;
  createTime: string;
  updateTime: string;
  category: string;
}

interface SOPTemplateTabProps {
  categoryId: string;
}

export const SOPTemplateTab: React.FC<SOPTemplateTabProps> = ({
  categoryId,
}) => {
  const { t } = useTranslation();
  const { selectedItem, setSelectedItem } = useTab();
  const { userId, canEdit } = usePermissions();
  const [searchValue, setSearchValue] = useState('');
  const [debouncedSearchValue, setDebouncedSearchValue] = useState('');
  const [statusFilter, setStatusFilter] = useState('Status');
  const [currentPage, setCurrentPage] = useState(1);
  const [perPage, setPerPage] = useState(50);
  const [isCreatePopupOpen, setIsCreatePopupOpen] = useState(false);

  // Use the sorting hook
  const { orderBy, order, handleSortChange, resetSort } = useSorting({
    defaultOrderBy: 'createTime',
    defaultOrder: 'DESC',
  });

  // Reset search and pagination when category changes
  useEffect(() => {
    setSearchValue('');
    setDebouncedSearchValue('');
    setStatusFilter('Status');
    setCurrentPage(1);
    // Reset sorting when category changes
    resetSort();
  }, [categoryId, resetSort]);

  // Debounce search to avoid excessive API calls
  const debouncedSearch = useCallback(
    debounce((value: string) => {
      setDebouncedSearchValue(value);
      setCurrentPage(1); // Reset to first page on new search
    }, 400),
    []
  );

  // Update debounced search when search value changes
  useEffect(() => {
    debouncedSearch(searchValue);
  }, [searchValue, debouncedSearch]);

  // Data fetching with the new SOP template specific React Query
  const { data: templatesResponse, isLoading } = useSOPTemplateListQuery(
    categoryId,
    debouncedSearchValue,
    statusFilter === 'Status' ? '' : statusFilter,
    currentPage,
    perPage,
    orderBy,
    order
  );

  // Add the create mutation hook - using the SOP template specific approach
  const createTemplateMutation = useCreateSOPTemplateMutation();

  // Extract templates and total from the API response
  const templates = templatesResponse?.data?.items || [];
  const totalItems = templatesResponse?.data?.totalCount || 0;
  const totalPages = templatesResponse?.data?.totalPage || 0;

  // Transform the API response to match the expected table data interface
  const transformedTemplates: TransformedSOPTemplateData[] = templates.map(
    (item: SOPTemplateFormItem) => ({
      formId: item.formId,
      id: item.formId, // Map formId to id for compatibility
      name: item.name,
      status: item.status,
      version: item.formVersion, // Map formVersion to version for BaseTableData compatibility
      approvedBy: '', // No approvedBy field in SOP template data, so provide empty string
      formVersion: item.formVersion,
      product: item.product,
      createUser: item.createUser || '',
      createTime: item.createTime,
      updateTime: item.updateTime,
      category: item.category,
      language: item.language,
    })
  );

  const handleTemplateClick = (template: TransformedSOPTemplateData) => {
    setSelectedItem({ type: 'sopTemplate', id: template.formId });
  };

  const handleCreateClick = () => {
    setIsCreatePopupOpen(true);
  };

  const handleCreateSubmit = (values: Record<string, string>) => {
    const { name, formVersion, product } = values;

    // Use the SOP template specific create mutation
    const request: SOPTemplateCreateRequest = {
      name,
      category: categoryId,
      status: 'Draft',
      formVersion: formVersion || '0.1',
      product: product || 'Default',
    };

    createTemplateMutation.mutate(request, {
      onSuccess: (response) => {
        // Set the newly created template as selected
        const newTemplate = response.data;
        setSelectedItem({ type: 'sopTemplate', id: newTemplate.formId });
        setIsCreatePopupOpen(false);
      },
      onError: () => {
        // Keep the popup open on error so user can retry
        // Error handling is already done in the mutation hook
      },
    });
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handlePerPageChange = (value: number) => {
    setPerPage(value);
    setCurrentPage(1); // Reset to page 1 when changing items per page
  };

  // Handler for sorting changes from table - now uses the hook
  const handleTableSortChange = (
    newOrderBy: string,
    newOrder: 'ASC' | 'DESC' | undefined
  ) => {
    handleSortChange(newOrderBy, newOrder);
    setCurrentPage(1); // Reset to first page on sort change
  };

  const createTemplateFields = [
    {
      key: 'name',
      label: t('ctint-mf-cdss.qmAdmin.sopTemplates.templateName'),
      placeholder: t('ctint-mf-cdss.qmAdmin.sopTemplates.enterTemplateName'),
      required: true,
    },
    {
      key: 'formVersion',
      label: t('ctint-mf-cdss.qmAdmin.sopTemplates.version'),
      placeholder: t('ctint-mf-cdss.qmAdmin.sopTemplates.enterVersion'),
      initialValue: generateDefaultVersion(),
      required: true,
    },
    {
      key: 'product',
      label: t('ctint-mf-cdss.qmAdmin.sopTemplates.product'),
      placeholder: t('ctint-mf-cdss.qmAdmin.sopTemplates.enterProduct'),
      initialValue: 'Default',
      required: true,
    },
  ];

  // Create columns using the new SOP form template helper
  const columns = createSOPFormTemplateColumns<TransformedSOPTemplateData>(
    {
      orderBy,
      order,
      onSort: handleTableSortChange,
    },
    t
  );

  // If we're in details view, show the details component
  // Pass the string ID directly since SOPTemplateDetails now expects string
  if (selectedItem.type === 'sopTemplate') {
    const templateId = selectedItem.id as string;
    return <SOPTemplateDetails templateId={templateId} />;
  }

  // Otherwise show the table view
  return (
    <div className="p-6 flex flex-col overflow-auto flex-1">
      <div className="flex justify-between items-center mb-4">
        <SearchAndFilter
          searchValue={searchValue}
          setSearchValue={setSearchValue}
          statusFilter={statusFilter}
          setStatusFilter={setStatusFilter}
          statusOptions={getTranslatedStatusOptionsSOPTemplate(t)}
        />
        {/* <Button onClick={handleCreateClick}>{t('ctint-mf-cdss.qmAdmin.sopTemplates.createTemplate')}</Button> */}
      </div>
      <ReusablePopup
        isOpen={isCreatePopupOpen}
        onOpenChange={setIsCreatePopupOpen}
        title={t('ctint-mf-cdss.qmAdmin.sopTemplates.createTemplate')}
        fields={createTemplateFields}
        onSubmitMultiple={handleCreateSubmit}
        triggerButtonText={t(
          'ctint-mf-cdss.qmAdmin.sopTemplates.createTemplate'
        )}
        submitButtonText={t('ctint-mf-cdss.qmAdmin.sopTemplates.create')}
        showTrigger={false}
      />
      <ReusableTable
        data={transformedTemplates}
        columns={columns}
        isLoading={isLoading}
        onRowClick={handleTemplateClick}
        currentPage={currentPage}
        perPage={perPage}
        onPageChange={handlePageChange}
        onPerPageChange={handlePerPageChange}
        totalItems={totalItems}
        totalPages={totalPages}
        emptyMessage={t('ctint-mf-cdss.qmAdmin.sopTemplates.noDataFound')}
      />
    </div>
  );
};
