export const apiConfig = {
  paths: {
    detail: {
      info: '/api/process-api/ctint-conv/interaction/detail',
      media: '/api/process-api/ctint-conv/interaction/recordings',
      transcript:
        '/api/process-api/ctint-conv/interaction/recordings/transcript',
      evaluation_form: '/api/process-api/ctint-qm/sop/form/list',
      evaluation_list: '/api/process-api/ctint-qm/inspection/evaluation/list',
      evaluation_assign: '/api/process-api/ctint-qm/inspection/evaluation',
      evaluation_nlp_result_update:
        '/api/process-api/ctint-qm/inspection/nlpResult/detail',
      evaluation_nlp_result_list:
        '/api/process-api/ctint-qm/inspection/nlpResult/detail/list',
      evaluation_result_update:
        '/api/process-api/ctint-qm/inspection/evaluation/detail',
      stand_script_result: '/api/process-api/ctint-qm/sop/standardScript/list',
      download_nlp_report:
        '/api/process-api/ctint-qm/inspection/excel/download',
    },
    recordings: '/api/process-api/ctint-conv/recordings',
    export: '/api/process-api/ctint-conv/interaction',
    gc_recordings: '/api/process-api/ctint-conv/interactions',
    sort: 'http://localhost:4400/ctint/mf-cdss/api/mock/ctint-conv/recordings',
    config: '/api/process-api/ctint-config/userconfig',
    transcript:
      'http://localhost:4400/ctint/mf-cdss/api/mock/ctint-conv/recordings/transcript',
    // api for announcer: call the same microservice as the call page. (refer to ctint-mf-cdss)
    get_announcer_message_list: '/api/process-api/ctint-ccba-api/getAnnouncerMessages',
    get_call_nature_list: '/api/process-api/ctint-ccba-api/getCallNatures',
    save_call_nature: '/api/process-api/ctint-ccba-api/saveCallNature',
    play_announcer_message: '/api/process-api/ctint-ccba-api/playAnnouncerMessage',
    stop_announcer_message: '/api/process-api/ctint-ccba-api/stopAnnouncerMessage',
    face_to_face: '/api/process-api/ctint-ccba-api/faceToFace',
    // cdss api
    get_conversation_active_list: '/api/process-api/ctint/conv/conversation/activeList',
    get_ctint_call_control_stations_users_by_user_id: '/api/process-api/ctint/call-control/stations/users',
  },
};