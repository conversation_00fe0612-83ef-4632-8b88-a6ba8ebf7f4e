'use client';

import { useState, useEffect } from 'react';
import { Controller, FormProvider, useForm } from 'react-hook-form';
import { Loader, toast } from '@cdss-modules/design-system';
import Input from '@cdss-modules/design-system/components/_ui/Input';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';

// Import store and types
import { useSOPTemplateStore } from '../../../../../_store/sopTemplateStore';
import {
  SOPTemplateDetailedData,
  ScenarioData,
  StepData,
} from '../../../sop-template-queries';

// Permissions context
import { Status } from '../../../../../context/PermissionsContext';

// Import ActionButtons component
import { ActionButtons } from './ActionButtons';

interface SimpleDetailsContentProps {
  onSave?: (data: SOPTemplateDetailedData) => void;
  onStatusChange?: (status: Status) => void;
}

// Transform the SOP template data to a flat structure similar to standard script data
interface FlattenedScenarioData {
  standardScriptId: string;
  stepId: number;
  scenarioId: string;
  content: string;
  doSimilarity: boolean;
  similarityRequired: number;
}

export const SimpleDetailsContent = ({
  onSave,
  onStatusChange,
}: SimpleDetailsContentProps) => {
  const { t } = useTranslation();

  // Get data from the store
  const {
    templateData,
    isLoading,
    isEditing,
    updateScenarioField,
    getChangedSimilarityValues,
    clearChangedSimilarityValues,
  } = useSOPTemplateStore();

  // Form setup
  const methods = useForm();
  const {
    control,
    formState: { errors },
    setValue,
  } = methods;

  // Flatten the SOP template data to display in table format
  const flattenedData: FlattenedScenarioData[] = [];

  if (templateData?.steps) {
    templateData.steps.forEach((step: StepData) => {
      step.scenarios.forEach((scenario: ScenarioData) => {
        flattenedData.push({
          standardScriptId: scenario.standardScriptId,
          stepId: step.id,
          scenarioId: scenario.id,
          content: scenario.script, // Using Cantonese script as default content
          doSimilarity: scenario.doSimilarity,
          similarityRequired: scenario.similarityRequired,
        });
      });
    });
  }

  // Initialize form values when template data changes
  useEffect(() => {
    if (templateData?.steps) {
      flattenedData.forEach((item) => {
        setValue(item.standardScriptId, item.similarityRequired);
      });
    }
  }, [templateData, setValue]);

  // Handle similarity score change
  const handleSimilarityChange = (
    standardScriptId: string,
    stepId: number,
    scenarioId: string,
    value: number
  ) => {
    // Update the store with the new similarity value
    updateScenarioField(stepId, scenarioId, 'similarityRequired', value);
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-full">
        <Loader size={64} />
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Action Buttons - Fixed at top */}
      <div className="flex-shrink-0 pt-4 pr-4 self-end">
        <ActionButtons
          onStatusChange={onStatusChange}
          onSave={onSave}
        />
      </div>

      <FormProvider {...methods}>
        <form className="p-6 mb-4 flex flex-col gap-4 overflow-y-auto h-full">
          <div className="flex flex-col gap-4 flex-1 h-0 overflow-auto pr-4">
            <table className="text-left">
              <thead>
                <tr className="border-b border-grey-100 text-center">
                  <th className="py-2 truncate">
                    {t('ctint-mf-cdss.qmAdmin.sop.columns.step')}
                  </th>
                  <th className="py-2 truncate">
                    {t('ctint-mf-cdss.qmAdmin.sop.columns.scenarioId')}
                  </th>
                  <th className="py-2 truncate">
                    {t('ctint-mf-cdss.qmAdmin.sop.columns.content')}
                  </th>
                  <th className="py-2 truncate">
                    {t(
                      'ctint-mf-cdss.qmAdmin.sop.columns.doSentenceSimilarity'
                    )}
                  </th>
                  <th className="py-2 truncate">
                    {t('ctint-mf-cdss.qmAdmin.sop.columns.scoreToPassed')}
                  </th>
                </tr>
              </thead>
              <tbody>
                {flattenedData && flattenedData.length > 0 ? (
                  flattenedData.map((thisData: FlattenedScenarioData) => {
                    const key = thisData.standardScriptId;
                    return (
                      <tr
                        key={`sop-step-${key}`}
                        className="border-b border-grey-100"
                      >
                        <td className="py-2 text-center">
                          {thisData.stepId || '-'}
                        </td>
                        <td className="py-2 text-center">
                          {thisData.scenarioId || '-'}
                        </td>
                        <td className="py-2 max-w-[400px]">
                          {thisData.content || '-'}
                        </td>
                        <td className="py-2 text-center">
                          {thisData.doSimilarity
                            ? t('ctint-mf-cdss.qmAdmin.sop.yes')
                            : t('ctint-mf-cdss.qmAdmin.sop.no')}
                        </td>
                        <td className="py-2 text-center">
                          {thisData.doSimilarity && isEditing ? (
                            <div className="flex items-center justify-center">
                              <Controller
                                name={key}
                                control={control}
                                defaultValue={thisData.similarityRequired}
                                render={({ field }) => (
                                  <Input
                                    {...field}
                                    type="text"
                                    className="text-center"
                                    placeholder="Enter Score"
                                    onChange={(value) => {
                                      const inputValue = value.toString();
                                      // Limit input to at most 2 decimal places
                                      if (
                                        inputValue &&
                                        /^\d*\.?\d{0,2}$/.test(inputValue)
                                      ) {
                                        field.onChange(value);
                                        // Update the store with the new value
                                        const numValue =
                                          parseFloat(inputValue) || 0;
                                        handleSimilarityChange(
                                          thisData.standardScriptId,
                                          thisData.stepId,
                                          thisData.scenarioId,
                                          numValue
                                        );
                                      }
                                    }}
                                    onBlur={(e) => {
                                      const value = parseFloat(e.target.value);
                                      if (!isNaN(value)) {
                                        const decimalPlaces =
                                          value.toString().split('.')[1]
                                            ?.length || 0;
                                        if (decimalPlaces > 2) {
                                          // Format to 2 decimal places only if more than 2 decimal places
                                          const formattedValue =
                                            value.toFixed(2);
                                          field.onChange(formattedValue);
                                          // Update the store with the formatted value
                                          handleSimilarityChange(
                                            thisData.standardScriptId,
                                            thisData.stepId,
                                            thisData.scenarioId,
                                            parseFloat(formattedValue)
                                          );
                                        }
                                      }
                                    }}
                                    size="s"
                                  />
                                )}
                              />
                            </div>
                          ) : thisData.doSimilarity ? (
                            <span>{thisData.similarityRequired || '--'}</span>
                          ) : (
                            <span>{'--'}</span>
                          )}
                        </td>
                      </tr>
                    );
                  })
                ) : (
                  <tr>
                    <td
                      colSpan={5}
                      className="py-2 text-center"
                    >
                      {t('ctint-mf-cdss.qmAdmin.emptyData')}
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </form>
      </FormProvider>
    </div>
  );
};

export default SimpleDetailsContent;
