'use client';

import { useState } from 'react';
import { Button } from '@cdss-modules/design-system';
import Input from '@cdss-modules/design-system/components/_ui/Input';

// Comment interface
export interface CommentType {
  id: string;
  text: string;
  date: Date;
  user: string;
}

interface CommentSectionProps {
  comments: CommentType[];
  onAddComment: (comment: CommentType) => void;
  containerClassName?: string;
  isApprover?: boolean;
  isReturned?: boolean;
}

export const CommentSection = ({
  comments,
  onAddComment,
  containerClassName = 'm-6 border rounded-lg p-4 flex flex-col',
  isApprover = false,
  isReturned = false,
}: CommentSectionProps) => {
  const [currentComment, setCurrentComment] = useState('');

  const handleCommentSubmit = () => {
    if (currentComment.trim()) {
      const newComment: CommentType = {
        id: Date.now().toString(),
        text: currentComment,
        date: new Date(),
        user: 'Current User', // In a real app, this would come from auth context
      };

      onAddComment(newComment);
      setCurrentComment('');
    }
  };

  if (isReturned && !isApprover) {
    return (
      <div className={containerClassName}>Comment Section from checker</div>
    );
  }

  if (!isApprover) {
    return null;
  }

  return (
    <div className={containerClassName}>
      {/* Display submitted comments */}
      {comments.length > 0 && (
        <div className="mb-4 space-y-2 max-h-[100px] overflow-y-auto">
          {comments.map((comment) => (
            <div
              key={comment.id}
              className="p-3 bg-gray-50 rounded-md"
            >
              <div className="flex justify-between text-sm text-gray-500">
                <span>{comment.user}</span>
                <span>{comment.date.toLocaleString()}</span>
              </div>
              <p className="mt-1">{comment.text}</p>
            </div>
          ))}
        </div>
      )}

      {/* Comment input */}
      <div className="flex items-center gap-2">
        <span>Comment:</span>
        <Input
          placeholder="Return button will be disabled if no comments are left"
          size="s"
          value={currentComment}
          onChange={(value) => setCurrentComment(value as string)}
        />
        <Button
          size="s"
          variant="primary"
          onClick={handleCommentSubmit}
          disabled={!currentComment.trim()}
        >
          Submit
        </Button>
      </div>
    </div>
  );
};

export default CommentSection;
