'use client';

import CallControlButton from '@cdss-modules/design-system/components/_ui/CallControlButton';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import { useState } from 'react';

const CallControlButtonDemo = () => {
  const [padActive, setPadActive] = useState(false);
  const [muteActive, setMuteActive] = useState(false);
  const [holdActive, setHoldActive] = useState(false);
  const [transferActive, setTransferActive] = useState(false);
  const [consultActive, setConsultActive] = useState(false);
  const [groupActive, setGroupActive] = useState(false);

  const handlePadActive = () => {
    setPadActive(!padActive);
  };

  const handleMuteActive = () => {
    setMuteActive(!muteActive);
  };

  const handleHoldActive = () => {
    setHoldActive(!holdActive);
  };

  const handleTransferActive = () => {
    setTransferActive(!transferActive);
  };

  const handleConsultActive = () => {
    setConsultActive(!consultActive);
  };

  const handleGroupActive = () => {
    setGroupActive(!groupActive);
  };

  return (
    <div className="flex flex-col justify-between w-[473px] gap-4">
      <div className="flex justify-between item-center">
        <CallControlButton
          className="justify-self-start"
          icon={
            <Icon
              name="pad"
              size={64}
            />
          }
          label={'Pad'}
          active={padActive}
          handleOnChange={handlePadActive}
        />
        <CallControlButton
          icon={
            <Icon
              name="mute"
              size={64}
            />
          }
          label={'Mute'}
          active={muteActive}
          handleOnChange={handleMuteActive}
        />
        <CallControlButton
          className="justify-self-end"
          icon={
            <Icon
              name="hold"
              size={32}
            />
          }
          label={'Hold'}
          active={holdActive}
          handleOnChange={handleHoldActive}
        />
      </div>
      <div className="w-full border border-grey-200" />
      <div className="flex justify-between item-center">
        <CallControlButton
          className="justify-self-start"
          icon={
            <Icon
              name="transfer"
              size={32}
            />
          }
          label={'Transfer'}
          active={transferActive}
          handleOnChange={handleTransferActive}
        />
        <CallControlButton
          icon={
            <Icon
              name="consult"
              size={32}
            />
          }
          label={'Consult'}
          active={consultActive}
          handleOnChange={handleConsultActive}
        />
        <CallControlButton
          className="justify-self-end"
          icon={
            <Icon
              name="group"
              size={32}
            />
          }
          label={'Group'}
          active={groupActive}
          handleOnChange={handleGroupActive}
        />
      </div>
    </div>
  );
};

export default CallControlButtonDemo;
