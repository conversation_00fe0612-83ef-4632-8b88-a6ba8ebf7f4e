'use client';

import { useState, useEffect, useCallback } from 'react';
import { Loader, Toaster, useToast } from '@cdss-modules/design-system';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';
import Button from '@cdss-modules/design-system/components/_ui/Button';
import { useQueryClient, useMutation } from '@tanstack/react-query';

import { EditableBreadcrumb } from '../../../../_ui/CategoriesDetails/EditableBreadcrumb';
import { usePermissions, Status } from '../../../../context/PermissionsContext';
import { useTab } from '../../../../context/TabContext';

import { DetailsContent } from './DetailsContent';
import { SOPTemplateDetailsProps } from './types';

// Zustand store
import { useSOPTemplateStore } from '../../../../_store/sopTemplateStore';

// Import API functions from sop-template-queries.ts
import {
  useUpdateSOPTemplateMutation,
  useSOPTemplateFullDetailQuery,
  useUpdateStandardScriptDetailMutation,
  SOPTemplateUpdateRequest,
  SOPTemplateDetailedData,
} from '../../sop-template-queries';

// Import the unified status mutation from definition-queries
import { useUnifiedUpdateDefinitionStatusMutation } from '../../../definition-queries';

import { StatusChangeDialog } from '../../../../_ui/SOPTemplateDetails/StatusChangeDialog';
import SimpleDetailsContent from './DetailsContent/SimpleDetailsContent';

// Define types that were missing from storageUtils
export type TemplateStatus =
  | 'Draft'
  | 'Pending Approval'
  | 'Approved'
  | 'Returned';

// Simple error boundary component
const ErrorBoundary = ({ children }: { children: React.ReactNode }) => {
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    const handleError = () => {
      setHasError(true);
    };

    window.addEventListener('error', handleError);

    return () => {
      window.removeEventListener('error', handleError);
    };
  }, []);

  if (hasError) {
    return (
      <div className="p-4 bg-red-50 border border-red-200 rounded">
        <h2 className="text-red-700 font-bold">Something went wrong</h2>
        <p className="text-red-600">Please try refreshing the page</p>
      </div>
    );
  }

  return <>{children}</>;
};

export const SOPTemplateDetails = ({ templateId }: SOPTemplateDetailsProps) => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const [isEditMode, setIsEditMode] = useState(false);
  const [isUpdatingViaSetData, setIsUpdatingViaSetData] = useState(false);
  const [statusDialogState, setStatusDialogState] = useState({
    isOpen: false,
    status: '' as Status,
    comment: '',
  });

  // Zustand store
  const {
    loadTemplateData,
    resetStore,
    getChangedSimilarityValues,
    clearChangedSimilarityValues,
  } = useSOPTemplateStore();

  // Fetch detailed template data with React Query using the new API
  const {
    data: templateResponse,
    isLoading,
    isError,
  } = useSOPTemplateFullDetailQuery(templateId.toString());

  // Extract the actual template data
  const templateData = templateResponse?.data;

  // Use the existing update mutation from sop-template-queries.ts
  const updateSOPTemplateMutation = useUpdateSOPTemplateMutation();

  // Use the new standard script detail update mutation
  const updateStandardScriptDetailMutation =
    useUpdateStandardScriptDetailMutation();

  // Use the unified status update mutation for real API calls
  const updateStatusMutation = useUnifiedUpdateDefinitionStatusMutation();

  // Memoized template data loading function to prevent infinite loops
  const handleLoadTemplateData = useCallback(() => {
    if (templateData && !isUpdatingViaSetData) {
      loadTemplateData(templateData);
    }
  }, [templateData, isUpdatingViaSetData, loadTemplateData]);

  // Initialize store when template data is loaded
  useEffect(() => {
    handleLoadTemplateData();
  }, [handleLoadTemplateData]);

  // Cleanup store when component unmounts
  useEffect(() => {
    return () => {
      resetStore();
    };
  }, [resetStore]);

  const status = (templateData?.status as Status) || 'Draft';
  const itemName =
    templateData?.name ||
    t('ctint-mf-cdss.qmAdmin.sopTemplates.details.sopTemplateDefault');
  const creatorId = templateData?.creatorId
    ? String(templateData.creatorId)
    : undefined;
  const versionName = templateData?.version || '0.1';

  const { canEdit, canApprove } = usePermissions();
  const isEditable = canEdit(status, creatorId);

  const { handleBackToList, categoryName } = useTab();

  // Update template mutation using real API
  const updateTemplateMutation = useMutation({
    mutationFn: async (data: SOPTemplateDetailedData) => {
      // Get only the changed similarity values from the store
      const changedSimilarityValues = getChangedSimilarityValues();

      if (changedSimilarityValues.length === 0) {
        // If no changes, just return the data without making API call
        return data;
      }

      // Call the real API to update standard script details
      await updateStandardScriptDetailMutation.mutateAsync(
        changedSimilarityValues
      );

      // Clear the tracked changes after successful API call
      clearChangedSimilarityValues();

      return data;
    },
    onSuccess: (updatedTemplate: SOPTemplateDetailedData) => {
      // Set flag to prevent loadTemplateData from being called
      setIsUpdatingViaSetData(true);

      // Don't invalidate queries immediately to prevent button flickering
      // Instead, update the query data directly
      queryClient.setQueryData(['sopTemplateFullDetail', templateId], {
        data: updatedTemplate,
        error: '',
        isSuccess: true,
      });

      // Reset flag after a short delay to allow React to process the update
      setTimeout(() => {
        setIsUpdatingViaSetData(false);
      }, 100);

      toast({
        title: t(
          'ctint-mf-cdss.qmAdmin.sopTemplates.details.templateSavedSuccessfully'
        ),
        description: t(
          'ctint-mf-cdss.qmAdmin.sopTemplates.details.templateSavedSuccessfully'
        ),
        variant: 'success',
      });
      setIsEditMode(false);
    },
    onError: (error) => {
      toast({
        title: t(
          'ctint-mf-cdss.qmAdmin.sopTemplates.details.failedToSaveTemplate'
        ),
        description: t(
          'ctint-mf-cdss.qmAdmin.sopTemplates.details.failedToSaveTemplate'
        ),
        variant: 'error',
      });
    },
  });

  // Change template status mutation using real API
  const changeStatusMutation = useMutation({
    mutationFn: async ({
      id,
      newStatus,
      comment,
    }: {
      id: number;
      newStatus: TemplateStatus;
      comment?: string;
    }) => {
      // Call the real unified status update API
      const result = await updateStatusMutation.mutateAsync({
        id: id.toString(),
        request: {
          status: newStatus,
        },
      });

      return result.data;
    },
    onSuccess: (updatedTemplate) => {
      queryClient.invalidateQueries({
        queryKey: ['sopTemplateFullDetail', templateId],
      });
      toast({
        title: t(
          'ctint-mf-cdss.qmAdmin.sopTemplates.details.templateStatusChanged'
        ),
        description: `${t('ctint-mf-cdss.qmAdmin.sopTemplates.details.templateStatusChanged')} ${updatedTemplate.status}`,
        variant: 'success',
      });
      closeStatusDialog();
    },
    onError: (error) => {
      toast({
        title: t(
          'ctint-mf-cdss.qmAdmin.sopTemplates.details.failedToChangeStatus'
        ),
        description: t(
          'ctint-mf-cdss.qmAdmin.sopTemplates.details.failedToChangeStatus'
        ),
        variant: 'error',
      });
      closeStatusDialog();
    },
  });

  // Handle title change using the real API mutation
  const handleTitleChange = useCallback(
    (newTitle: string) => {
      if (templateData) {
        const updateRequest: SOPTemplateUpdateRequest = {
          name: newTitle,
        };
        updateSOPTemplateMutation.mutate({
          id: templateData.id,
          request: updateRequest,
        });
      }
    },
    [templateData, updateSOPTemplateMutation]
  );

  // Handle version change using the real API mutation
  const handleVersionChange = useCallback(
    (newVersion: string) => {
      if (templateData) {
        const updateRequest: SOPTemplateUpdateRequest = {
          formVersion: newVersion,
        };
        updateSOPTemplateMutation.mutate({
          id: templateData.id,
          request: updateRequest,
        });
      }
    },
    [templateData, updateSOPTemplateMutation]
  );

  // Simplified status change handler
  const handleStatusChange = useCallback(
    (newStatus: Status) => {
      // Only allow certain status changes based on current status
      const allowedStatusChanges: Record<Status, Status[]> = {
        Draft: ['Pending Approval'],
        'Pending Approval': ['Approved', 'Returned'],
        Returned: ['Pending Approval'],
        Approved: [],
      };

      if (!allowedStatusChanges[status].includes(newStatus)) {
        toast({
          title: t(
            'ctint-mf-cdss.qmAdmin.sopTemplates.details.invalidStatusChange'
          ),
          description: `${t('ctint-mf-cdss.qmAdmin.sopTemplates.details.cannotChangeStatus')} ${status} ${t('ctint-mf-cdss.qmAdmin.sopTemplates.details.to')} ${newStatus}`,
          variant: 'error',
        });
        return;
      }

      setStatusDialogState({
        isOpen: true,
        status: newStatus,
        comment: '',
      });
    },
    [status, toast]
  );

  // Simplified status dialog handlers
  const closeStatusDialog = () => {
    setStatusDialogState({
      isOpen: false,
      status: '' as Status,
      comment: '',
    });
  };

  const updateDialogComment = (comment: string) => {
    setStatusDialogState((prev) => ({
      ...prev,
      comment,
    }));
  };

  const handleConfirmStatusChange = useCallback(() => {
    if (statusDialogState.status && templateId) {
      changeStatusMutation.mutate({
        id: Number(templateId),
        newStatus: statusDialogState.status as TemplateStatus,
        comment: statusDialogState.comment.trim() || undefined,
      });
    }
  }, [statusDialogState, templateId, changeStatusMutation]);

  // Loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <Loader size={64} />
      </div>
    );
  }

  // Error state
  if (isError || !templateData) {
    return (
      <div className="flex flex-col items-center justify-center h-full">
        <div className="text-red-500 mb-4">
          {t('ctint-mf-cdss.qmAdmin.sopTemplates.details.failedToLoad')}
        </div>
        <Button
          variant="primary"
          onClick={handleBackToList}
        >
          {t('ctint-mf-cdss.qmAdmin.sopTemplates.details.backToList')}
        </Button>
      </div>
    );
  }

  const breadcrumbItems = [
    {
      label: t('ctint-mf-cdss.qmAdmin.sopTemplates.details.sopCategories'),
      link: '/ctint/mf-cdss/admin/qm-admin',
    },
    {
      label: categoryName || '...',
      onClick: handleBackToList,
    },
    {
      label: t('ctint-mf-cdss.qmAdmin.sopTemplates.details.sopTemplate'),
      // onClick: handleBackToList,
    },
  ];

  return (
    <div className="flex flex-col h-full">
      <div className="flex-shrink-0 border-b">
        <EditableBreadcrumb
          items={breadcrumbItems}
          selectedItemName={itemName}
          selectedItemStatus={status}
          onTitleChange={handleTitleChange}
          versionName={versionName}
          onVersionChange={handleVersionChange}
          isEditMode={isEditable}
        />
      </div>

      <div className="flex-1 min-h-0">
        <ErrorBoundary>
          {/* <DetailsContent
            onStatusChange={handleStatusChange}
            onSave={(data) => updateTemplateMutation.mutate(data)}
          /> */}
          <SimpleDetailsContent
            onSave={(data) => updateTemplateMutation.mutate(data)}
            onStatusChange={handleStatusChange}
          />

          {/* Status change dialog */}
          <StatusChangeDialog
            isOpen={statusDialogState.isOpen}
            onClose={closeStatusDialog}
            status={statusDialogState.status}
            commentText={statusDialogState.comment}
            setCommentText={updateDialogComment}
            onConfirm={handleConfirmStatusChange}
            isReturning={statusDialogState.status === 'Returned'}
          />

          {/* <Toaster /> */}
        </ErrorBoundary>
      </div>
    </div>
  );
};

export default SOPTemplateDetails;
