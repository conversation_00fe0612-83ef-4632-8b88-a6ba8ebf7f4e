'use client';

import React from 'react';

interface ClientResponseViewProps {
  clientResponse: string;
}

export const ClientResponseView: React.FC<ClientResponseViewProps> = ({
  clientResponse,
}) => {
  return (
    <div className="p-2 bg-white border rounded-md min-h-[100px]">
      {clientResponse || 'No client response content available'}
    </div>
  );
};

export default ClientResponseView;
