'use client';

import Checkbox from '@cdss-modules/design-system/components/_ui/Checkbox';
import { useState } from 'react';

interface TItemProps {
  label: string;
  value: string;
  id: string;
}

const location = [
  {
    id: 'NT',
    label: 'NT',
    value: 'NT',
  },
  {
    id: 'KW',
    label: 'KW',
    value: 'KW',
  },
  {
    id: 'HK',
    label: 'HK',
    value: 'HK',
  },
];

const CheckboxDemo = () => {
  const [district, setDistrict] = useState<string[]>([]);

  const handleChange = (e: any) => {
    const isSelected = e?.target.checked;
    const value = e?.target.value;

    if (isSelected) {
      setDistrict([...district, value]);
    } else {
      setDistrict((prev) => {
        return prev.filter((pre) => pre !== value);
      });
    }
  };

  return (
    <div className="flex flex-col gap-2">
      {location.map((p: TItemProps) => (
        <Checkbox
          key={p.id}
          id={p.id}
          label={p.label}
          value={p.value}
          checked={district.includes(p.value)}
          onChange={handleChange}
        />
      ))}
      <p>Selected: {district.join(', ')}</p>
    </div>
  );
};

export default CheckboxDemo;
