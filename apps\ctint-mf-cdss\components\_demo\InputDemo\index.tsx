'use client';

import { useRef, useState } from 'react';

import Input from '@cdss-modules/design-system/components/_ui/Input';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';

const DemoInput = () => {
  const inputRef = useRef<HTMLInputElement | null>(null);
  const [message, setMessage] = useState<string | number>('');

  const handleClearValue = () => {
    if (inputRef && inputRef.current) {
      inputRef.current.value = '';
      setMessage('');
    }
  };

  return (
    <div>
      {message}
      <button
        className="border-[1px] p-2"
        onClick={handleClearValue}
      >
        clear
      </button>
      <button
        className="border-[1px] p-2"
        onClick={() => alert(inputRef.current?.value)}
      >
        getValue
      </button>
      <Input
        ref={inputRef}
        isSearch={true}
        beforeIcon={<Icon name="search" />}
        beforeIconFn={() => console.log('click before')}
        onChange={(value) => setMessage(value)}
        allowClear
      />
    </div>
  );
};

export default DemoInput;
