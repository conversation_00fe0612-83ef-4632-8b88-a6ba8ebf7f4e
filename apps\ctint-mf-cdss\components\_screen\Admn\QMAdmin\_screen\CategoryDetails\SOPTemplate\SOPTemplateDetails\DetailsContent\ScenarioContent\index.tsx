'use client';

import { useSOPTemplateStore } from '../../../../../../_store/sopTemplateStore';
import { usePermissions } from '../../../../../../context/PermissionsContext';

import { CriteriaSection } from './CriteriaSection';
import { ClientResponseSection } from './ClientResponseSection';
import { JumpingCriteriaSection } from './JumpingCriteriaSection';
import { ImportantWordsSection } from './ImportantWordsSection';
import { Status } from '../../types';
import { SimpleScriptSectionWrapper } from './ScriptSection/SimpleScriptSectionWrapper';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';

// This will be a placeholder for now until we implement the actual section components
// We'll update this file as we implement each section component

export const ScenarioContent = () => {
  const { t } = useTranslation();
  const {
    isEditing,
    getCurrentStepData,
    getCurrentScenarioData,
    addScenario,
    templateData,
  } = useSOPTemplateStore();
  const { canEdit } = usePermissions();

  const stepData = getCurrentStepData();
  const scenarioData = getCurrentScenarioData();
  const status = templateData?.status || 'Draft';
  const creatorId = templateData?.creatorId
    ? String(templateData.creatorId)
    : undefined;
  const userCanEdit = canEdit(status as Status, creatorId);

  if (stepData && !scenarioData) {
    // Scenario exists but no steps
    return (
      <div className="p-6 bg-gray-50 rounded-md text-center">
        <div className="mb-4">
          <div className="w-12 h-12 mx-auto mb-3 bg-gray-100 rounded-full flex items-center justify-center">
            <svg
              className="w-6 h-6 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 5l7 7-7 7"
              />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {t('ctint-mf-cdss.qmAdmin.sopTemplates.scenarioContent.noStepsYet')}
          </h3>
          <p className="text-gray-600 mb-4">
            {t(
              'ctint-mf-cdss.qmAdmin.sopTemplates.scenarioContent.noStepsDescription'
            )}
          </p>
        </div>

        {isEditing && userCanEdit ? (
          <button
            type="button"
            onClick={() => addScenario(stepData.id)}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
          >
            <svg
              className="w-4 h-4 mr-2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 6v6m0 0v6m0-6h6m-6 0H6"
              />
            </svg>
            {t(
              'ctint-mf-cdss.qmAdmin.sopTemplates.scenarioContent.addFirstScenario'
            )}
          </button>
        ) : (
          <div className="text-sm text-gray-500">
            {t(
              'ctint-mf-cdss.qmAdmin.sopTemplates.scenarioContent.enterEditModeScenarios'
            )}
          </div>
        )}
      </div>
    );
  }

  if (!stepData) {
    return (
      <div className="h-full flex items-center justify-center">
        <p className="text-gray-500">
          {t(
            'ctint-mf-cdss.qmAdmin.sopTemplates.scenarioContent.pleaseSelectStep'
          )}
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6 h-fit">
      {/* <ScriptSection /> */}
      <SimpleScriptSectionWrapper />
      {/* <CriteriaSection />
      <ClientResponseSection />
      <JumpingCriteriaSection />
      <ImportantWordsSection /> */}
    </div>
  );
};

export default ScenarioContent;
