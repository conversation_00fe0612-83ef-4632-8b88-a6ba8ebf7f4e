import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from '@cdss-modules/design-system';
import { useRouteHandler } from '@cdss-modules/design-system';

// Import SOP template specific fire functions
import {
  fireGetSOPTemplateList,
  fireCreateSOPTemplate,
  fireUpdateSOPTemplate,
  fireDeleteSOPTemplate,
  fireGetSOPTemplateDetail,
  fireGetSOPTemplateFormDetail,
  fireUpdateStandardScriptDetail,
} from '@cdss/lib/api';

// ======================================
// SOP TEMPLATE TYPES AND INTERFACES
// ======================================

// SOP Template item interface based on the API response structure
export interface SOPTemplateFormItem {
  formId: string;
  name: string;
  category: string;
  status: string;
  formVersion: string;
  product: string;
  createTime: string;
  createUser: string;
  updateTime: string;
  language: string;
}

// SOP Template list request interface
export interface SOPTemplateListRequest {
  category: string; // Category ID
  order: 'ASC' | 'DESC';
  orderBy: string;
  page: number;
  pageSize: number;
  name?: string; // Optional search parameter
  status?: string; // Optional status filter
}

// SOP Template list response interface
export interface SOPTemplateListResponse {
  data: {
    totalCount: number;
    totalPage: number;
    items: SOPTemplateFormItem[];
  };
  error: string;
  isSuccess: boolean;
}

// SOP Template create request interface
export interface SOPTemplateCreateRequest {
  name: string;
  category: string;
  status?: string;
  formVersion?: string;
  product?: string;
}

// SOP Template create response interface
export interface SOPTemplateCreateResponse {
  data: SOPTemplateFormItem;
  error: string;
  isSuccess: boolean;
}

// SOP Template by ID response interface
export interface SOPTemplateByIdResponse {
  data: SOPTemplateFormItem;
  error: string;
  isSuccess: boolean;
}

// SOP Template update request interface
export interface SOPTemplateUpdateRequest {
  name?: string;
  formVersion?: string;
  status?: string;
  product?: string;
}

// SOP Template update response interface
export interface SOPTemplateUpdateResponse {
  data: SOPTemplateFormItem;
  error: string;
  isSuccess: boolean;
}

// SOP Template delete response interface
export interface SOPTemplateDeleteResponse {
  data: null;
  error: string;
  isSuccess: boolean;
}

// Standard Script Detail Update interfaces
export interface StandardScriptUpdateItem {
  standardScriptId: string;
  similarityRequired: number;
}

export type StandardScriptDetailUpdateRequest = Array<StandardScriptUpdateItem>;

export interface StandardScriptDetailUpdateResponse {
  data: null;
  error: string;
  isSuccess: boolean;
}

// Standard Script Rule interface
export interface StandardScriptRule {
  ruleId: string;
  referenceType: string;
  referenceId: string;
  ruleType: string;
  ruleContent: string;
  enableFlag: boolean;
  createTime: string;
  updateTime: string | null;
  createBy: string;
  updateBy: string;
  platform: string;
  tenant: string;
}

// Standard Script (Scenario) interface based on the API response
export interface StandardScript {
  standardScriptId: string;
  formId: string;
  stepId: number; // This becomes "Step" in UI (what was previously "Stage")
  scenarioId: string; // This becomes "Scenario" in UI (what was previously "Step")
  scriptOrder: number;
  content: string; // This becomes "Script" in UI
  helpContent: string;
  participant: string;
  doSimilarity: boolean;
  doExtraction: boolean;
  createTime: string;
  updateTime: string | null;
  createBy: string;
  updateBy: string;
  platform: string;
  tenant: string;
  enable: string;
  similarityRequired: number;
  standardScriptRules: StandardScriptRule[];
  useDictionary: boolean;
  doMetadataRef: boolean;
  contentEng: string;
  contentMandarin: string;
}

// SOP Template detailed response interface
export interface SOPTemplateDetailResponse {
  data: StandardScript[];
  error: string;
  isSuccess: boolean;
}

// Transformed data structures for UI
export interface ScenarioData {
  id: string; // scenarioId
  standardScriptId: string;
  name: string; // derived from scenarioId
  script: string; // content (Cantonese)
  scriptEng: string; // contentEng (English)
  scriptMandarin: string; // contentMandarin (Putonghua)
  helpContent: string;
  participant: string;
  doSimilarity: boolean;
  doExtraction: boolean;
  similarityRequired: number;
  useDictionary: boolean;
  doMetadataRef: boolean;
  standardScriptRules: StandardScriptRule[];
  scriptOrder: number;
  createTime: string;
  updateTime: string | null;
  createBy: string;
  updateBy: string;
}

export interface StepData {
  id: number; // stepId
  name: string; // derived from stepId
  description?: string;
  scenarios: ScenarioData[]; // what was previously "steps"
}

export interface SOPTemplateDetailedData {
  id: string; // formId
  name: string;
  status: string;
  version: string;
  // createdBy: string;
  createUser: string;
  approvedBy: string;
  createdDate: string;
  lastUpdated: string;
  categoryId: string;
  creatorId: string;
  description?: string;
  steps: StepData[]; // what was previously "stages"
}

// ======================================
// SOP TEMPLATE API FUNCTIONS
// ======================================

// Fetch SOP templates list
export const fetchSOPTemplateList = async (
  request: SOPTemplateListRequest,
  basePath = ''
): Promise<SOPTemplateListResponse> => {
  try {
    const requestBody = {
      category: request.category,
      order: request.order,
      orderBy: request.orderBy,
      page: request.page,
      pageSize: request.pageSize,
      ...(request.name && { name: request.name }),
      ...(request.status &&
        request.status !== 'Status' && { status: request.status }),
    };

    // console.log('SOP Template List API request:', {
    //   body: requestBody,
    // });

    const response = await fireGetSOPTemplateList(requestBody, basePath);
    const result: SOPTemplateListResponse = response.data;

    // console.log('SOP Template List API response:', result);

    if (!result.isSuccess) {
      throw new Error(result.error || 'API request failed');
    }

    // console.log('Processed SOP Template List API result:', {
    //   dataLength: result.data.items?.length || 0,
    //   totalItems: result.data.totalCount || 0,
    //   totalPages: result.data.totalPage || 0,
    //   requestedPage: request.page,
    //   requestedPageSize: request.pageSize,
    // });

    return result;
  } catch (error) {
    console.error('Error fetching SOP template list:', error);
    throw error;
  }
};

// Create SOP template
export const createSOPTemplate = async (
  request: SOPTemplateCreateRequest,
  basePath = ''
): Promise<SOPTemplateCreateResponse> => {
  try {
    const requestBody = {
      name: request.name,
      category: request.category,
      ...(request.status && { status: request.status }),
      ...(request.formVersion && { formVersion: request.formVersion }),
      ...(request.product && { product: request.product }),
    };

    console.log('SOP Template Create API request:', {
      body: requestBody,
    });

    const response = await fireCreateSOPTemplate(requestBody, basePath);
    const result: SOPTemplateCreateResponse = response.data;

    console.log('SOP Template Create API response:', result);

    if (!result.isSuccess) {
      throw new Error(result.error || 'API request failed');
    }

    console.log('Processed SOP template create API result:', {
      createdId: result.data?.formId || 'unknown',
    });

    return result;
  } catch (error) {
    console.error('Error creating SOP template:', error);
    throw error;
  }
};

// Update SOP template
export const updateSOPTemplate = async (
  id: string,
  request: SOPTemplateUpdateRequest,
  basePath = ''
): Promise<SOPTemplateUpdateResponse> => {
  try {
    const requestBody = {
      ...(request.name && { name: request.name }),
      ...(request.formVersion && { formVersion: request.formVersion }),
      ...(request.status && { status: request.status }),
      ...(request.product && { product: request.product }),
    };

    console.log('SOP Template Update API request:', {
      body: requestBody,
    });

    const response = await fireUpdateSOPTemplate(id, requestBody, basePath);
    const result: SOPTemplateUpdateResponse = response.data;

    console.log('SOP Template Update API response:', result);

    if (!result.isSuccess) {
      throw new Error(result.error || 'API request failed');
    }

    console.log('Processed SOP template update API result:', {
      updatedId: result.data?.formId || 'unknown',
      updatedName: result.data?.name || 'unknown',
    });

    return result;
  } catch (error) {
    console.error('Error updating SOP template:', error);
    throw error;
  }
};

// Delete SOP template
export const deleteSOPTemplate = async (
  id: string,
  basePath = ''
): Promise<SOPTemplateDeleteResponse> => {
  try {
    console.log('SOP Template Delete API request:', {
      id: id,
    });

    const response = await fireDeleteSOPTemplate(id, basePath);
    const result: SOPTemplateDeleteResponse = response.data;

    console.log('SOP Template Delete API response:', result);

    if (!result.isSuccess) {
      throw new Error(result.error || 'API request failed');
    }

    console.log('Processed SOP template delete API result:', {
      deletedId: id,
    });

    return result;
  } catch (error) {
    console.error('Error deleting SOP template:', error);
    throw error;
  }
};

// Fetch detailed SOP template data with standard scripts
export const fetchSOPTemplateDetail = async (
  formId: string,
  basePath = ''
): Promise<SOPTemplateDetailResponse> => {
  try {
    console.log('SOP Template Detail API request:', {
      formId: formId,
    });

    const response = await fireGetSOPTemplateDetail(formId, basePath);
    const result: SOPTemplateDetailResponse = response.data;

    if (!result.isSuccess) {
      throw new Error(result.error || 'API request failed');
    }

    console.log('SOP Template Detail API response:', result.data);

    console.log('Processed SOP template detail API result:', {
      formId: formId,
      standardScriptsCount: result.data?.length || 0,
      stepsCount:
        [...new Set(result.data?.map((item) => item.stepId))].length || 0,
    });

    return result;
  } catch (error) {
    console.error('Error fetching SOP template detail:', error);
    throw error;
  }
};

// Fetch SOP template form detail by ID
export const fetchSOPTemplateFormDetail = async (
  formId: string,
  basePath = ''
): Promise<SOPTemplateByIdResponse> => {
  try {
    console.log('SOP Template Form Detail API request:', {
      formId: formId,
    });

    const response = await fireGetSOPTemplateFormDetail(formId, basePath);
    const result: SOPTemplateByIdResponse = response.data;

    console.log('SOP Template Form Detail API response:', result);

    if (!result.isSuccess) {
      throw new Error(result.error || 'API request failed');
    }

    console.log('Processed SOP template form detail result:', {
      formId: result.data?.formId || 'unknown',
      name: result.data?.name || 'unknown',
    });

    return result;
  } catch (error) {
    console.error('Error fetching SOP template form detail:', error);
    throw error;
  }
};

// Update standard script detail (similarity)
export const updateStandardScriptDetail = async (
  request: StandardScriptDetailUpdateRequest,
  basePath = ''
): Promise<StandardScriptDetailUpdateResponse> => {
  try {
    console.log('Standard Script Detail Update API request:', {
      body: request,
    });

    const response = await fireUpdateStandardScriptDetail(request, basePath);
    const result: StandardScriptDetailUpdateResponse = response.data;

    console.log('Standard Script Detail Update API response:', result);

    if (!result.isSuccess) {
      throw new Error(result.error || 'API request failed');
    }

    console.log('Processed standard script detail update API result:', {
      updatedCount: request.length,
    });

    return result;
  } catch (error) {
    console.error('Error updating standard script detail:', error);
    throw error;
  }
};

// Transform API data to UI structure
export const transformSOPTemplateDetailData = (
  apiData: StandardScript[],
  templateInfo: SOPTemplateFormItem
): SOPTemplateDetailedData => {
  // Group by stepId
  const stepGroups = apiData.reduce(
    (acc, script) => {
      if (!acc[script.stepId]) {
        acc[script.stepId] = [];
      }
      acc[script.stepId].push(script);
      return acc;
    },
    {} as Record<number, StandardScript[]>
  );

  // Transform to UI structure
  const steps: StepData[] = Object.entries(stepGroups)
    .map(([stepId, scripts]) => {
      // Sort scenarios by scriptOrder
      const sortedScripts = scripts.sort(
        (a, b) => a.scriptOrder - b.scriptOrder
      );

      const scenarios: ScenarioData[] = sortedScripts.map((script) => ({
        id: script.scenarioId,
        standardScriptId: script.standardScriptId,
        name: script.scenarioId, // Just use the ID, translation will be handled in UI
        script: script.content,
        scriptEng: script.contentEng,
        scriptMandarin: script.contentMandarin,
        helpContent: script.helpContent,
        participant: script.participant,
        doSimilarity: script.doSimilarity,
        doExtraction: script.doExtraction,
        similarityRequired: script.similarityRequired,
        useDictionary: script.useDictionary,
        doMetadataRef: script.doMetadataRef,
        standardScriptRules: script.standardScriptRules,
        scriptOrder: script.scriptOrder,
        createTime: script.createTime,
        updateTime: script.updateTime,
        createBy: script.createBy,
        updateBy: script.updateBy,
      }));

      return {
        id: parseInt(stepId),
        name: stepId, // Just use the ID, translation will be handled in UI
        scenarios: scenarios,
      };
    })
    .sort((a, b) => a.id - b.id);

  return {
    id: templateInfo.formId,
    name: templateInfo.name,
    status: templateInfo.status,
    version: templateInfo.formVersion,
    createUser: templateInfo.createUser,
    approvedBy: '', // Not available in current API
    createdDate: templateInfo.createTime,
    lastUpdated: templateInfo.updateTime,
    categoryId: templateInfo.category,
    creatorId: templateInfo.createUser,
    steps: steps,
  };
};

// ======================================
// SOP TEMPLATE REACT QUERY HOOKS
// ======================================

// React Query hook for SOP templates list
export const useSOPTemplateListQuery = (
  categoryId: string,
  search = '',
  statusFilter = '',
  page = 1,
  pageSize = 10,
  orderBy = 'createTime',
  order: 'ASC' | 'DESC' = 'DESC'
) => {
  const { basePath } = useRouteHandler();

  return useQuery({
    queryKey: [
      'sopTemplateList',
      categoryId,
      search,
      statusFilter,
      page,
      pageSize,
      orderBy,
      order,
    ],
    queryFn: async () => {
      const request: SOPTemplateListRequest = {
        category: categoryId,
        order,
        orderBy,
        page,
        pageSize,
        ...(search && { name: search }),
        ...(statusFilter && { status: statusFilter }),
      };

      return await fetchSOPTemplateList(request, basePath);
    },
    retry: 3,
    retryDelay: 1000,
    enabled: !!categoryId, // Only run if categoryId is provided
  });
};

// React Query hook for creating SOP templates
export const useCreateSOPTemplateMutation = () => {
  const queryClient = useQueryClient();
  const { basePath } = useRouteHandler();

  return useMutation({
    mutationFn: (request: SOPTemplateCreateRequest) =>
      createSOPTemplate(request, basePath),
    onSuccess: (data, variables) => {
      // Invalidate and refetch the SOP template list for this category
      queryClient.invalidateQueries({
        queryKey: ['sopTemplateList', variables.category],
      });

      // Show success toast
      toast({
        variant: 'success',
        title: 'Success',
        description: 'SOP template created successfully',
      });
    },
    onError: (error: Error) => {
      console.error('Failed to create SOP template:', error);

      // Show error toast notification
      toast({
        variant: 'error',
        title: 'Error',
        description: `Failed to create SOP template: ${error.message}`,
      });
    },
  });
};

// React Query hook for updating SOP template
export const useUpdateSOPTemplateMutation = () => {
  const queryClient = useQueryClient();
  const { basePath } = useRouteHandler();

  return useMutation({
    mutationFn: ({
      id,
      request,
    }: {
      id: string;
      request: SOPTemplateUpdateRequest;
    }) => updateSOPTemplate(id, request, basePath),
    onSuccess: (data, variables) => {
      // Invalidate and refetch relevant queries
      queryClient.invalidateQueries({
        queryKey: ['sopTemplateById', variables.id],
      });
      queryClient.invalidateQueries({
        queryKey: ['sopTemplateList'],
      });

      // Show success toast
      const updatedFields = [];
      if (variables.request.name) updatedFields.push('name');
      if (variables.request.formVersion) updatedFields.push('version');
      if (variables.request.status) updatedFields.push('status');

      toast({
        variant: 'success',
        title: 'Success',
        description: `SOP template ${updatedFields.join(' and ')} updated successfully`,
      });
    },
    onError: (error: Error, variables) => {
      console.error(
        `Failed to update SOP template with ID ${variables.id}:`,
        error
      );

      // Show error toast notification
      toast({
        variant: 'error',
        title: 'Error',
        description: `Failed to update SOP template: ${error.message}`,
      });
    },
  });
};

// React Query hook for deleting SOP template
export const useDeleteSOPTemplateMutation = () => {
  const queryClient = useQueryClient();
  const { basePath } = useRouteHandler();

  return useMutation({
    mutationFn: (id: string) => deleteSOPTemplate(id, basePath),
    onSuccess: (data, variables) => {
      // Invalidate and refetch relevant queries
      queryClient.invalidateQueries({
        queryKey: ['sopTemplateById', variables],
      });
      queryClient.invalidateQueries({
        queryKey: ['sopTemplateList'],
      });

      // Show success toast
      toast({
        variant: 'success',
        title: 'Success',
        description: 'SOP template deleted successfully',
      });
    },
    onError: (error: Error, variables) => {
      console.error(
        `Failed to delete SOP template with ID ${variables}:`,
        error
      );

      // Show error toast notification
      toast({
        variant: 'error',
        title: 'Error',
        description: `Failed to delete SOP template: ${error.message}`,
      });
    },
  });
};

// React Query hook for fetching detailed SOP template data with transformation
export const useSOPTemplateFullDetailQuery = (
  formId: string,
  enabled = true
) => {
  const { basePath } = useRouteHandler();

  return useQuery({
    queryKey: ['sopTemplateFullDetail', formId],
    queryFn: async () => {
      // Get the form detail first
      const formDetailResponse = await fetchSOPTemplateFormDetail(
        formId,
        basePath
      );

      if (!formDetailResponse.isSuccess || !formDetailResponse.data) {
        throw new Error(formDetailResponse.error || 'No form data found');
      }

      // Get the detailed script data
      const templateDetailResponse = await fetchSOPTemplateDetail(
        formId,
        basePath
      );

      if (
        !templateDetailResponse.isSuccess ||
        !templateDetailResponse.data ||
        templateDetailResponse.data.length === 0
      ) {
        throw new Error(
          templateDetailResponse.error || 'No template data found'
        );
      }

      // Use the actual form detail response instead of mock data
      const templateInfo: SOPTemplateFormItem = formDetailResponse.data;

      // Transform the data to UI structure
      const transformedData = transformSOPTemplateDetailData(
        templateDetailResponse.data,
        templateInfo
      );

      console.log('SOP Template Full Detail API response:', transformedData);

      return {
        data: transformedData,
        error: '',
        isSuccess: true,
      };
    },
    enabled: !!formId && enabled,
    retry: 3,
    retryDelay: 1000,
  });
};

// React Query hook for updating standard script details (similarity)
export const useUpdateStandardScriptDetailMutation = () => {
  const queryClient = useQueryClient();
  const { basePath } = useRouteHandler();

  return useMutation({
    mutationFn: (request: StandardScriptDetailUpdateRequest) =>
      updateStandardScriptDetail(request, basePath),
    onSuccess: (data, variables) => {
      // Invalidate relevant queries to refresh the data
      queryClient.invalidateQueries({
        queryKey: ['sopTemplateFullDetail'],
      });

      // Show success toast
      toast({
        variant: 'success',
        title: 'Success',
        description: `Updated ${variables.length} script detail${variables.length > 1 ? 's' : ''} successfully`,
      });
    },
    onError: (error: Error) => {
      console.error('Failed to update standard script details:', error);

      // Show error toast notification
      toast({
        variant: 'error',
        title: 'Error',
        description: `Failed to update script details: ${error.message}`,
      });
    },
  });
};
