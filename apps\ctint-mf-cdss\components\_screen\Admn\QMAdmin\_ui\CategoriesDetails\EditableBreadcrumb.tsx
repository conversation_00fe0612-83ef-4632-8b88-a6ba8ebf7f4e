import React, { useEffect, useState } from 'react';
import { Pencil, Check } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';
import Input from '@cdss-modules/design-system/components/_ui/Input';
import { Button } from '@cdss-modules/design-system';
import {
  Popup,
  PopupContent,
  PopupTrigger,
} from '@cdss-modules/design-system/components/_ui/Popup';
import StatusBadge, { STATUS_STYLES } from '../StatusBadge';
import { useUnifiedUpdateDefinitionFieldsMutation } from '../../_screen/unified-queries';

interface EditableBreadcrumbProps {
  items: {
    label: string;
    link?: string;
    onClick?: () => void;
  }[];
  selectedItemName: string;
  selectedItemStatus?: string;
  selectedItemId?: string;
  onTitleChange?: (newTitle: string) => void;
  versionName?: string;
  onVersionChange?: (newVersion: string) => void;
  isEditMode?: boolean;
}

// Confirmation popup component for name changes
const NameChangeConfirmationPopup: React.FC<{
  onConfirm: () => void;
  onCancel: () => void;
  children: React.ReactNode;
  oldName: string;
  newName: string;
}> = ({ onConfirm, onCancel, children, oldName, newName }) => {
  const [open, setOpen] = useState(false);
  const { t } = useTranslation();

  const handleConfirm = () => {
    onConfirm();
    setOpen(false);
  };

  const handleCancel = () => {
    onCancel();
    setOpen(false);
  };

  return (
    <Popup
      open={open}
      onOpenChange={setOpen}
    >
      <PopupTrigger asChild>{children}</PopupTrigger>
      <PopupContent
        title={t(
          'ctint-mf-cdss.qmAdmin.actionButtons.confirmNameChange',
          'Confirm Name Change'
        )}
        className="max-w-md"
      >
        <div className="p-6">
          <p className="text-gray-600 mb-4">
            {t(
              'ctint-mf-cdss.qmAdmin.actionButtons.confirmNameChangeMessage',
              'Are you sure you want to change the name?'
            )}
          </p>
          <div className="bg-gray-50 p-3 rounded-md mb-6">
            <div className="text-sm">
              <div className="mb-2">
                <span className="font-medium">
                  {t('ctint-mf-cdss.qmAdmin.actionButtons.from', 'From')}:
                </span>{' '}
                {oldName}
              </div>
              <div>
                <span className="font-medium">
                  {t('ctint-mf-cdss.qmAdmin.actionButtons.to', 'To')}:
                </span>{' '}
                {newName}
              </div>
            </div>
          </div>
          <div className="flex justify-end gap-3">
            <Button
              variant="secondary"
              onClick={handleCancel}
            >
              {t('ctint-mf-cdss.qmAdmin.actionButtons.cancel', 'Cancel')}
            </Button>
            <Button onClick={handleConfirm}>
              {t(
                'ctint-mf-cdss.qmAdmin.actionButtons.confirmChange',
                'Confirm Change'
              )}
            </Button>
          </div>
        </div>
      </PopupContent>
    </Popup>
  );
};

// Confirmation popup component for version changes
const VersionChangeConfirmationPopup: React.FC<{
  onConfirm: () => void;
  onCancel: () => void;
  children: React.ReactNode;
  oldVersion: string;
  newVersion: string;
}> = ({ onConfirm, onCancel, children, oldVersion, newVersion }) => {
  const [open, setOpen] = useState(false);
  const { t } = useTranslation();

  const handleConfirm = () => {
    onConfirm();
    setOpen(false);
  };

  const handleCancel = () => {
    onCancel();
    setOpen(false);
  };

  return (
    <Popup
      open={open}
      onOpenChange={setOpen}
    >
      <PopupTrigger asChild>{children}</PopupTrigger>
      <PopupContent
        title={t(
          'ctint-mf-cdss.qmAdmin.actionButtons.confirmVersionChange',
          'Confirm Version Change'
        )}
        className="max-w-md"
      >
        <div className="p-6">
          <p className="text-gray-600 mb-4">
            {t(
              'ctint-mf-cdss.qmAdmin.actionButtons.confirmVersionChangeMessage',
              'Are you sure you want to change the version?'
            )}
          </p>
          <div className="bg-gray-50 p-3 rounded-md mb-6">
            <div className="text-sm">
              <div className="mb-2">
                <span className="font-medium">
                  {t('ctint-mf-cdss.qmAdmin.actionButtons.from', 'From')}:
                </span>{' '}
                {oldVersion}
              </div>
              <div>
                <span className="font-medium">
                  {t('ctint-mf-cdss.qmAdmin.actionButtons.to', 'To')}:
                </span>{' '}
                {newVersion}
              </div>
            </div>
          </div>
          <div className="flex justify-end gap-3">
            <Button
              variant="secondary"
              onClick={handleCancel}
            >
              {t('ctint-mf-cdss.qmAdmin.actionButtons.cancel', 'Cancel')}
            </Button>
            <Button onClick={handleConfirm}>
              {t(
                'ctint-mf-cdss.qmAdmin.actionButtons.confirmChange',
                'Confirm Change'
              )}
            </Button>
          </div>
        </div>
      </PopupContent>
    </Popup>
  );
};

export const EditableBreadcrumb = ({
  items,
  selectedItemName,
  selectedItemStatus,
  selectedItemId,
  onTitleChange,
  versionName,
  onVersionChange,
  isEditMode = false,
}: EditableBreadcrumbProps) => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [isEditing, setIsEditing] = useState(false);
  const [editedTitle, setEditedTitle] = useState(selectedItemName);
  const [isEditingVersion, setIsEditingVersion] = useState(false);
  const [editedVersion, setEditedVersion] = useState(versionName || '');

  // Initialize the update mutation hook
  const updateFieldsMutation = useUnifiedUpdateDefinitionFieldsMutation();

  const style = STATUS_STYLES[selectedItemStatus || ''];

  // Check if editing is allowed (only for Draft status)
  const canEditFields = selectedItemStatus === 'Draft';

  // Update local state when props change
  useEffect(() => {
    setEditedTitle(selectedItemName);
  }, [selectedItemName]);

  useEffect(() => {
    setEditedVersion(versionName || '');
  }, [versionName]);

  // Reset editing states when global edit mode changes
  useEffect(() => {
    if (!isEditMode) {
      setIsEditing(false);
      setIsEditingVersion(false);
    }
  }, [isEditMode]);

  const handleEditClick = () => {
    if (isEditMode && canEditFields) {
      setIsEditing(true);
    }
  };

  const handleSaveClick = async () => {
    if (
      !selectedItemId ||
      !editedTitle.trim() ||
      editedTitle === selectedItemName
    ) {
      setIsEditing(false);
      return;
    }

    try {
      // Call API to update the name
      await updateFieldsMutation.mutateAsync({
        id: selectedItemId,
        request: {
          name: editedTitle.trim(),
        },
      });

      // Call the parent's callback if provided
      if (onTitleChange) {
        onTitleChange(editedTitle.trim());
      }

      setIsEditing(false);
    } catch (error) {
      console.error('Failed to update name:', error);
      // Reset to original value on error
      setEditedTitle(selectedItemName);
      setIsEditing(false);
    }
  };

  const handleCancelNameEdit = () => {
    setEditedTitle(selectedItemName);
    setIsEditing(false);
  };

  const handleVersionEditClick = () => {
    if (isEditMode && canEditFields) {
      setIsEditingVersion(true);
    }
  };

  const handleVersionSaveClick = async () => {
    if (
      !selectedItemId ||
      !editedVersion.trim() ||
      editedVersion === versionName
    ) {
      setIsEditingVersion(false);
      return;
    }

    try {
      // Call API to update the version
      await updateFieldsMutation.mutateAsync({
        id: selectedItemId,
        request: {
          version: editedVersion.trim(),
        },
      });

      // Call the parent's callback if provided
      if (onVersionChange) {
        onVersionChange(editedVersion.trim());
      }

      setIsEditingVersion(false);
    } catch (error) {
      console.error('Failed to update version:', error);
      // Reset to original value on error
      setEditedVersion(versionName || '');
      setIsEditingVersion(false);
    }
  };

  const handleCancelVersionEdit = () => {
    setEditedVersion(versionName || '');
    setIsEditingVersion(false);
  };

  return (
    <div className="flex items-center p-6 pb-2">
      <div className="flex items-center">
        {items.map((item, index) => (
          <React.Fragment key={index}>
            {item.link ? (
              <span
                onClick={() => navigate(item.link!)}
                className="hover:underline cursor-pointer"
              >
                {item.label}
              </span>
            ) : item.onClick ? (
              <span
                onClick={item.onClick}
                className="hover:underline cursor-pointer"
              >
                {item.label}
              </span>
            ) : (
              <span>{item.label}</span>
            )}
            {index < items.length - 1 && <span className="mx-1">/</span>}
          </React.Fragment>
        ))}
        {items.length > 0 && <span className="mx-1">-</span>}
      </div>

      <div className="flex items-center gap-2">
        <div className="flex items-center gap-1">
          {isEditing ? (
            <>
              <Input
                value={editedTitle}
                onChange={(value) => setEditedTitle(value as string)}
                className="w-44"
                size="s"
                autoFocus
              />
              <NameChangeConfirmationPopup
                onConfirm={handleSaveClick}
                onCancel={handleCancelNameEdit}
                oldName={selectedItemName}
                newName={editedTitle}
              >
                <button
                  className="p-1 rounded-full hover:bg-gray-100"
                  aria-label={t(
                    'ctint-mf-cdss.qmAdmin.actionButtons.saveTitle',
                    'Save title'
                  )}
                  disabled={
                    !editedTitle.trim() || editedTitle === selectedItemName
                  }
                >
                  <Check
                    size={16}
                    className={
                      editedTitle.trim() && editedTitle !== selectedItemName
                        ? 'text-green-600'
                        : 'text-gray-400'
                    }
                  />
                </button>
              </NameChangeConfirmationPopup>
            </>
          ) : !selectedItemName ? (
            <span className="inline-block h-5 w-10 bg-gray-200 animate-pulse rounded"></span>
          ) : (
            <>
              <span>{selectedItemName}</span>
              {/* {isEditMode && canEditFields && (
                <button
                  onClick={handleEditClick}
                  className="p-1 rounded-full hover:bg-gray-100"
                  aria-label={t(
                    'ctint-mf-cdss.qmAdmin.actionButtons.editTitle',
                    'Edit title'
                  )}
                  title={
                    !canEditFields
                      ? t(
                          'ctint-mf-cdss.qmAdmin.actionButtons.editOnlyDraft',
                          'Can only edit when status is Draft'
                        )
                      : t(
                          'ctint-mf-cdss.qmAdmin.actionButtons.editTitle',
                          'Edit title'
                        )
                  }
                >
                  <Pencil
                    size={16}
                    className={
                      canEditFields ? 'text-gray-600' : 'text-gray-400'
                    }
                  />
                </button>
              )} */}
            </>
          )}
        </div>

        {/* {selectedItemStatus ? (
          <StatusBadge
            status={selectedItemStatus}
            t={t}
          />
        ) : (
          <span className="inline-block h-6 w-10 bg-gray-200 animate-pulse rounded-md"></span>
        )} */}

        {/* <div className="flex items-center gap-1">
          {isEditingVersion ? (
            <>
              <Input
                value={editedVersion}
                onChange={(value) => setEditedVersion(value as string)}
                className="w-32"
                size="s"
                placeholder={t(
                  'ctint-mf-cdss.qmAdmin.actionButtons.versionPlaceholder',
                  'Version Name'
                )}
                autoFocus
              />
              <VersionChangeConfirmationPopup
                onConfirm={handleVersionSaveClick}
                onCancel={handleCancelVersionEdit}
                oldVersion={versionName || ''}
                newVersion={editedVersion}
              >
                <button
                  className="p-1 rounded-full hover:bg-gray-100"
                  aria-label={t(
                    'ctint-mf-cdss.qmAdmin.actionButtons.saveVersion',
                    'Save version'
                  )}
                  disabled={
                    !editedVersion.trim() || editedVersion === versionName
                  }
                >
                  <Check
                    size={16}
                    className={
                      editedVersion.trim() && editedVersion !== versionName
                        ? 'text-green-600'
                        : 'text-gray-400'
                    }
                  />
                </button>
              </VersionChangeConfirmationPopup>
            </>
          ) : !versionName ? (
            <span className="inline-block h-4 w-4 bg-gray-200 animate-pulse rounded"></span>
          ) : (
            <>
              <span className="text-gray-500">{versionName}</span>
              {isEditMode && canEditFields && (
                <button
                  onClick={handleVersionEditClick}
                  className="p-1 rounded-full hover:bg-gray-100"
                  aria-label={t(
                    'ctint-mf-cdss.qmAdmin.actionButtons.editVersion',
                    'Edit version'
                  )}
                  title={
                    !canEditFields
                      ? t(
                          'ctint-mf-cdss.qmAdmin.actionButtons.editOnlyDraft',
                          'Can only edit when status is Draft'
                        )
                      : t(
                          'ctint-mf-cdss.qmAdmin.actionButtons.editVersion',
                          'Edit version'
                        )
                  }
                >
                  <Pencil
                    size={16}
                    className={
                      canEditFields ? 'text-gray-600' : 'text-gray-400'
                    }
                  />
                </button>
              )}
            </>
          )}
        </div> */}
      </div>
    </div>
  );
};
