// Re-export from the new separate files for backward compatibility
export type { RelationshipType } from './definition-queries';
export type { ContentMappingType } from './content-mapping-queries';

// Re-export definition queries
export {
  useUnifiedDefinitionListQuery,
  useUnifiedCreateDefinitionMutation,
  useUnifiedDefinitionByIdQuery,
  useUnifiedUpdateDefinitionStatusMutation,
  useUnifiedUpdateDefinitionFieldsMutation,
  useUnifiedDeleteDefinitionMutation,
  // Type-specific hooks
  useCategoriesUnifiedQuery,
  useSOPTemplatesUnifiedQuery,
  useMetadataUnifiedQuery,
  useDictionaryUnifiedQuery,
  useSTTKeywordsUnifiedQuery,
  useSensitiveKeywordsUnifiedQuery,
  // Create hooks
} from './definition-queries';

// Re-export content mapping queries
export {
  useContentMappingListQuery,
  useContentMappingCreateMutation,
  useContentMappingUpdateMutation,
  useContentMappingDeleteMutation,
  useContentMappingMultiCreateMutation,
} from './content-mapping-queries';

// Re-export types and interfaces for backward compatibility
export type {
  UnifiedDefinitionItem,
  UnifiedListingRequest,
  UnifiedListingResponse,
  UnifiedDefinitionByIdResponse,
  UnifiedCreateRequest,
  UnifiedCreateResponse,
  UnifiedUpdateStatusRequest,
  UnifiedUpdateStatusResponse,
  UnifiedUpdateFieldsRequest,
  UnifiedUpdateFieldsResponse,
} from './definition-queries';

export type {
  UnifiedMappingItem,
  UnifiedMappingRequest,
  UnifiedMappingResponse,
  UnifiedMappingCreateRequest,
  UnifiedMappingCreateResponse,
  UnifiedMappingMultiCreateRequest,
  UnifiedMappingMultiCreateResponse,
  UnifiedMappingUpdateRequest,
  UnifiedMappingUpdateResponse,
  UnifiedMappingDeleteResponse,
} from './content-mapping-queries';
