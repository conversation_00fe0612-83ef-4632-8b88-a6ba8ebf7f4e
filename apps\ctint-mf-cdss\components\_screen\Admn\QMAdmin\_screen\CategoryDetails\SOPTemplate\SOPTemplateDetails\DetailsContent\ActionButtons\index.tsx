'use client';

import { Button } from '@cdss-modules/design-system';
import { ActionButtonsProps, Status } from '../../types';
import { useSOPTemplateStore } from '../../../../../../_store/sopTemplateStore';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';

import { useState } from 'react';
import { usePermissions } from '../../../../../../context/PermissionsContext';

interface ActionButtonsExtendedProps {
  className?: string;
  onStatusChange?: (status: Status) => void;
  onSave?: (data: any) => void;
}

export const ActionButtons = ({
  className = '',
  onStatusChange,
  onSave,
}: ActionButtonsExtendedProps) => {
  const { t } = useTranslation();
  const {
    templateData,
    isLoading,
    isEditing,
    enterEditMode,
    exitEditMode,
    cancelEdit,
    saveAndExitEditMode,
    submitForApprovalAction,
    approveTemplate,
    returnTemplate,
  } = useSOPTemplateStore();

  // Use permissions context directly instead of store
  const { canEdit, canApprove } = usePermissions();

  const [isSaving, setIsSaving] = useState(false);

  // Cannot perform any actions while loading
  if (isLoading) {
    return null;
  }

  // If no template data is loaded, don't show any buttons
  if (!templateData) {
    return null;
  }

  const status = templateData.status as Status; // Properly cast to Status type
  const creatorId = templateData.creatorId
    ? String(templateData.creatorId)
    : undefined;
  const isDraft = status === 'Draft';
  const isPendingApproval = status === 'Pending Approval';
  const isReturned = status === 'Returned';

  // Get actual permissions based on current status and creator
  const userCanEdit = canEdit(status, creatorId);
  const userCanApprove = canApprove(status, creatorId);

  const handleSave = async () => {
    if (isSaving) return; // Prevent double-clicking

    try {
      setIsSaving(true);

      // Use the original saveAndExitEditMode but with proper timing
      saveAndExitEditMode((data) => {
        if (onSave) {
          onSave(data);
        }
      });

      // Add a small delay to ensure the save operation has time to complete
      // before we allow the component to re-render with new button state
      await new Promise((resolve) => setTimeout(resolve, 200));
    } catch (error) {
      console.error('Save error:', error);
    } finally {
      setIsSaving(false);
    }
  };

  // Show edit mode buttons only when actually in edit mode
  // During saving, we still show edit mode buttons to prevent flickering
  if (isEditing) {
    return (
      <div className={`flex space-x-2 ${className}`}>
        {/* <Button
          type="button"
          onClick={() => console.log('Test functionality not implemented')}
          disabled={!userCanEdit || isSaving}
          variant="secondary"
        >
          Dependency
        </Button>
        <Button
          type="button"
          onClick={() => console.log('Test functionality not implemented')}
          variant="secondary"
          disabled={!userCanEdit || isSaving}
        >
          Manage Triggers
        </Button>
        <Button
          type="button"
          onClick={() => console.log('Test functionality not implemented')}
          variant="secondary"
          disabled={!userCanEdit || isSaving}
        >
          Supplementary Recording
        </Button> */}
        <Button
          type="button"
          onClick={cancelEdit}
          variant="secondary"
          disabled={isSaving}
        >
          {t('ctint-mf-cdss.qmAdmin.sopTemplates.actionButtons.cancel')}
        </Button>
        <Button
          type="button"
          onClick={handleSave}
          disabled={isSaving}
        >
          {isSaving
            ? t('ctint-mf-cdss.qmAdmin.sopTemplates.actionButtons.saving')
            : t('ctint-mf-cdss.qmAdmin.sopTemplates.actionButtons.save')}
        </Button>
      </div>
    );
  }

  // Different buttons based on status and permissions
  return (
    <div className={`flex space-x-2 ${className}`}>
      {/* <Button
        type="button"
        onClick={() => console.log('Test functionality not implemented')}
        disabled={!userCanEdit}
        variant="secondary"
      >
        Dependency
      </Button>
      <Button
        type="button"
        onClick={() => console.log('Test functionality not implemented')}
        variant="secondary"
        disabled={!userCanEdit}
      >
        Manage Triggers
      </Button>
      <Button
        type="button"
        onClick={() => console.log('Test functionality not implemented')}
        variant="secondary"
        disabled={!userCanEdit}
      >
        Supplementary Recording
      </Button> */}
      {/* Edit button - only for Draft or Returned status and if user has edit permission */}
      {/* {userCanEdit && (isDraft || isReturned) && ( */}
      <Button
        type="button"
        onClick={enterEditMode}
      >
        {t('ctint-mf-cdss.qmAdmin.sopTemplates.actionButtons.edit')}
      </Button>
      {/* )} */}

      {/* Submit for Approval - only for Draft or Returned status and if user has edit permission */}
      {userCanEdit && (isDraft || isReturned) && (
        <Button
          type="button"
          onClick={() => submitForApprovalAction(onStatusChange)}
        >
          {t(
            'ctint-mf-cdss.qmAdmin.sopTemplates.actionButtons.submitForApproval'
          )}
        </Button>
      )}

      {/* Approve button - only for Pending Approval status and if user has approve permission */}
      {userCanApprove && isPendingApproval && (
        <Button
          type="button"
          onClick={() => approveTemplate(onStatusChange)}
        >
          {t('ctint-mf-cdss.qmAdmin.sopTemplates.actionButtons.approve')}
        </Button>
      )}

      {/* Return button - only for Pending Approval status and if user has approve permission */}
      {userCanApprove && isPendingApproval && (
        <Button
          type="button"
          onClick={() => returnTemplate(onStatusChange)}
        >
          {t('ctint-mf-cdss.qmAdmin.sopTemplates.actionButtons.return')}
        </Button>
      )}
    </div>
  );
};
