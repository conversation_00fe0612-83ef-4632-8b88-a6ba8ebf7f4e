import { ColumnDef } from '@tanstack/react-table';
import { SortingButton } from '@cdss-modules/design-system';
import Checkbox from '@cdss-modules/design-system/components/_ui/Checkbox';
import StatusBadge, { STATUS_STYLES } from '../StatusBadge';
import { BaseTableData } from './ReusableTable';

export interface ColumnConfig {
  accessorKey: string;
  id: string;
  header: string;
  enableSorting?: boolean;
  cellRenderer?: (value: any, row: any) => React.ReactNode;
}

export interface SortHandler {
  orderBy?: string;
  order?: 'ASC' | 'DESC';
  onSort?: (orderBy: string, order: 'ASC' | 'DESC' | undefined) => void;
}

// Helper function to create the select column
export const createSelectColumn = <
  T extends BaseTableData,
>(): ColumnDef<T> => ({
  id: 'select',
  header: ({ table }: any) => (
    <div
      className="bg-white z-30"
      onClick={(e) => e.stopPropagation()}
    >
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && 'indeterminate')
        }
        onChange={(e: any) => {
          const isSelected = e?.target.checked;
          table.toggleAllPageRowsSelected(isSelected);
        }}
      />
    </div>
  ),
  cell: ({ row }: any) => (
    <div
      className="z-0"
      onClick={(e) => e.stopPropagation()}
    >
      <Checkbox
        checked={row.getIsSelected()}
        onChange={(e: any) => {
          const isSelected = e?.target.checked;
          row.toggleSelected(isSelected);
        }}
      />
    </div>
  ),
  enableSorting: false,
  enableHiding: false,
});

// Helper function to create a sortable column
export const createSortableColumn = <T extends BaseTableData>(
  accessorKey: string,
  header: string,
  sortHandler: SortHandler,
  cellRenderer?: (value: any, row: any) => React.ReactNode
): ColumnDef<T> => {
  const handleHeaderSortClick = (columnId: string) => {
    if (sortHandler.onSort) {
      if (sortHandler.orderBy === columnId) {
        // Column is already sorted, toggle direction or clear
        if (sortHandler.order === 'ASC') {
          sortHandler.onSort(columnId, 'DESC');
        } else if (sortHandler.order === 'DESC') {
          sortHandler.onSort('', undefined); // Clear sort by passing empty orderBy
        } else {
          sortHandler.onSort(columnId, 'ASC'); // This shouldn't happen, but just in case
        }
      } else {
        // New column to sort
        sortHandler.onSort(columnId, 'ASC');
      }
    }
  };

  return {
    accessorKey,
    id: accessorKey,
    header: ({ column }) => (
      <SortingButton
        sorting={
          sortHandler.orderBy === accessorKey
            ? sortHandler.order === 'ASC'
              ? 'asc'
              : sortHandler.order === 'DESC'
                ? 'desc'
                : false
            : false
        }
        onClick={() => handleHeaderSortClick(accessorKey)}
      >
        {header}
      </SortingButton>
    ),
    cell: ({ row }) => {
      const value = row.getValue(accessorKey);
      if (cellRenderer) {
        return cellRenderer(value, row);
      }
      return <div>{String(value)}</div>;
    },
    enableSorting: true,
  };
};

// Helper function to create a non-sortable column
export const createSimpleColumn = <T extends BaseTableData>(
  accessorKey: string,
  header: string,
  cellRenderer?: (value: any, row: any) => React.ReactNode
): ColumnDef<T> => ({
  accessorKey,
  id: accessorKey,
  header,
  cell: ({ row }) => {
    const value = row.getValue(accessorKey);
    if (cellRenderer) {
      return cellRenderer(value, row);
    }
    return <div>{String(value)}</div>;
  },
  enableSorting: false,
});

// Helper function to create status column with styling
export const createStatusColumn = <T extends BaseTableData>(
  sortHandler: SortHandler,
  t: (key: string) => string
): ColumnDef<T> => {
  return createSortableColumn<T>(
    'status',
    t('ctint-mf-cdss.qmAdmin.tableColumns.status'),
    sortHandler,
    (status: string) => {
      const bgColor = STATUS_STYLES[status] || STATUS_STYLES['default'];
      return (
        <StatusBadge
          status={status}
          className={`${bgColor}`}
          t={t}
        />
      );
    }
  );
};

// Helper function to create date column with formatting
export const createDateColumn = <T extends BaseTableData>(
  accessorKey: string,
  header: string,
  sortHandler: SortHandler,
  formatAsDate = true
): ColumnDef<T> =>
  createSortableColumn<T>(
    accessorKey,
    header,
    sortHandler,
    (dateValue: string) => {
      if (formatAsDate && dateValue) {
        return <div>{new Date(dateValue).toLocaleString()}</div>;
      }
      return <div>{dateValue || ''}</div>;
    }
  );

// Predefined column configurations for different data types
export const createSensitiveKeywordsColumns = <T extends BaseTableData>(
  sortHandler: SortHandler,
  t: (key: string) => string
): ColumnDef<T>[] => {
  return [
    // createSelectColumn<T>(),
    createSortableColumn<T>(
      'id',
      t('ctint-mf-cdss.qmAdmin.tableColumns.id'),
      sortHandler
    ),
    createSortableColumn<T>(
      'name',
      t('ctint-mf-cdss.qmAdmin.tableColumns.name'),
      sortHandler
    ),
    createSortableColumn<T>(
      'language',
      t('ctint-mf-cdss.qmAdmin.tableColumns.language'),
      sortHandler
    ),
    createSortableColumn<T>(
      'createdBy',
      t('ctint-mf-cdss.qmAdmin.tableColumns.createdBy'),
      sortHandler
    ),
    createDateColumn<T>(
      'createdDate',
      t('ctint-mf-cdss.qmAdmin.tableColumns.createdDate'),
      sortHandler
    ),
    createDateColumn<T>(
      'lastUpdated',
      t('ctint-mf-cdss.qmAdmin.tableColumns.lastUpdated'),
      sortHandler
    ),
  ];
};

export const createSTTKeywordsColumns = <T extends BaseTableData>(
  sortHandler: SortHandler,
  t: (key: string) => string
): ColumnDef<T>[] => {
  return [
    // createSelectColumn<T>(),
    createSortableColumn<T>(
      'id',
      t('ctint-mf-cdss.qmAdmin.tableColumns.id'),
      sortHandler
    ),
    createSortableColumn<T>(
      'name',
      t('ctint-mf-cdss.qmAdmin.tableColumns.name'),
      sortHandler
    ),
    createSortableColumn<T>(
      'language',
      t('ctint-mf-cdss.qmAdmin.tableColumns.language'),
      sortHandler
    ),
    createSortableColumn<T>(
      'createdBy',
      t('ctint-mf-cdss.qmAdmin.tableColumns.createdBy'),
      sortHandler
    ),
    createDateColumn<T>(
      'createTime',
      t('ctint-mf-cdss.qmAdmin.tableColumns.createTime'),
      sortHandler
    ),
    createDateColumn<T>(
      'updateTime',
      t('ctint-mf-cdss.qmAdmin.tableColumns.updateTime'),
      sortHandler
    ),
  ];
};

export const createMetadataColumns = <T extends BaseTableData>(
  sortHandler: SortHandler,
  t: (key: string) => string
): ColumnDef<T>[] => {
  return [
    // createSelectColumn<T>(),
    createSortableColumn<T>(
      'id',
      t('ctint-mf-cdss.qmAdmin.tableColumns.id'),
      sortHandler
    ),
    createSortableColumn<T>(
      'name',
      t('ctint-mf-cdss.qmAdmin.tableColumns.name'),
      sortHandler
    ),
    createSortableColumn<T>(
      'language',
      t('ctint-mf-cdss.qmAdmin.tableColumns.language'),
      sortHandler
    ),
    createSortableColumn<T>(
      'createdBy',
      t('ctint-mf-cdss.qmAdmin.tableColumns.createdBy'),
      sortHandler
    ),
    createDateColumn<T>(
      'createdDate',
      t('ctint-mf-cdss.qmAdmin.tableColumns.createdDate'),
      sortHandler
    ),
    createDateColumn<T>(
      'lastUpdated',
      t('ctint-mf-cdss.qmAdmin.tableColumns.lastUpdated'),
      sortHandler
    ),
  ];
};

export const createDictionaryColumns = <T extends BaseTableData>(
  sortHandler: SortHandler,
  t: (key: string) => string
): ColumnDef<T>[] => {
  return [
    // createSelectColumn<T>(),
    createSortableColumn<T>(
      'id',
      t('ctint-mf-cdss.qmAdmin.tableColumns.id'),
      sortHandler
    ),
    createSortableColumn<T>(
      'name',
      t('ctint-mf-cdss.qmAdmin.tableColumns.name'),
      sortHandler
    ),
    createSortableColumn<T>(
      'language',
      t('ctint-mf-cdss.qmAdmin.tableColumns.language'),
      sortHandler
    ),
    createSortableColumn<T>(
      'createdBy',
      t('ctint-mf-cdss.qmAdmin.tableColumns.createdBy'),
      sortHandler
    ),
    createDateColumn<T>(
      'createdDate',
      t('ctint-mf-cdss.qmAdmin.tableColumns.createdDate'),
      sortHandler
    ),
    createDateColumn<T>(
      'lastUpdated',
      t('ctint-mf-cdss.qmAdmin.tableColumns.lastUpdated'),
      sortHandler
    ),
  ];
};

// export const createSOPTemplateColumns = <T extends BaseTableData>(
//   sortHandler: SortHandler,
//   t: (key: string) => string
// ): ColumnDef<T>[] => {
//   return [
//     // createSelectColumn<T>(),
//     createSortableColumn<T>(
//       'id',
//       t('ctint-mf-cdss.qmAdmin.tableColumns.id'),
//       sortHandler
//     ),
//     createSortableColumn<T>(
//       'name',
//       t('ctint-mf-cdss.qmAdmin.tableColumns.name'),
//       sortHandler
//     ),
//     createStatusColumn<T>(sortHandler, t),
//     createSortableColumn<T>(
//       'version',
//       t('ctint-mf-cdss.qmAdmin.tableColumns.version'),
//       sortHandler
//     ),
//     createSortableColumn<T>(
//       'createdBy',
//       t('ctint-mf-cdss.qmAdmin.tableColumns.createdBy'),
//       sortHandler
//     ),
//     createDateColumn<T>(
//       'createdDate',
//       t('ctint-mf-cdss.qmAdmin.tableColumns.createdDate'),
//       sortHandler
//     ),
//     createDateColumn<T>(
//       'lastUpdated',
//       t('ctint-mf-cdss.qmAdmin.tableColumns.lastUpdated'),
//       sortHandler
//     ),
//   ];
// };

// New column configuration for SOP templates with form-specific fields
export const createSOPFormTemplateColumns = <T extends BaseTableData>(
  sortHandler: SortHandler,
  t: (key: string) => string
): ColumnDef<T>[] => {
  return [
    // createSelectColumn<T>(),
    createSortableColumn<T>(
      'formId',
      t('ctint-mf-cdss.qmAdmin.tableColumns.formId'),
      sortHandler
    ),
    createSortableColumn<T>(
      'name',
      t('ctint-mf-cdss.qmAdmin.tableColumns.name'),
      sortHandler
    ),
    createSortableColumn<T>(
      'language',
      t('ctint-mf-cdss.qmAdmin.tableColumns.language'),
      sortHandler
    ),
    // createStatusColumn<T>(sortHandler, t),
    // createSortableColumn<T>(
    //   'formVersion',
    //   t('ctint-mf-cdss.qmAdmin.tableColumns.formVersion'),
    //   sortHandler
    // ),
    // createSortableColumn<T>(
    //   'product',
    //   t('ctint-mf-cdss.qmAdmin.tableColumns.product'),
    //   sortHandler
    // ),
    createSortableColumn<T>(
      'createUser',
      t('ctint-mf-cdss.qmAdmin.tableColumns.createBy'),
      sortHandler
    ),
    createDateColumn<T>(
      'createTime',
      t('ctint-mf-cdss.qmAdmin.tableColumns.createTime'),
      sortHandler
    ),
    createDateColumn<T>(
      'updateTime',
      t('ctint-mf-cdss.qmAdmin.tableColumns.updateTime'),
      sortHandler
    ),
  ];
};
