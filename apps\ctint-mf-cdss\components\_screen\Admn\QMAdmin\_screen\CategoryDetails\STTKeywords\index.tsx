import React, { useState, useEffect } from 'react';
import { useTab } from '../../../context/TabContext';
import { ReusableTable } from '../../../_ui/CategoriesDetails/ReusableTable';
import { createSTTKeywordsColumns } from '../../../_ui/CategoriesDetails/TableColumnsHelper';
import STTKeywordsDetails from './STTKeywordsDetails';
import {
  useSTTKeywordsUnifiedQuery,
  UnifiedCreateRequest,
  useUnifiedCreateDefinitionMutation,
} from '../../unified-queries';
import { STTKeywordData } from '../../../types';
import { SearchAndFilter } from '../../../_ui/CategoriesDetails/SearchAndFilter';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';
import {
  generateDefaultVersion,
  getTranslatedStatusOptions,
} from '../../../_ui/CategoriesDetails/constants';

import { ReusablePopup } from '@cdss/components/_ui/ReusablePopup';
import debounce from 'lodash/debounce';
import { useCallback } from 'react';
import { useSorting } from '../../../hooks/useSorting';

interface STTKeywordsTabProps {
  categoryId: string;
}

export const STTKeywordsTab: React.FC<STTKeywordsTabProps> = ({
  categoryId,
}) => {
  const { t } = useTranslation();
  const { selectedItem, setSelectedItem } = useTab();
  const [searchValue, setSearchValue] = useState('');
  const [debouncedSearchValue, setDebouncedSearchValue] = useState('');
  const [statusFilter, setStatusFilter] = useState('Status');
  const [currentPage, setCurrentPage] = useState(1);
  const [perPage, setPerPage] = useState(50);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);

  // Use the sorting hook
  const { orderBy, order, handleSortChange, resetSort } = useSorting({
    defaultOrderBy: 'createTime',
    defaultOrder: 'DESC',
  });

  // Reset search and pagination when category changes
  useEffect(() => {
    setSearchValue('');
    setDebouncedSearchValue('');
    setStatusFilter('Status');
    setCurrentPage(1);
    // Reset sorting when category changes
    resetSort();
  }, [categoryId, resetSort]);

  // Debounce search to avoid excessive API calls
  const debouncedSearch = useCallback(
    debounce((value: string) => {
      setDebouncedSearchValue(value);
      setCurrentPage(1); // Reset to first page on new search
    }, 400),
    []
  );

  // Update debounced search when search value changes
  useEffect(() => {
    debouncedSearch(searchValue);
  }, [searchValue, debouncedSearch]);

  // Data fetching with the new unified React Query - using the unified API
  const { data: sttKeywordsResponse, isLoading: isListLoading } =
    useSTTKeywordsUnifiedQuery(
      categoryId, // parentId as string to match the unified query pattern
      debouncedSearchValue,
      statusFilter === 'Status' ? '' : statusFilter,
      currentPage,
      perPage,
      orderBy, // Pass orderBy
      order // Pass order
    );

  console.log('STT Keywords Response:', sttKeywordsResponse);

  // Add the create mutation hook - using the unified approach
  const createSTTKeywordMutation = useUnifiedCreateDefinitionMutation();

  // Extract STT keywords list and total from the API response - matching categories pattern
  const sttKeywordsList = sttKeywordsResponse?.data?.items || [];
  const totalItems = sttKeywordsResponse?.data?.totalCount || 0;
  const totalPages = sttKeywordsResponse?.data?.totalPage || 0;

  // Transform the API response to match the expected STTKeywordData interface
  const transformedSTTKeywordsList: STTKeywordData[] = sttKeywordsList.map(
    (item: any) => ({
      ...item,
      createdDate: item.createTime,
      lastUpdated: item.updateTime,
      approvedBy: item.approveUser,
      createdBy: item.createUser,
      language: item.language, // Add language property with fallback
    })
  );

  const handleKeywordClick = (keyword: STTKeywordData) => {
    setSelectedItem({ type: 'sttKeywords', id: keyword.id });
  };

  const handleCreateSubmit = (values: Record<string, string>) => {
    const { name, version, platform } = values;

    // Use the unified create mutation
    const request: UnifiedCreateRequest = {
      name,
      relationshipType: 'sttKeyword',
      status: 'Draft',
      version: version || '1.0',
      parentId: categoryId, // Keep as string to match the unified pattern
      platform: platform || 'mobile-app',
    };

    createSTTKeywordMutation.mutate(request, {
      onSuccess: (response) => {
        // Set the newly created keyword as selected
        const newKeyword = response.data;
        setSelectedItem({ type: 'sttKeywords', id: newKeyword.id });
        setIsCreateModalOpen(false);
      },
      onError: () => {
        // Keep the modal open on error so user can retry
        // Error handling is already done in the mutation hook
      },
    });
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handlePerPageChange = (value: number) => {
    setPerPage(value);
    setCurrentPage(1); // Reset to page 1 when changing items per page
  };

  // Handler for sorting changes from table - now uses the hook
  const handleTableSortChange = (
    newOrderBy: string,
    newOrder: 'ASC' | 'DESC' | undefined
  ) => {
    handleSortChange(newOrderBy, newOrder);
    setCurrentPage(1); // Reset to first page on sort change
  };

  const createSTTKeywordFields = [
    {
      key: 'name',
      label: t('ctint-mf-cdss.qmAdmin.sttKeywords.keywordName'),
      placeholder: t('ctint-mf-cdss.qmAdmin.sttKeywords.enterKeywordName'),
      required: true,
    },
    {
      key: 'version',
      label: t('ctint-mf-cdss.qmAdmin.sttKeywords.version'),
      placeholder: t('ctint-mf-cdss.qmAdmin.sttKeywords.enterVersion'),
      initialValue: generateDefaultVersion(),
      required: true,
    },
    // {
    //   key: 'platform',
    //   label: 'Platform',
    //   placeholder: 'Enter platform (e.g., mobile-app)',
    //   initialValue: 'mobile-app',
    //   required: false,
    // },
  ];

  // Create columns using the helper
  const columns = createSTTKeywordsColumns<STTKeywordData>(
    {
      orderBy,
      order,
      onSort: handleTableSortChange,
    },
    t
  );

  // If we have a selected STT keyword item, show the details component
  // We don't pass the keyword data from the list view - the detail component will fetch its own data
  if (selectedItem.type === 'sttKeywords') {
    return (
      <STTKeywordsDetails
        data={transformedSTTKeywordsList?.find((k) => k.id === selectedItem.id)}
      />
    );
  }

  return (
    <div className="p-6 flex-1 flex flex-col h-full overflow-y-auto">
      <div className="flex justify-between items-center mb-4">
        <SearchAndFilter
          searchValue={searchValue}
          setSearchValue={setSearchValue}
          statusFilter={statusFilter}
          setStatusFilter={setStatusFilter}
          statusOptions={getTranslatedStatusOptions(t)}
        />
        <ReusablePopup
          isOpen={isCreateModalOpen}
          onOpenChange={setIsCreateModalOpen}
          title={t('ctint-mf-cdss.qmAdmin.sttKeywords.createKeyword')}
          fields={createSTTKeywordFields}
          onSubmitMultiple={handleCreateSubmit}
          triggerButtonText={t(
            'ctint-mf-cdss.qmAdmin.sttKeywords.createKeyword'
          )}
          submitButtonText={t('ctint-mf-cdss.qmAdmin.sttKeywords.create')}
        />
      </div>
      <ReusableTable
        data={transformedSTTKeywordsList}
        columns={columns}
        isLoading={isListLoading}
        onRowClick={handleKeywordClick}
        currentPage={currentPage}
        perPage={perPage}
        onPageChange={handlePageChange}
        onPerPageChange={handlePerPageChange}
        totalItems={totalItems}
        totalPages={totalPages}
        emptyMessage={t('ctint-mf-cdss.qmAdmin.sttKeywords.noDataFound')}
      />
    </div>
  );
};
