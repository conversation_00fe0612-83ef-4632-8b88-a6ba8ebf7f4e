import React, { useState, useEffect } from 'react';
import { useTab } from '../../../context/TabContext';
import { ReusableTable } from '../../../_ui/CategoriesDetails/ReusableTable';
import { createMetadataColumns } from '../../../_ui/CategoriesDetails/TableColumnsHelper';
import { MetadataDetails } from './MetadataDetails';
import {
  useMetadataUnifiedQuery,
  UnifiedCreateRequest,
} from '../../unified-queries';
import {
  useUnifiedCreateDefinitionMutation,
  UnifiedCreateResponse,
} from '../../definition-queries';
import { MetadataData } from '../../../types';
import { SearchAndFilter } from '../../../_ui/CategoriesDetails/SearchAndFilter';
import Button from '@cdss-modules/design-system/components/_ui/Button';
import { ReusablePopup } from '@cdss/components/_ui/ReusablePopup';
import debounce from 'lodash/debounce';
import { useCallback } from 'react';
import { useSorting } from '../../../hooks/useSorting';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';
import {
  generateDefaultVersion,
  getTranslatedStatusOptions,
} from '../../../_ui/CategoriesDetails/constants';

interface MetadataTabProps {
  categoryId: string;
}

export const MetadataTab: React.FC<MetadataTabProps> = ({ categoryId }) => {
  const { t } = useTranslation();
  const { selectedItem, setSelectedItem } = useTab();
  const [searchValue, setSearchValue] = useState('');
  const [debouncedSearchValue, setDebouncedSearchValue] = useState('');
  const [statusFilter, setStatusFilter] = useState('Status');
  const [currentPage, setCurrentPage] = useState(1);
  const [perPage, setPerPage] = useState(50);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);

  // Use the sorting hook
  const { orderBy, order, handleSortChange, resetSort } = useSorting({
    defaultOrderBy: 'createTime',
    defaultOrder: 'DESC',
  });

  // Reset search and pagination when category changes
  useEffect(() => {
    setSearchValue('');
    setDebouncedSearchValue('');
    setStatusFilter('Status');
    setCurrentPage(1);
    // Reset sorting when category changes
    resetSort();
  }, [categoryId, resetSort]);

  // Debounce search to avoid excessive API calls
  const debouncedSearch = useCallback(
    debounce((value: string) => {
      setDebouncedSearchValue(value);
      setCurrentPage(1); // Reset to first page on new search
    }, 1000),
    []
  );

  // Update debounced search when search value changes
  useEffect(() => {
    debouncedSearch(searchValue);
  }, [searchValue, debouncedSearch]);

  // Data fetching with the new unified React Query - using the unified API
  const { data: metadataResponse, isLoading: isListLoading } =
    useMetadataUnifiedQuery(
      categoryId, // parentId as string to match the unified query pattern
      debouncedSearchValue,
      statusFilter === 'Status' ? '' : statusFilter,
      currentPage,
      perPage,
      orderBy, // Pass orderBy
      order // Pass order
    );

  // Add the create mutation hook - using the unified approach
  const createMetadataMutation = useUnifiedCreateDefinitionMutation();

  // Extract metadata list and total from the API response - matching categories pattern
  const metadataList = metadataResponse?.data?.items || [];
  const totalItems = metadataResponse?.data?.totalCount || 0;
  const totalPages = metadataResponse?.data?.totalPage || 0;

  // Transform the API response to match the expected MetadataData interface
  const transformedMetadataList: MetadataData[] = metadataList.map(
    (item: any) => ({
      ...item,
      createdDate: item.createTime,
      lastUpdated: item.updateTime,
      approvedBy: item.approveUser,
      createdBy: item.createUser,
      language: item.language, // Add language property with fallback
    })
  );

  const handleMetadataClick = (metadataItem: MetadataData) => {
    setSelectedItem({ type: 'metadata', id: metadataItem.id });
  };

  const handleCreateSubmit = (values: Record<string, string>) => {
    const { name, version } = values;

    // Use the unified create mutation
    const request: UnifiedCreateRequest = {
      name,
      relationshipType: 'metadata',
      status: 'Draft',
      version: version || '1.0',
      parentId: categoryId, // Keep as string to match the unified pattern
    };

    createMetadataMutation.mutate(request, {
      onSuccess: (response: UnifiedCreateResponse) => {
        // Set the newly created metadata as selected
        const newMetadata = response.data;
        setSelectedItem({ type: 'metadata', id: newMetadata.id });
        setIsCreateModalOpen(false);
      },
      onError: () => {
        // Keep the modal open on error so user can retry
        // Error handling is already done in the mutation hook
      },
    });
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handlePerPageChange = (value: number) => {
    setPerPage(value);
    setCurrentPage(1); // Reset to page 1 when changing items per page
  };

  // Handler for sorting changes from table - now uses the hook
  const handleTableSortChange = (
    newOrderBy: string,
    newOrder: 'ASC' | 'DESC' | undefined
  ) => {
    handleSortChange(newOrderBy, newOrder);
    setCurrentPage(1); // Reset to first page on sort change
  };

  const createMetadataFields = [
    {
      key: 'name',
      label: t('ctint-mf-cdss.qmAdmin.metadata.metadataName'),
      placeholder: t('ctint-mf-cdss.qmAdmin.metadata.enterMetadataName'),
      required: true,
    },
    {
      key: 'version',
      label: t('ctint-mf-cdss.qmAdmin.metadata.version'),
      placeholder: t('ctint-mf-cdss.qmAdmin.metadata.enterVersion'),
      initialValue: generateDefaultVersion(),
      required: true,
    },
  ];

  // Create columns using the helper
  const columns = createMetadataColumns<MetadataData>(
    {
      orderBy,
      order,
      onSort: handleTableSortChange,
    },
    t
  );

  // If we have a selected metadata item, show the details component
  // We don't pass the metadata data from the list view - the detail component will fetch its own data
  if (selectedItem.type === 'metadata' && selectedItem.id) {
    return <MetadataDetails metadataId={selectedItem.id} />;
  }

  return (
    <div className="p-6 flex-1 flex flex-col h-full overflow-y-auto">
      <div className="flex justify-between items-center mb-4">
        <SearchAndFilter
          searchValue={searchValue}
          setSearchValue={setSearchValue}
          statusFilter={statusFilter}
          setStatusFilter={setStatusFilter}
          statusOptions={getTranslatedStatusOptions(t)}
        />
        {/* Reomve the create metadata popup for now*/}
        {/* <ReusablePopup
          isOpen={isCreateModalOpen}
          onOpenChange={setIsCreateModalOpen}
          title={t('ctint-mf-cdss.qmAdmin.metadata.createMetadata')}
          fields={createMetadataFields}
          onSubmitMultiple={handleCreateSubmit}
          triggerButtonText={t('ctint-mf-cdss.qmAdmin.metadata.createMetadata')}
          submitButtonText={t('ctint-mf-cdss.qmAdmin.metadata.create')}
        /> */}
      </div>
      <ReusableTable
        data={transformedMetadataList}
        columns={columns}
        isLoading={isListLoading}
        onRowClick={handleMetadataClick}
        currentPage={currentPage}
        perPage={perPage}
        onPageChange={handlePageChange}
        onPerPageChange={handlePerPageChange}
        totalItems={totalItems}
        totalPages={totalPages}
        emptyMessage={t('ctint-mf-cdss.qmAdmin.metadata.noDataFound')}
      />
    </div>
  );
};
