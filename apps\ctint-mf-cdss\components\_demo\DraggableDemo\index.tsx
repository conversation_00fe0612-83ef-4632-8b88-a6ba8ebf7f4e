'use client';

import { useState } from 'react';
import Draggable, {
  ICriteria,
} from '@cdss-modules/design-system/components/_ui/Draggable';

const planets: ICriteria[] = [
  {
    labelEn: 'Mercury',
    labelCh: '水星',
    value: 'Mercury',
    data: 'Mercury',
    filterType: 'input',
    active: true,
  },
  {
    labelEn: 'Venus',
    labelCh: '金星',
    value: 'Venus',
    data: 'Venus',
    filterType: 'input',
    active: true,
  },
  {
    labelEn: 'Earth',
    labelCh: '地球',
    value: 'Earth',
    data: 'Earth',
    filterType: 'input',
    active: true,
  },
  {
    labelEn: 'Mars',
    labelCh: '火星',
    value: 'Mars',
    data: 'Mars',
    filterType: 'input',
    active: true,
  },
  {
    labelEn: 'Jupiter',
    labelCh: '木星',
    value: 'Jupiter',
    data: 'Jupiter',
    filterType: 'input',
    active: true,
  },
];

const DraggableDemo = () => {
  const [tableColumn, setTableColumn] = useState<ICriteria[]>(planets);

  const handleOrder = (e: any) => {
    console.log('handleOrder', e);
    setTableColumn(e);
  };

  return (
    <div className="w-full flex flex-row gap-8">
      <Draggable
        items={tableColumn}
        onChange={handleOrder}
        toogleSwitch
      />
      <div className="w-1/2">
        {tableColumn
          .filter((table: ICriteria) => table.active)
          .map((item: ICriteria) => item.value)
          .join(', ')}
      </div>
    </div>
  );
};

export default DraggableDemo;
