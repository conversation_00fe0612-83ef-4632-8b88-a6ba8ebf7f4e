'use client';

import React, { useState } from 'react';

import { useSOPTemplateStore } from '../../../../../../../_store/sopTemplateStore';
import CriteriaSectionView from './CriteriaSectionView';
import CriteriaSectionEdit from './CriteriaSectionEdit';

export const CriteriaSection = () => {
  const {
    getCurrentStepData,
    currentStep,
    currentScenario,
    updateStepField,
    isEditing,
  } = useSOPTemplateStore();
  const stepData = getCurrentStepData();
  const [isInEditMode, setIsInEditMode] = useState(false);

  if (!stepData) {
    return null;
  }

  const handleEdit = () => {
    setIsInEditMode(true);
  };

  const handleSave = (newCriteria: string) => {
    // updateStepField(currentStep, currentScenario, 'criteria', newCriteria);
    setIsInEditMode(false);
  };

  const handleCancel = () => {
    setIsInEditMode(false);
  };

  return (
    <div className="border rounded-md p-4">
      <div className="flex justify-between items-center mb-3">
        <h3 className="font-medium text-lg">Criteria</h3>
        {isEditing && !isInEditMode && (
          <button
            className="text-blue-600 hover:text-blue-800 text-sm font-medium"
            onClick={handleEdit}
          >
            Edit
          </button>
        )}
      </div>

      {/* {isEditing && isInEditMode ? (
        <CriteriaSectionEdit
          criteria={stepData.criteria}
          onSave={handleSave}
          onCancel={handleCancel}
        />
      ) : (
        <CriteriaSectionView criteria={stepData.criteria} />
      )} */}
    </div>
  );
};

export default CriteriaSection;
