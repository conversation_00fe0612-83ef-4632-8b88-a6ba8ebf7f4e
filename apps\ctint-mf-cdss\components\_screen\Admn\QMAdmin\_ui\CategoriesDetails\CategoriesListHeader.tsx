'use client';

import { useTranslation } from '@cdss-modules/design-system/i18n/client';
import React from 'react';
import { Tabs } from '@cdss-modules/design-system/components/_ui/Tabs';
import { useTabsContext } from '@cdss-modules/design-system/context/TabsContext';
import { useTab } from '../../context/TabContext';
import { useNavigate } from 'react-router-dom';

// Define the interface for breadcrumb items
interface BreadcrumbItem {
  label: string | number;
  link?: string;
  onClick?: () => void;
}

const Breadcrumbs = ({ items }: { items: BreadcrumbItem[] }) => {
  const navigate = useNavigate();

  if (items.length === 0) return null;

  return items.map((item, index) => (
    <React.Fragment key={index}>
      {item.link ? (
        <span
          onClick={() => navigate(item.link!)}
          className="hover:underline cursor-pointer"
        >
          {item.label}
        </span>
      ) : item.onClick ? (
        <span
          onClick={item.onClick}
          className="hover:underline cursor-pointer"
        >
          {item.label}
        </span>
      ) : typeof item.label === 'string' && item.label === 'Loading...' ? (
        <span className="inline-block h-4 w-24 bg-gray-200 animate-pulse rounded"></span>
      ) : (
        <span>{item.label}</span>
      )}
      {index < items.length - 1 && <span className="mx-1">/</span>}
    </React.Fragment>
  ));
};

export const CategoriesListHeader: React.FC = () => {
  const { t } = useTranslation();

  // Use TabsContext directly for tab state
  const { selected: activeTab, onChangeTab: setActiveTab } = useTabsContext();

  // Only use TabContext for categoryName and loading state
  const { categoryName, isCategoryLoading } = useTab();

  // Create breadcrumb items based on whether we're in details view
  const breadcrumbItems: BreadcrumbItem[] = [
    // Base item - always present
    {
      label: t('ctint-mf-cdss.qmAdmin.breadcrumb.sopCategories'),
      link: '/ctint/mf-cdss/admin/qm-admin',
    },
  ];

  breadcrumbItems.push({
    label: isCategoryLoading ? '' : categoryName || 'Category',
  });

  return (
    <div className="p-6 pb-0">
      <div className="mb-4 flex items-center">
        <Breadcrumbs items={breadcrumbItems} />
      </div>
      <Tabs
        onChangeTabFunc={(tab) => setActiveTab(tab)}
        defaultTab={activeTab || 'sopTemplate'}
        triggers={[
          {
            value: 'sopTemplate',
            label: t('ctint-mf-cdss.qmAdmin.tabs.sopTemplate'),
          },
          {
            value: 'metadata',
            label: t('ctint-mf-cdss.qmAdmin.tabs.metadata'),
          },
          {
            value: 'dictionary',
            label: t('ctint-mf-cdss.qmAdmin.tabs.dictionary'),
          },
          {
            value: 'sttKeywords',
            label: t('ctint-mf-cdss.qmAdmin.tabs.sttKeywords'),
          },
          {
            value: 'sensitiveKeywords',
            label: t('ctint-mf-cdss.qmAdmin.tabs.sensitiveKeywords'),
          },
          // {
          //   value: 'permissions',
          //   label: 'Permissions',
          // },
        ]}
        triggerClassName="py-1 px-2 text-body"
      />
    </div>
  );
};
