// Version generator utility
export const generateDefaultVersion = () => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  return `v${year}${month}${day}`;
};

// Status options for regular components (draft, approved, etc.)
export const STATUS_OPTIONS = [
  { id: 'status', value: 'Status', label: 'Status' },
  { id: 'draft', value: 'Draft', label: 'Draft' },
  //   {
  //     id: 'pending approval',
  //     value: 'Pending Approval',
  //     label: 'Pending Approval',
  //   },
  { id: 'approved', value: 'Approved', label: 'Approved' },
  //   { id: 'returned', value: 'Returned', label: 'Returned' },
];

// Status options for SOP Templates (active, inactive)
export const SOP_TEMPLATE_STATUS_OPTIONS = [
  { id: 'status', value: 'Status', label: 'Status' },
  { id: 'active', value: 'Active', label: 'Active' },
  { id: 'inactive', value: 'Inactive', label: 'Inactive' },
];

// Generic function to get translated status options (for regular components)
export const getTranslatedStatusOptions = (t: (key: string) => string) => [
  {
    id: 'status',
    value: 'Status',
    label: t('ctint-mf-cdss.qmAdmin.status.status'),
  },
  {
    id: 'draft',
    value: 'Draft',
    label: t('ctint-mf-cdss.qmAdmin.status.draft'),
  },
  {
    id: 'approved',
    value: 'Approved',
    label: t('ctint-mf-cdss.qmAdmin.status.approved'),
  },
  // Uncomment these when needed:
  // {
  //   id: 'pending approval',
  //   value: 'Pending Approval',
  //   label: t('ctint-mf-cdss.qmAdmin.status.pendingApproval')
  // },
  // {
  //   id: 'returned',
  //   value: 'Returned',
  //   label: t('ctint-mf-cdss.qmAdmin.status.returned')
  // },
];

// Generic function to get translated status options for SOP Templates (active/inactive)
export const getTranslatedStatusOptionsSOPTemplate = (
  t: (key: string) => string
) => [
  {
    id: 'status',
    value: 'Status',
    label: t('ctint-mf-cdss.qmAdmin.status.status'),
  },
  {
    id: 'active',
    value: 'Active',
    label: t('ctint-mf-cdss.qmAdmin.status.active'),
  },
  {
    id: 'inactive',
    value: 'Inactive',
    label: t('ctint-mf-cdss.qmAdmin.status.inactive'),
  },
  // Uncomment these when needed:
  // {
  //   id: 'pending approval',
  //   value: 'Pending Approval',
  //   label: t('ctint-mf-cdss.qmAdmin.status.pendingApproval')
  // },
  // {
  //   id: 'returned',
  //   value: 'Returned',
  //   label: t('ctint-mf-cdss.qmAdmin.status.returned')
  // },
];

// Status values as a type for better type safety
// export type StatusValue =
//   | 'Status'
//   | 'Draft'
//   | 'Approved'
//   | 'Pending Approval'
//   | 'Returned'
//   | 'Active'
//   | 'Inactive';

// You can extend this pattern for other types of status options if needed
// export type a = 'default' | 'sopTemplate';

// // Generic function factory for different component types
// export const getTranslatedStatusOptionsByType = (
//   t: (key: string) => string,
//   type: ComponentType = 'default'
// ) => {
//   switch (type) {
//     case 'sopTemplate':
//       return getTranslatedStatusOptionsSOPTemplate(t);
//     case 'default':
//     default:
//       return getTranslatedStatusOptions(t);
//   }
// };
