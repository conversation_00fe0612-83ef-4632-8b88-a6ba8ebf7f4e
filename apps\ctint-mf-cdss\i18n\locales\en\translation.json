{"ctint-mf-cdss": {"template": "Template", "lipsum": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. <PERSON><PERSON>am nec purus nec nunc", "header": {"interactions": "Interactions", "users": "Users", "audit": "Audit", "qmAdmin": "QM Admin", "callPatch": "Call Patch", "logout": "Logout", "playbackPortal": "Playback Portal", "tts": "TTS", "singleMessage": "Single Message", "styleGuide": "Style Guide", "info": "Info"}, "login": {"username": "Username", "usernameOrEmail": "Username / Email", "password": "Password", "login": "<PERSON><PERSON>", "back": "Back"}, "langDemo": {"languageDemo": "Language Demo", "currentLanguage": "Current Language", "changeLanguage": "Change Language", "changeTo": "Change to"}, "qmAdmin": {"tabs": {"sop": "SOP", "metaDataMapping": "Meta Data Mapping", "sopTemplate": "SOP Template", "metadata": "<PERSON><PERSON><PERSON>", "dictionary": "Dictionary", "sttKeywords": "STT Keywords", "sensitiveKeywords": "Sensitive Keywords", "rerun": "<PERSON><PERSON>", "jobHistory": "Job History"}, "evaluationForm": "Evaluation Form", "evaluationFormPlaceholder": "Please select evaluation form", "formVersion": "Form Version", "formVersionPlaceholder": "Please select form version", "emptyData": "No data available", "support": {"searchRecordings": "Search Recordings for Job Re-run", "startTime": "Start Time", "selectStartTime": "Select start time", "endTime": "End Time", "selectEndTime": "Select end time", "rerunJobs": "Re-run Jobs", "jobType": "Job Type", "totalRecordings": "Total Recordings", "rerunAllJobs": "Re-run All Jobs", "rerunning": "Re-running...", "failedRecordings": "Failed Recordings", "rerunFailedJobs": "Re-run Failed Jobs", "recordingId": "Recording ID", "status": "Status", "timestamp": "Timestamp", "duration": "Duration", "agentId": "Agent ID", "searching": "Searching...", "search": "Search", "noRecordings": "No recordings found for the selected time range.", "useSearchForm": "Use the search form above to find recordings.", "quickDateSelection": "Quick Date Selection", "searchLimitation": "Search Limitation", "searchWithin180Days": "You can only search for recordings within 180 days range.", "quickSelect": "Quick Select", "24hours": "24 hours", "7days": "7 days", "30days": "30 days", "customDays": "Custom Range", "enterDays": "Enter number of days", "enterDaysPlaceholder": "e.g., 15", "apply": "Apply", "enterDaysHint": "Enter a number between 1 and 180 and press Enter or click Apply.", "validRange": "Selected range: {{days}} days (valid)", "invalidRange": "Selected range: {{days}} days (exceeds 180-day limit)", "confirmModal": {"title": "Confirm Re-run Jobs", "highPerformanceAction": "High Performance Action", "descriptionPrefix": "You are about to re-run", "descriptionSuffix": "This is a high-performance action that may impact system resources.", "allJobs": "all jobs", "failedJobs": "failed jobs", "confirmInstruction": "To confirm this action, please enter the following verification number:", "verificationNumber": "Verification Number:", "enterVerificationNumber": "Enter the verification number", "enterNumberPlaceholder": "Enter the number above", "cancel": "Cancel", "confirmRerun": "Confirm Re-run"}}, "jobHistory": {"title": "Job History", "noRecords": "No job history records found", "columns": {"batchId": "Batch ID", "processTime": "Process Time", "recordingPeriod": "Recording Period", "timeRangeStart": "Start Time", "timeRangeEnd": "End Time", "totalCount": "Total Count", "successCount": "Success Count", "failedCount": "Failed Count", "username": "User Name"}}, "sopCategories": {"title": "SOP Categories", "search": "Search...", "newCategory": "New Category", "categoryName": "Category Name", "inputCategoryName": "Input the Category Name", "editCategory": "Edit Category", "enterCategoryName": "Enter category name", "noDataFound": "No SOP categories found", "confirmDelete": "Are you sure you want to delete this category?", "columns": {"id": "ID", "name": "Name", "status": "Status", "version": "Version", "createTime": "Created Time", "createdBy": "Created By", "updatedBy": "Last Updated"}, "actions": {"edit": "Edit", "delete": "Delete", "save": "Save", "cancel": "Cancel", "create": "Create", "submit": "Submit"}}, "sopTemplates": {"createTemplate": "Create SOP Template", "templateName": "Template Name", "enterTemplateName": "Enter template name", "version": "Version", "enterVersion": "Enter version (e.g., v20250617)", "product": "Product", "enterProduct": "Enter product name", "noDataFound": "No SOP templates found", "create": "Create", "cancel": "Cancel", "details": {"backToList": "Back to List", "sopCategories": "SOP Categories", "sopTemplate": "SOP Template", "sopTemplateDefault": "SOP Template", "failedToLoad": "Failed to load template details", "loading": "Loading...", "noTemplateData": "No template data available", "somethingWentWrong": "Something went wrong", "refreshPage": "Please try refreshing the page", "templateSavedSuccessfully": "Template saved successfully", "failedToSaveTemplate": "Failed to save template", "templateStatusChanged": "Template status changed to", "failedToChangeStatus": "Failed to change template status", "invalidStatusChange": "Invalid Status Change", "cannotChangeStatus": "Cannot change from", "to": "to"}, "stepNavigator": {"noStepsCreated": "No steps created yet", "addFirstStep": "Add First Step", "addStep": "Add Step", "addScenario": "Add <PERSON>", "removeStep": "Remove Step", "removeScenario": "<PERSON><PERSON><PERSON>", "deleteStep": "Delete Step", "deleteStepConfirm": "Are you sure you want to delete the step", "deleteScenario": "Delete Scenario", "deleteScenarioConfirm": "Are you sure you want to delete the scenario", "changesOnlySaved": "This will only be saved when you save the template.", "useArrowKeys": "Step Navigator - Use arrow keys to navigate", "step": "Step", "scenario": "<PERSON><PERSON><PERSON>"}, "detailsContent": {"noStagesYet": "No Stages Yet", "noStagesDescription": "This SOP template doesn't have any stages. Get started by creating your first stage to define the workflow steps.", "addFirstStage": "Add First Stage", "enterEditMode": "Enter edit mode to add stages", "stepDetails": "Step Details"}, "scenarioContent": {"noStepsYet": "No Steps Yet", "noStepsDescription": "This step doesn't have any scenarios. Add your first scenario to define the workflow.", "addFirstScenario": "Add First Scenario", "enterEditModeScenarios": "Enter edit mode to add scenarios", "pleaseSelectStep": "Please select a step to view its content"}, "scriptSection": {"script": "<PERSON><PERSON><PERSON>", "similarity": "Similarity", "content": "Content", "contentEng": "Content (Eng)", "enterScriptContent": "Enter script content...", "dropdownArrow": "Dropdown Arrow"}, "statusDialog": {"returnTemplate": "Return Template", "changeStatusTo": "Change Status to", "provideReason": "Please provide a reason for returning this template:", "addComment": "Add a comment (optional):", "requiredReason": "Required reason for returning", "optionalComment": "Optional comment", "cancel": "Cancel", "confirm": "Confirm"}, "actionButtons": {"edit": "Edit", "save": "Save", "saving": "Saving...", "cancel": "Cancel", "submitForApproval": "Submit for Approval", "approve": "Approve", "return": "Return"}}, "dictionary": {"createDictionary": "Create Dictionary", "dictionaryName": "Dictionary Name", "enterDictionaryName": "Enter dictionary name", "version": "Version", "enterVersion": "Enter version (e.g., v20250617)", "noDataFound": "No dictionary entries found", "create": "Create", "cancel": "Cancel"}, "metadata": {"createMetadata": "Create Metada<PERSON>", "metadataName": "Metadata Name", "enterMetadataName": "Enter metadata name", "version": "Version", "enterVersion": "Enter version (e.g., v20250617)", "noDataFound": "No metadata found", "create": "Create", "cancel": "Cancel", "title": "<PERSON><PERSON><PERSON>"}, "sttKeywords": {"createKeyword": "Create STT Keyword", "keywordName": "STT Keyword Name", "enterKeywordName": "Enter STT keyword name", "version": "Version", "enterVersion": "Enter version (e.g., v20250617)", "noDataFound": "No STT keywords found", "create": "Create", "cancel": "Cancel"}, "sensitiveKeywords": {"createKeyword": "Create Sensitive Keyword", "keywordName": "Sensitive Keyword Name", "enterKeywordName": "Enter sensitive keyword name", "version": "Version", "enterVersion": "Enter version (e.g., v20250617)", "noDataFound": "No sensitive keywords found", "create": "Create", "cancel": "Cancel"}, "searchFilter": {"search": "Search...", "status": "Status", "statusPlaceholder": "Select status"}, "searchBar": {"selectField": "Select field...", "searchByPrefix": "Search by", "searchByField": "Search by field...", "noFieldSelected": "field"}, "status": {"status": "Status", "draft": "Draft", "approved": "Approved", "pendingApproval": "Pending Approval", "returned": "Returned", "active": "Active", "inactive": "Inactive"}, "tableColumns": {"id": "ID", "formId": "Form ID", "name": "Name", "status": "Status", "version": "Version", "formVersion": "Version", "product": "Product", "createdBy": "Created By", "createBy": "Created By", "approvedBy": "Approved By", "createdDate": "Created Date", "createTime": "Created Date", "lastUpdated": "Last Updated", "updateTime": "Last Updated", "language": "Language"}, "sop": {"title": "SOP: ELI Public Offer", "changeScore": "Change Score", "edit": "Edit", "back": "Back", "save": "Save", "columns": {"step": "Step", "scenarioId": "Scenario ID", "content": "Content", "doSentenceSimilarity": "Sentence Similarity", "scoreToPassed": "Score to Pass"}, "yes": "Yes", "no": "No"}, "metaDataMapping": {"title": "Metadata Mapping", "key": "Key", "value": "Value", "display": "Display"}, "contentMapping": {"fields": {"keyDefinition": "Key", "value": "Value", "englishDisplay": "English Display", "mandarinDisplay": "Putonghua Display", "displayValue": "Display", "standardScriptId": "Standard Script ID", "regexFormula": "Regex Formula", "useModel": "Use Model", "source": "Source", "category": "Category", "formId": "Form ID", "remark": "Remark", "type": "Type", "language": "Language", "platform": "Platform", "tenant": "Tenant", "createdBy": "Created By", "updatedBy": "Updated By", "createTime": "Created Time", "updateTime": "Updated Time"}, "searchableFields": {"key": "Key", "value": "Value", "cantoneseDisplay": "Cantonese Display", "englishDisplay": "English Display", "putonghuaDisplay": "Putonghua Display", "regexFormula": "Regex Formula"}, "typeOptions": {"sensitive_word": "Sensitive Word", "data_dict": "Data Dictionary", "additional_recording": "Additional Recording", "customer_response": "Customer Response", "important_word": "Important Word"}, "popupModals": {"saveConfirmation": {"title": "Confirm Save Changes", "message": "Are you sure you want to save the changes to this row?", "cancel": "Cancel", "confirm": "Confirm Save"}, "deleteConfirmation": {"title": "Confirm Delete Row", "message": "Are you sure you want to permanently delete this row? This action cannot be undone.", "cancel": "Cancel", "confirm": "Delete"}, "addRow": {"tooltip": "Add new mapping", "disabledTooltip": "Please save or cancel the current editing row before adding a new row"}, "editRow": {"tooltip": "Edit row", "disabledTooltip": "Please save or cancel the current editing row before editing this row"}, "deleteRow": {"tooltip": "Delete row", "disabledTooltip": "Please save or cancel the current editing row before deleting this row"}, "actions": {"saveChanges": "Save changes", "cancelEditing": "Cancel editing"}}}, "actionButtons": {"edit": "Edit", "duplicate": "Duplicate", "bulkImport": "Bulk Import", "exitEditing": "Exit Editing", "publish": "Publish", "return": "Return", "approve": "Approve", "cancel": "Cancel", "confirmPublish": "Confirm Publish", "publishConfirmation": {"title": "Confirm Publish", "message": "Are you sure you want to publish this item? Once published, it will be available to use.", "confirmButton": "Confirm Publish"}, "tooltips": {"incompleteRows": "Please complete or confirm all table rows before exiting", "exitEditingMode": "Exit editing mode"}, "confirmNameChange": "Confirm Name Change", "confirmNameChangeMessage": "Are you sure you want to change the name?", "confirmVersionChange": "Confirm Version Change", "confirmVersionChangeMessage": "Are you sure you want to change the version?", "from": "From", "to": "To", "confirmChange": "Confirm Change", "saveTitle": "Save title", "editTitle": "Edit title", "editOnlyDraft": "Can only edit when status is Draft", "versionPlaceholder": "Version Name", "saveVersion": "Save version", "editVersion": "Edit version"}, "bulkImportModal": {"title": "Bulk Import", "downloadTemplate": "Download Template", "chooseFile": "Choose <PERSON>", "dragAndDrop": "Drag and drop your CSV file here", "orClickChooseFile": "or click \"Choose File\" to select from your computer", "supportedFormats": "Supported formats: CSV files", "preview": "Preview", "rows": "rows", "clearAll": "Clear All", "noDataToPreview": "No data to preview", "importData": "Import Data", "importing": "Importing...", "actions": "Actions", "removeRow": "Remove row", "success": "Success", "itemsImportedSuccessfully": "items imported successfully", "importFailed": "Import Failed", "failedToImportData": "Failed to import data", "unknownErrorOccurred": "Unknown error occurred"}, "duplicateModal": {"title": "Duplicate <PERSON>em", "createNewVersion": "Create a new version of", "withDataCopied": "with all its data copied.", "currentVersion": "Current Version:", "newVersionName": "New Version Name", "enterNewVersionName": "Enter new version name", "versionNameRequired": "Version name is required", "versionMustBeDifferent": "New version must be different from current version", "cancel": "Cancel", "creating": "Creating...", "createDuplicate": "Create"}, "breadcrumb": {"category": "Category", "sopCategories": "SOP Categories"}, "error": {"title": "Error", "retry": "Retry"}}, "callPatch": {"recordingId": "Recording ID", "userId": "User ID", "userName": "User Name", "branchCode": "Branch Code", "saReferenceNumber": "SA Reference Number", "customerCIF": "Customer CIF", "investmentOrderType": "Investment Order Type", "investmentAccount": "Investment Account", "language": "Language", "officerCode": "Officer Code", "productName1": "Product Series No. 1", "productName2": "Product Series No. 2", "productName3": "Product Series No. 3", "cashSettlementAccountNumber": "Cash Settlement Account Number", "remoteNumber": "Remote Number", "localNumber": "Local Number"}}, "ctint-mf-user-admin": {"test": "test:en", "templateHome": {"title": "CDSS 3.0 Microfrontend Template", "desc": "This is a template project for microfrontend development in CDSS 3.0. Click below button to go to example detail page.", "btnLabel": "Go to Detail"}, "templateDetail": {"title": "CDSS 3.0 Microfrontend Template - Detail Page", "desc": "This is the detail page of template project for microfrontend development in CDSS 3.0.", "btnLabel": "Go back to Main Page"}, "langDemo": {"languageDemo": "Language Demo", "currentLanguage": "Current Language", "changeLanguage": "Change Language", "changeTo": "Change to"}, "filter": {"search": "Search", "clear": "Clear", "name": "Filters here"}, "tab": {"user": "User", "userGroup": "User Groups", "queueGroup": "Queue Groups", "newQueueGroup": "New Queue Group", "newUserGroup": "New User Group", "new": "New User"}, "formAction": {"discard": "Discard", "save": "Save", "edit": "Edit", "notAllowEdit": "Not Allow to Edit"}, "form": {"password": "Password", "passworPlacehoder": "Please enter your password", "invaildRequired": "Please input the necessary field"}}}