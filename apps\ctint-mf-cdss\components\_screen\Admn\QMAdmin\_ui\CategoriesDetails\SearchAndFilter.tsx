import React from 'react';
import { Search } from 'lucide-react';
import Input from '@cdss-modules/design-system/components/_ui/Input';
import { Select } from '@cdss-modules/design-system/components/_ui/Select';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';

interface SearchAndFilterProps {
  searchValue: string;
  setSearchValue: (value: string) => void;
  statusFilter: string;
  setStatusFilter: (value: string) => void;
  statusOptions: Array<{
    id: string;
    value: string;
    label: string;
  }>;
}

export const SearchAndFilter: React.FC<SearchAndFilterProps> = ({
  searchValue,
  setSearchValue,
  statusFilter,
  setStatusFilter,
  statusOptions,
}) => {
  const { t } = useTranslation();

  return (
    <div className="flex justify-between items-center gap-2">
      <div className="relative w-80">
        <Input
          placeholder={t('ctint-mf-cdss.qmAdmin.searchFilter.search')}
          value={searchValue}
          onChange={(value) => setSearchValue(value as string)}
          className="pl-10"
          size="s"
        />
        <Search
          className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
          size={20}
        />
      </div>

      <div className="flex items-center gap-2">
        <div className="w-48">
          <Select
            placeholder={t(
              'ctint-mf-cdss.qmAdmin.searchFilter.statusPlaceholder'
            )}
            mode="single"
            options={statusOptions}
            showSearch={false}
            isPagination={false}
            value={statusFilter}
            onChange={(value) => setStatusFilter(value as string)}
          />
        </div>
      </div>
    </div>
  );
};
