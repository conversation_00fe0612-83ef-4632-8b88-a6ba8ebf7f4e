'use client';

import React, { useState } from 'react';

import { useSOPTemplateStore } from '../../../../../../../_store/sopTemplateStore';
import ImportantWordsView from './ImportantWordsView';
import ImportantWordsEdit from './ImportantWordsEdit';

export const ImportantWordsSection = () => {
  const {
    getCurrentStepData,
    currentStep,
    currentScenario,
    updateStepField,
    isEditing,
  } = useSOPTemplateStore();
  const stepData = getCurrentStepData();
  const [isInEditMode, setIsInEditMode] = useState(false);

  if (!stepData) {
    return null;
  }

  const handleEdit = () => {
    setIsInEditMode(true);
  };

  const handleSave = (newImportantWords: string) => {
    // updateStepField(
    //   currentStep,
    //   currentScenario,
    //   'importantWords',
    //   newImportantWords
    // );
    setIsInEditMode(false);
  };

  const handleCancel = () => {
    setIsInEditMode(false);
  };

  return (
    <div className="border rounded-md p-4">
      <div className="flex justify-between items-center mb-3">
        <h3 className="font-medium text-lg">Important Words</h3>
        {isEditing && !isInEditMode && (
          <button
            className="text-blue-600 hover:text-blue-800 text-sm font-medium"
            onClick={handleEdit}
          >
            Edit
          </button>
        )}
      </div>

      {/* {isEditing && isInEditMode ? (
        <ImportantWordsEdit
          importantWords={stepData.importantWords}
          onSave={handleSave}
          onCancel={handleCancel}
        />
      ) : (
        <ImportantWordsView importantWords={stepData.importantWords} />
      )} */}
    </div>
  );
};

export default ImportantWordsSection;
