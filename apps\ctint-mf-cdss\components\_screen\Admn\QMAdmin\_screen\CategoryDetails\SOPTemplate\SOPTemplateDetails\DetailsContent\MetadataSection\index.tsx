'use client';

import React, { useState } from 'react';

import { useSOPTemplateStore } from '../../../../../../_store/sopTemplateStore';
import MetadataView from './MetadataView';
import MetadataEdit from './MetadataEdit';

export const MetadataSection = () => {
  const { isEditing } = useSOPTemplateStore();
  const { getCurrentStepData, currentStep, currentScenario, updateStepField } =
    useSOPTemplateStore();
  const stepData = getCurrentStepData();
  const [isInEditMode, setIsInEditMode] = useState(false);

  if (!stepData) {
    return null;
  }

  const handleEdit = () => {
    setIsInEditMode(true);
  };

  const handleSave = (newMetadata: any[]) => {
    // updateStepField(currentStep, currentScenario, 'metadata', newMetadata);
    setIsInEditMode(false);
  };

  const handleCancel = () => {
    setIsInEditMode(false);
  };

  return (
    <div className="border rounded-md p-4 mt-6">
      <div className="flex justify-between items-center mb-3">
        <h3 className="font-medium text-lg">Metadata</h3>
        {isEditing && !isInEditMode && (
          <button
            className="text-blue-600 hover:text-blue-800 text-sm font-medium"
            onClick={handleEdit}
          >
            Edit
          </button>
        )}
      </div>

      {/* {isEditing && isInEditMode ? (
        <MetadataEdit
          metadata={stepData.metadata || []}
          onSave={handleSave}
          onCancel={handleCancel}
        />
      ) : (
        <MetadataView metadata={stepData.metadata || []} />
      )} */}
    </div>
  );
};

export default MetadataSection;
