'use client';

import React, { useState } from 'react';

import { useSOPTemplateStore } from '../../../../../../../_store/sopTemplateStore';
import ClientResponseView from './ClientResponseView';
import ClientResponseEdit from './ClientResponseEdit';

export const ClientResponseSection = () => {
  const {
    getCurrentStepData,
    currentStep,
    currentScenario,
    updateStepField,
    isEditing,
  } = useSOPTemplateStore();
  const stepData = getCurrentStepData();
  const [isInEditMode, setIsInEditMode] = useState(false);

  if (!stepData) {
    return null;
  }

  const handleEdit = () => {
    setIsInEditMode(true);
  };

  const handleSave = (newClientResponse: string) => {
    // updateStepField(
    //   currentStep,
    //   currentScenario,
    //   'clientResponse',
    //   newClientResponse
    // );
    setIsInEditMode(false);
  };

  const handleCancel = () => {
    setIsInEditMode(false);
  };

  return (
    <div className="border rounded-md p-4">
      <div className="flex justify-between items-center mb-3">
        <h3 className="font-medium text-lg">Client Response</h3>
        {isEditing && !isInEditMode && (
          <button
            className="text-blue-600 hover:text-blue-800 text-sm font-medium"
            onClick={handleEdit}
          >
            Edit
          </button>
        )}
      </div>

      {/* {isEditing && isInEditMode ? (
        <ClientResponseEdit
          clientResponse={stepData.clientResponse}
          onSave={handleSave}
          onCancel={handleCancel}
        />
      ) : (
        <ClientResponseView clientResponse={stepData.clientResponse} />
      )} */}
    </div>
  );
};

export default ClientResponseSection;
