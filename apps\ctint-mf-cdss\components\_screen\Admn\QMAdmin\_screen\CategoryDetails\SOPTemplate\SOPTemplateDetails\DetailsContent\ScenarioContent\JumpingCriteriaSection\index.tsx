'use client';

import React, { useState } from 'react';

import { useSOPTemplateStore } from '../../../../../../../_store/sopTemplateStore';
import JumpingCriteriaView from './JumpingCriteriaView';
import JumpingCriteriaEdit from './JumpingCriteriaEdit';

export const JumpingCriteriaSection = () => {
  const {
    getCurrentStepData,
    currentStep,
    currentScenario,
    updateStepField,
    isEditing,
  } = useSOPTemplateStore();
  const stepData = getCurrentStepData();
  const [isInEditMode, setIsInEditMode] = useState(false);

  if (!stepData) {
    return null;
  }

  const handleEdit = () => {
    setIsInEditMode(true);
  };

  // const handleSave = (newJumpingCriteria: string) => {
  //   updateStepField(
  //     currentStep,
  //     currentScenario,
  //     'jumpingCriteria',
  //     newJumpingCriteria
  //   );
  //   setIsInEditMode(false);
  // };

  const handleCancel = () => {
    setIsInEditMode(false);
  };

  return (
    <div className="border rounded-md p-4">
      <div className="flex justify-between items-center mb-3">
        <h3 className="font-medium text-lg">Jumping Criteria</h3>
        {isEditing && !isInEditMode && (
          <button
            className="text-blue-600 hover:text-blue-800 text-sm font-medium"
            onClick={handleEdit}
          >
            Edit
          </button>
        )}
      </div>

      {/* {isEditing && isInEditMode ? (
        <JumpingCriteriaEdit
          jumpingCriteria={stepData.jumpingCriteria}
          onSave={handleSave}
          onCancel={handleCancel}
        />
      ) : (
        <JumpingCriteriaView jumpingCriteria={stepData.jumpingCriteria} />
      )} */}
    </div>
  );
};

export default JumpingCriteriaSection;
