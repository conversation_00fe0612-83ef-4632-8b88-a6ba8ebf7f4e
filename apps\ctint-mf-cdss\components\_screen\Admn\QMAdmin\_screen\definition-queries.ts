import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from '@cdss-modules/design-system';
import { useRouteHandler } from '@cdss-modules/design-system';

// Import fire functions
import {
  fireGetQMAdministrationList,
  fireCreateQMAdministrationItem,
  fireGetQMAdministrationItemById,
  fireUpdateQMAdministrationItem,
  fireDeleteQMAdministrationItem,
} from '@cdss/lib/api';

// Import content mapping functions for duplication
import {
  fetchContentMappingList,
  multiCreateContentMappingItems,
  type ContentMappingType,
  type UnifiedMappingRequest,
  type UnifiedMappingMultiCreateRequest,
} from './content-mapping-queries';

import type { SOPTemplateData } from '../types';

export interface CategoryData {
  id: number;
  name: string;
  relationshipType: string;
  status: string;
  version: string;
  tenant: string;
  createTime: string;
  updateTime: string;
  deleteTime: string;
  createdBy: string;
  updatedBy: string;
  approvedBy: string;
}

export interface STTKeywordData {
  id: number;
  parentId: number;
  name: string;
  tenant: string;
  relationshipType: string;
  platform: string;
  status: 'Draft' | 'Pending Approval' | 'Approved' | 'Returned';
  version: string;
  createTime: string;
  updateTime: string;
  deleteTime: string;
  createdBy: string;
  updatedBy: string;
  approvedBy: string;
  updateUser: string;
  createUser: string;
  approveUser: string;
  userName: string;
}

// ======================================
// DEFINITION TYPES AND INTERFACES
// ======================================

// Relationship type enum for better type safety and maintainability
export type RelationshipType =
  | 'category'
  | 'sopTemplate'
  | 'metadata'
  | 'dictionary'
  | 'sttKeyword'
  | 'sensitiveKeyword';

// Single definition interface based on the API response structure
export interface UnifiedDefinitionItem {
  id: string;
  parentId: string;
  name: string;
  tenant: string;
  relationshipType: RelationshipType;
  platform: string;
  status: string;
  version: string;
  createTime: string;
  updateTime: string;
  deleteTime: string;
  createBy: string;
  updateBy: string;
  approveBy: string;
  userName: string;
}

// Unified request interface for listings
export interface UnifiedListingRequest {
  page: number;
  pageSize: number;
  order: 'ASC' | 'DESC';
  orderBy: string;
  relationshipType: RelationshipType;
  parentId?: string; // Optional and string type as expected by API
  name?: string; // Optional search parameter
  status?: string; // Optional status filter
}

// Unified response interface - generic to handle different data types
export interface UnifiedListingResponse<T = any> {
  data: {
    items: T[];
    totalCount: number;
    totalPage: number;
  };
  error: string;
  isSuccess: boolean;
}

// Unified single definition response interface - now uses specific UnifiedDefinitionItem type
export interface UnifiedDefinitionByIdResponse {
  data: UnifiedDefinitionItem;
  error: string;
  isSuccess: boolean;
}

// Unified create request interface
export interface UnifiedCreateRequest {
  name: string;
  relationshipType: RelationshipType;
  status?: string;
  version?: string;
  parentId?: string; // Optional for categories, string type as expected by API
  platform?: string; // For STT keywords
}

// Unified create response interface
export interface UnifiedCreateResponse<T = any> {
  data: T;
  error: string;
  isSuccess: boolean;
}

// Unified update status request interface
export interface UnifiedUpdateStatusRequest {
  status: string;
}

// Unified update status response interface
export interface UnifiedUpdateStatusResponse {
  data: UnifiedDefinitionItem;
  error: string;
  isSuccess: boolean;
}

// Unified update fields request interface
export interface UnifiedUpdateFieldsRequest {
  name?: string;
  version?: string;
}

// Unified update fields response interface
export interface UnifiedUpdateFieldsResponse {
  data: UnifiedDefinitionItem;
  error: string;
  isSuccess: boolean;
}

// Unified delete response interface
export interface UnifiedDeleteResponse {
  data: null;
  error: string;
  isSuccess: boolean;
}

// ======================================
// DEFINITION API FUNCTIONS
// ======================================

// Unified fetch function for all definition listings
export const fetchUnifiedDefinitionList = async <T = any>(
  request: UnifiedListingRequest,
  basePath = ''
): Promise<UnifiedListingResponse<T>> => {
  try {
    const requestBody = {
      page: request.page,
      pageSize: request.pageSize,
      order: request.order,
      orderBy: request.orderBy,
      relationshipType: request.relationshipType,
      ...(request.parentId && { parentId: request.parentId }),
      ...(request.name && { name: request.name }),
      ...(request.status &&
        request.status !== 'status' && { status: request.status }),
    };

    console.log('Unified API request:', {
      body: requestBody,
    });

    const response = await fireGetQMAdministrationList(requestBody, basePath);
    const result: UnifiedListingResponse<T> = response.data;

    console.log('Unified API response:', result);

    if (!result.isSuccess) {
      throw new Error(result.error || 'API request failed');
    }

    console.log('Processed unified API result:', {
      relationshipType: request.relationshipType,
      dataLength: result.data.items?.length || 0,
      totalItems: result.data.totalCount || 0,
      totalPages: result.data.totalPage || 0,
      requestedPage: request.page,
      requestedPageSize: request.pageSize,
    });

    return result;
  } catch (error) {
    console.error('Error fetching unified definition list:', error);
    throw error;
  }
};

// Unified fetch function for single definition by ID
export const fetchUnifiedDefinitionById = async (
  id: string,
  basePath = ''
): Promise<UnifiedDefinitionByIdResponse> => {
  try {
    console.log('Unified Definition By ID API request:', {
      id: id,
    });

    const response = await fireGetQMAdministrationItemById(id, basePath);
    const result: UnifiedDefinitionByIdResponse = response.data;

    console.log('Unified Definition By ID API response:', result);

    if (!result.isSuccess) {
      throw new Error(result.error || 'API request failed');
    }

    console.log('Processed unified definition by ID result:', {
      definitionId: result.data?.id || 'unknown',
      relationshipType: result.data?.relationshipType || 'unknown',
    });

    return result;
  } catch (error) {
    console.error('Error fetching unified definition by ID:', error);
    throw error;
  }
};

// Unified create function for all definition types
export const createUnifiedDefinition = async <T = any>(
  request: UnifiedCreateRequest,
  basePath = ''
): Promise<UnifiedCreateResponse<T>> => {
  try {
    const requestBody = {
      name: request.name,
      relationshipType: request.relationshipType,
      ...(request.status && { status: request.status }),
      ...(request.version && { version: request.version }),
      ...(request.parentId && { parentId: request.parentId }),
      ...(request.platform && { platform: request.platform }),
    };

    console.log('Unified Create API request:', {
      body: requestBody,
    });

    const response = await fireCreateQMAdministrationItem(
      requestBody,
      basePath
    );
    const result: UnifiedCreateResponse<T> = response.data;

    console.log('Unified Create API response:', result);

    if (!result.isSuccess) {
      const error = new Error(result.error || 'API request failed');
      // Attach the full API response to the error for better debugging
      (error as any).apiResponse = result;
      throw error;
    }

    console.log('Processed unified create API result:', {
      relationshipType: request.relationshipType,
      createdId: (result.data as any)?.id || 'unknown',
    });

    return result;
  } catch (error) {
    console.error('Error creating unified definition:', error);
    throw error;
  }
};

// Unified update status function for definitions
export const updateUnifiedDefinitionStatus = async (
  id: string,
  request: UnifiedUpdateStatusRequest,
  basePath = ''
): Promise<UnifiedUpdateStatusResponse> => {
  try {
    const requestBody = {
      status: request.status,
    };

    console.log('Unified Update Status API request:', {
      body: requestBody,
    });

    const response = await fireUpdateQMAdministrationItem(
      id,
      requestBody,
      basePath
    );
    const result: UnifiedUpdateStatusResponse = response.data;

    console.log('Unified Update Status API response:', result);

    if (!result.isSuccess) {
      throw new Error(result.error || 'API request failed');
    }

    console.log('Processed unified update status API result:', {
      fullResult: result,
      updatedId: result.data?.id || 'unknown',
      newStatus: result.data?.status || 'unknown',
      dataExists: !!result.data,
    });

    return result;
  } catch (error) {
    console.error('Error updating unified definition status:', error);
    throw error;
  }
};

// Unified update fields function for definitions
export const updateUnifiedDefinitionFields = async (
  id: string,
  request: UnifiedUpdateFieldsRequest,
  basePath = ''
): Promise<UnifiedUpdateFieldsResponse> => {
  try {
    const requestBody = {
      ...(request.name && { name: request.name }),
      ...(request.version && { version: request.version }),
    };

    console.log('Unified Update Fields API request:', {
      body: requestBody,
    });

    const response = await fireUpdateQMAdministrationItem(
      id,
      requestBody,
      basePath
    );
    const result: UnifiedUpdateFieldsResponse = response.data;

    console.log('Unified Update Fields API response:', result);

    if (!result.isSuccess) {
      throw new Error(result.error || 'API request failed');
    }

    console.log('Processed unified update fields API result:', {
      updatedId: result.data?.id || 'unknown',
      updatedName: result.data?.name || 'unknown',
      updatedVersion: result.data?.version || 'unknown',
    });

    return result;
  } catch (error) {
    console.error('Error updating unified definition fields:', error);
    throw error;
  }
};

// Unified delete function for definitions
export const deleteUnifiedDefinition = async (
  id: string,
  basePath = ''
): Promise<UnifiedDeleteResponse> => {
  try {
    console.log('Unified Definition Delete API request:', {
      id: id,
    });

    const response = await fireDeleteQMAdministrationItem(id, basePath);
    const result: UnifiedDeleteResponse = response.data;

    console.log('Unified Definition Delete API response:', result);

    if (!result.isSuccess) {
      throw new Error(result.error || 'API request failed');
    }

    console.log('Processed unified definition delete API result:', {
      deletedId: id,
    });

    return result;
  } catch (error) {
    console.error('Error deleting unified definition:', error);
    throw error;
  }
};

// ======================================
// DEFINITION REACT QUERY HOOKS
// ======================================

// Unified React Query hook for all definition listings
export const useUnifiedDefinitionListQuery = <T = any>(
  relationshipType: RelationshipType,
  parentId?: string,
  search = '',
  statusFilter = '',
  page = 1,
  pageSize = 10,
  orderBy = 'createTime',
  order: 'ASC' | 'DESC' = 'DESC'
) => {
  const { basePath } = useRouteHandler();

  return useQuery({
    queryKey: [
      'unifiedDefinitionList',
      relationshipType,
      parentId,
      search,
      statusFilter,
      page,
      pageSize,
      orderBy,
      order,
    ],
    queryFn: async () => {
      const request: UnifiedListingRequest = {
        page,
        pageSize,
        order,
        orderBy,
        relationshipType,
        ...(parentId && { parentId }), // Only include parentId if it exists
        ...(search && { name: search }),
        ...(statusFilter && { status: statusFilter }),
      };

      return await fetchUnifiedDefinitionList<T>(request, basePath);
    },
    retry: 3,
    retryDelay: 1000,
    enabled: !!relationshipType, // Only run if relationshipType is provided, parentId is optional for categories
  });
};

// Unified React Query hook for creating definitions
export const useUnifiedCreateDefinitionMutation = <T = any>() => {
  const queryClient = useQueryClient();
  const { basePath } = useRouteHandler();

  return useMutation({
    mutationFn: (request: UnifiedCreateRequest) =>
      createUnifiedDefinition<T>(request, basePath),
    onSuccess: (data, variables) => {
      // Invalidate and refetch the unified definition list for this relationship type
      queryClient.invalidateQueries({
        queryKey: ['unifiedDefinitionList', variables.relationshipType],
      });

      // Show success toast
      toast({
        variant: 'success',
        title: 'Success',
        description: `${variables.relationshipType} created successfully`,
      });
    },
    onError: (error: any, variables) => {
      // console.error(`Failed to create ${variables.relationshipType}:`, error);

      // // Log the full error object to debug
      // console.log('Full error object:', error);

      // Extract the API error message
      let errorMessage = error.message;

      // If the error has the attached API response, extract it
      if (error.apiResponse?.error) {
        errorMessage = error.apiResponse.error;
      } else if (error.response?.data?.error) {
        errorMessage = error.response.data.error;
      } else if (error.cause?.response?.data?.error) {
        errorMessage = error.cause.response.data.error;
      }

      // Print the extracted error for debugging
      // console.log('Extracted error message:', errorMessage);

      // Show error toast notification
      toast({
        variant: 'error',
        title: 'Error',
        description: `Failed to create ${variables.relationshipType}: ${errorMessage}`,
      });
    },
  });
};

// Unified React Query hook for fetching single definition by ID
export const useUnifiedDefinitionByIdQuery = (id: string, enabled = true) => {
  const { basePath } = useRouteHandler();

  return useQuery({
    queryKey: ['unifiedDefinitionById', id],
    queryFn: async () => {
      return await fetchUnifiedDefinitionById(id, basePath);
    },
    retry: 3,
    retryDelay: 1000,
    enabled: !!id && enabled, // Only run if ID is provided and enabled is true
  });
};

// Unified React Query hook for updating definition status
export const useUnifiedUpdateDefinitionStatusMutation = () => {
  const queryClient = useQueryClient();
  const { basePath } = useRouteHandler();

  return useMutation({
    mutationFn: ({
      id,
      request,
    }: {
      id: string;
      request: UnifiedUpdateStatusRequest;
    }) => updateUnifiedDefinitionStatus(id, request, basePath),
    onSuccess: (data, variables) => {
      // Invalidate and refetch relevant queries
      queryClient.invalidateQueries({
        queryKey: ['unifiedDefinitionById', variables.id],
      });
      queryClient.invalidateQueries({
        queryKey: ['unifiedDefinitionList'],
      });

      // Show success toast with safe access to response data
      const newStatus =
        data?.data?.status || variables.request.status || 'updated';
      toast({
        variant: 'success',
        title: 'Success',
        description: `Status updated to ${newStatus} successfully`,
      });
    },
    onError: (error: Error, variables) => {
      console.error(`Failed to update status for ID ${variables.id}:`, error);

      // Show error toast notification
      toast({
        variant: 'error',
        title: 'Error',
        description: `Failed to update status: ${error.message}`,
      });
    },
  });
};

// Unified React Query hook for updating definition fields
export const useUnifiedUpdateDefinitionFieldsMutation = () => {
  const queryClient = useQueryClient();
  const { basePath } = useRouteHandler();

  return useMutation({
    mutationFn: ({
      id,
      request,
    }: {
      id: string;
      request: UnifiedUpdateFieldsRequest;
    }) => updateUnifiedDefinitionFields(id, request, basePath),
    onSuccess: (data, variables) => {
      // Invalidate and refetch relevant queries
      queryClient.invalidateQueries({
        queryKey: ['unifiedDefinitionById', variables.id],
      });
      queryClient.invalidateQueries({
        queryKey: ['unifiedDefinitionList'],
      });
      // Also invalidate mapping lists since they might depend on the definition data
      queryClient.invalidateQueries({
        queryKey: ['contentMappingList'],
      });

      // Show success toast
      const updatedFields = [];
      if (variables.request.name) updatedFields.push('name');
      if (variables.request.version) updatedFields.push('version');

      toast({
        variant: 'success',
        title: 'Success',
        description: `${updatedFields.join(' and ')} updated successfully`,
      });
    },
    onError: (error: Error, variables) => {
      console.error(`Failed to update fields for ID ${variables.id}:`, error);

      // Show error toast notification
      toast({
        variant: 'error',
        title: 'Error',
        description: `Failed to update fields: ${error.message}`,
      });
    },
  });
};

// Unified React Query hook for deleting definitions
export const useUnifiedDeleteDefinitionMutation = () => {
  const queryClient = useQueryClient();
  const { basePath } = useRouteHandler();

  return useMutation({
    mutationFn: (id: string) => deleteUnifiedDefinition(id, basePath),
    onSuccess: (data, variables) => {
      // Invalidate and refetch relevant queries
      queryClient.invalidateQueries({
        queryKey: ['unifiedDefinitionById', variables],
      });
      queryClient.invalidateQueries({
        queryKey: ['unifiedDefinitionList'],
      });

      // Show success toast
      toast({
        variant: 'success',
        title: 'Success',
        description: 'Item deleted successfully',
      });
    },
    onError: (error: Error, variables) => {
      console.error(`Failed to delete definition with ID ${variables}:`, error);

      // Show error toast notification
      toast({
        variant: 'error',
        title: 'Error',
        description: `Failed to delete item: ${error.message}`,
      });
    },
  });
};

// ======================================
// TYPE-SPECIFIC HOOKS USING UNIFIED APPROACH
// ======================================

// Categories
export const useCategoriesUnifiedQuery = (
  parentId?: string, // Categories might not need parentId
  search = '',
  statusFilter = '',
  page = 1,
  pageSize = 10,
  orderBy = 'createTime',
  order: 'ASC' | 'DESC' = 'DESC'
) => {
  return useUnifiedDefinitionListQuery<CategoryData>(
    'category',
    parentId, // This will be undefined for categories, which is correct
    search,
    statusFilter,
    page,
    pageSize,
    orderBy,
    order
  );
};

// SOP Templates
export const useSOPTemplatesUnifiedQuery = (
  parentId: string,
  search = '',
  statusFilter = '',
  page = 1,
  pageSize = 10,
  orderBy = 'createTime',
  order: 'ASC' | 'DESC' = 'DESC'
) => {
  return useUnifiedDefinitionListQuery<SOPTemplateData>(
    'sopTemplate',
    parentId,
    search,
    statusFilter,
    page,
    pageSize,
    orderBy,
    order
  );
};

// Metadata
export const useMetadataUnifiedQuery = (
  parentId: string,
  search = '',
  statusFilter = '',
  page = 1,
  pageSize = 10,
  orderBy = 'createTime',
  order: 'ASC' | 'DESC' = 'DESC'
) => {
  return useUnifiedDefinitionListQuery(
    'metadata',
    parentId,
    search,
    statusFilter,
    page,
    pageSize,
    orderBy,
    order
  );
};

// Dictionary
export const useDictionaryUnifiedQuery = (
  parentId: string,
  search = '',
  statusFilter = '',
  page = 1,
  pageSize = 10,
  orderBy = 'createTime',
  order: 'ASC' | 'DESC' = 'DESC'
) => {
  return useUnifiedDefinitionListQuery(
    'dictionary',
    parentId,
    search,
    statusFilter,
    page,
    pageSize,
    orderBy,
    order
  );
};

// STT Keywords
export const useSTTKeywordsUnifiedQuery = (
  parentId: string,
  search = '',
  statusFilter = '',
  page = 1,
  pageSize = 10,
  orderBy = 'createTime',
  order: 'ASC' | 'DESC' = 'DESC'
) => {
  return useUnifiedDefinitionListQuery<STTKeywordData>(
    'sttKeyword',
    parentId,
    search,
    statusFilter,
    page,
    pageSize,
    orderBy,
    order
  );
};

// Sensitive Keywords
export const useSensitiveKeywordsUnifiedQuery = (
  parentId: string,
  search = '',
  statusFilter = '',
  page = 1,
  pageSize = 10,
  orderBy = 'createTime',
  order: 'ASC' | 'DESC' = 'DESC'
) => {
  return useUnifiedDefinitionListQuery<STTKeywordData>(
    'sensitiveKeyword',
    parentId,
    search,
    statusFilter,
    page,
    pageSize,
    orderBy,
    order
  );
};

// ======================================
// DUPLICATE DEFINITION FUNCTIONALITY
// ======================================

// Duplicate request interface
export interface DuplicateDefinitionRequest {
  originalId: string;
  newVersion: string;
  parentId?: string;
  relationshipType: RelationshipType;
  contentType: ContentMappingType;
}

// Duplicate response interface
export interface DuplicateDefinitionResponse {
  newDefinitionId: string;
  mappingsCopied: number;
}

// Duplicate definition with all mappings using direct API calls
export const duplicateDefinitionWithMappings = async (
  request: DuplicateDefinitionRequest,
  basePath = ''
): Promise<DuplicateDefinitionResponse> => {
  try {
    // Step 1: Get original definition data
    console.log('Fetching original definition:', request.originalId);
    const originalDefinitionResponse = await fetchUnifiedDefinitionById(
      request.originalId,
      basePath
    );

    if (
      !originalDefinitionResponse.isSuccess ||
      !originalDefinitionResponse.data
    ) {
      throw new Error('Failed to fetch original definition');
    }

    const originalDefinition = originalDefinitionResponse.data;

    // Step 2: Create new definition with new version
    console.log('Creating new definition with version:', request.newVersion);
    const createResult = await createUnifiedDefinition(
      {
        name: originalDefinition.name, // Keep the same name
        relationshipType: request.relationshipType,
        version: request.newVersion,
        parentId: request.parentId,
        status: 'Draft', // Start as draft
      },
      basePath
    );

    if (!createResult.data?.id) {
      throw new Error('Failed to create new definition');
    }

    const newDefinitionId = createResult.data.id;
    console.log('New definition created with ID:', newDefinitionId);

    // Step 3: Get all mappings from original definition
    console.log('Fetching mappings from original definition');
    const mappingsRequest: UnifiedMappingRequest = {
      page: 1,
      pageSize: 1000, // Get all mappings (adjust if needed)
      order: 'DESC',
      orderBy: 'createTime',
      relationshipType: request.relationshipType,
      relationshipId: request.originalId,
    };

    const mappingsResponse = await fetchContentMappingList(
      request.contentType,
      mappingsRequest,
      basePath
    );

    if (!mappingsResponse.isSuccess) {
      throw new Error('Failed to fetch original mappings');
    }

    const originalMappings = mappingsResponse.data.items || [];
    console.log('Found mappings to copy:', originalMappings.length);

    // Step 4: Create new mappings for the new definition using bulk create
    let mappingsCopied = 0;
    if (originalMappings.length > 0) {
      const mappingItems = originalMappings.map((mapping) => ({
        keyDefinition: mapping.keyDefinition || '',
        value: mapping.value || '',
        language: mapping.language || 'zh',
        displayValue: mapping.displayValue || '',
        relationshipId: newDefinitionId, // Point to new definition
        englishDisplay: mapping.englishDisplay || '',
        mandarinDisplay: mapping.mandarinDisplay || '',
        regexFormula: mapping.regexFormula || '',
        useModel: mapping.useModel || false,
        source: mapping.source || '',
        category: mapping.category || '',
        formId: mapping.formId || '',
        remark: mapping.remark || '',
        type: mapping.type || 'data_dict',
      }));

      const multiCreateRequest: UnifiedMappingMultiCreateRequest = {
        items: mappingItems,
      };

      // Use bulk create for better performance
      await multiCreateContentMappingItems(
        request.contentType,
        multiCreateRequest,
        basePath
      );

      mappingsCopied = originalMappings.length;
      console.log(`Successfully bulk created ${mappingsCopied} mappings`);
    }

    return {
      newDefinitionId,
      mappingsCopied,
    };
  } catch (error) {
    console.error('Error duplicating definition with mappings:', error);
    throw error;
  }
};

// React Query hook for duplicating definitions with mappings
export const useDuplicateDefinitionMutation = () => {
  const queryClient = useQueryClient();
  const { basePath } = useRouteHandler();

  return useMutation({
    mutationFn: (request: DuplicateDefinitionRequest) =>
      duplicateDefinitionWithMappings(request, basePath),
    onSuccess: (data, variables) => {
      // Invalidate relevant queries to refresh the lists
      queryClient.invalidateQueries({
        queryKey: ['unifiedDefinitionList', variables.relationshipType],
      });

      // Also invalidate the specific definition by ID query for the new item
      queryClient.invalidateQueries({
        queryKey: ['unifiedDefinitionById', data.newDefinitionId],
      });

      // Show success toast
      toast({
        variant: 'success',
        title: 'Success',
        description: `Successfully duplicated ${variables.relationshipType} with version "${variables.newVersion}" and ${data.mappingsCopied} mappings`,
      });
    },
    onError: (error: Error, variables) => {
      console.error(
        `Failed to duplicate ${variables.relationshipType}:`,
        error
      );

      // Show error toast notification
      toast({
        variant: 'error',
        title: 'Error',
        description: `Failed to duplicate ${variables.relationshipType}: ${error.message}`,
      });
    },
  });
};
