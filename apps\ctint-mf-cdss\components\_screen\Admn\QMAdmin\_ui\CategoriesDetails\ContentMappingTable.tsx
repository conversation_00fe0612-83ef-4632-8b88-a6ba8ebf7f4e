'use client';

import { Loader } from '@cdss-modules/design-system';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';
import { DataTable } from '@cdss-modules/design-system/components/_ui/DataTable';
import { ColumnDef } from '@tanstack/react-table';
import { useState } from 'react';
import Pagination from '@cdss-modules/design-system/components/_ui/Pagination';
import { EditableContentMappingItem } from '../../_store/contentMappingStore';

export interface ContentMappingTableProps {
  data: EditableContentMappingItem[] | undefined;
  columns: ColumnDef<EditableContentMappingItem>[];
  isLoading: boolean;
  onRowClick?: (item: EditableContentMappingItem) => void;
  currentPage?: number;
  perPage?: number;
  onPageChange?: (page: number) => void;
  onPerPageChange?: (perPage: number) => void;
  totalItems?: number;
  emptyMessage?: string;
}

export const ContentMappingTable: React.FC<ContentMappingTableProps> = ({
  data,
  columns,
  isLoading,
  onRowClick,
  currentPage = 1,
  perPage = 10,
  onPageChange,
  onPerPageChange,
  totalItems = 0,
  emptyMessage = 'No data found',
}) => {
  const { t } = useTranslation();
  const [rowSelection, setRowSelection] = useState({});

  const handleRowClick = (item: EditableContentMappingItem) => {
    if (onRowClick) {
      onRowClick(item);
    }
  };

  return (
    <div className="flex-1 overflow-y-auto h-full flex flex-col p-6">
      <div className="flex-1 h-full overflow-y-auto mb-4">
        <DataTable<EditableContentMappingItem>
          data={data}
          columns={columns}
          loading={isLoading}
          emptyMessage={emptyMessage}
          resize={true}
          onClickRow={(row) => handleRowClick(row.original)}
          rowSelection={rowSelection}
          setRowSelection={setRowSelection}
          pageSizeOnChange={onPerPageChange}
          tableOptions={{
            manualPagination: true,
            pageCount: Math.ceil(totalItems / perPage),
          }}
        />
      </div>

      {/* Custom pagination control */}
      {totalItems > 0 && (
        <Pagination
          current={currentPage}
          perPage={perPage}
          total={Math.ceil(totalItems / perPage)}
          totalCount={totalItems}
          onChange={(page) => onPageChange?.(page)}
          handleOnPrevious={() => {
            if (currentPage > 1) {
              onPageChange?.(currentPage - 1);
            }
          }}
          handleOnNext={() => {
            if (currentPage < Math.ceil(totalItems / perPage)) {
              onPageChange?.(currentPage + 1);
            }
          }}
          handlePerPageSetter={(newPerPage) => {
            if (onPerPageChange) {
              const pageSize =
                typeof newPerPage === 'string'
                  ? parseInt(newPerPage, 10)
                  : newPerPage;
              if (!isNaN(pageSize)) {
                onPerPageChange(pageSize);
              }
            }
          }}
        />
      )}
    </div>
  );
};
