import React, { useMemo } from 'react';
import { Panel, SortingButton } from '@cdss-modules/design-system';
import { DataTable } from '@cdss-modules/design-system/components/_ui/DataTable';
import {
  QueryClient,
  QueryClientProvider,
  useQuery,
} from '@tanstack/react-query';
import { ColumnDef } from '@tanstack/react-table';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';
import { useRouteHandler } from '@cdss-modules/design-system';
import { formatUTCToLocal } from '@cdss-modules/design-system/lib/utils';
import { fireGetRerunHistory } from '../../../../../lib/api';

// Types
interface JobHistoryRecord {
  batchId: string;
  processTime: string;
  timeRangeStart: string;
  timeRangeEnd: string;
  totalCount: number;
  rerunSuccessCount: number;
  rerunFailCount: number;
  username: string;
}

interface JobHistoryData {
  totalCount: number;
  totalPage: number;
  items: JobHistoryRecord[];
}

interface JobHistoryResponse {
  data: JobHistoryData;
  error: string;
  isSuccess: boolean;
}

const QMJobHistoryContent = () => {
  const { t } = useTranslation();
  const { basePath } = useRouteHandler();

  // Define table columns with sorting enabled
  const columns: ColumnDef<JobHistoryRecord>[] = useMemo(
    () => [
      {
        accessorKey: 'batchId',
        header: ({ column }) => {
          return (
            <SortingButton
              sorting={
                column.getIsSorted() === 'asc'
                  ? 'asc'
                  : column.getIsSorted() === 'desc'
                    ? 'desc'
                    : false
              }
              onClick={() => {
                if (column.getIsSorted() === 'asc') {
                  column.toggleSorting(true); // Set to desc
                } else if (column.getIsSorted() === 'desc') {
                  column.clearSorting(); // Clear sorting (return to default)
                } else {
                  column.toggleSorting(false); // Set to asc
                }
              }}
            >
              {t('ctint-mf-cdss.qmAdmin.jobHistory.columns.batchId')}
            </SortingButton>
          );
        },
        cell: ({ row }) => (
          <div className="flex flex-col">
            <span className="font-medium text-sm font-mono">
              {row.original.batchId}
            </span>
          </div>
        ),
        enableSorting: true,
      },
      {
        accessorKey: 'processTime',
        header: ({ column }) => {
          return (
            <SortingButton
              sorting={
                column.getIsSorted() === 'asc'
                  ? 'asc'
                  : column.getIsSorted() === 'desc'
                    ? 'desc'
                    : false
              }
              onClick={() => {
                if (column.getIsSorted() === 'asc') {
                  column.toggleSorting(true); // Set to desc
                } else if (column.getIsSorted() === 'desc') {
                  column.clearSorting(); // Clear sorting (return to default)
                } else {
                  column.toggleSorting(false); // Set to asc
                }
              }}
            >
              {t('ctint-mf-cdss.qmAdmin.jobHistory.columns.processTime')}
            </SortingButton>
          );
        },
        cell: ({ row }) => (
          <span className="text-sm">
            {formatUTCToLocal(row.original.processTime)}
          </span>
        ),
        enableSorting: true,
        sortingFn: 'datetime',
      },
      {
        accessorKey: 'username',
        header: ({ column }) => {
          return (
            <SortingButton
              sorting={
                column.getIsSorted() === 'asc'
                  ? 'asc'
                  : column.getIsSorted() === 'desc'
                    ? 'desc'
                    : false
              }
              onClick={() => {
                if (column.getIsSorted() === 'asc') {
                  column.toggleSorting(true); // Set to desc
                } else if (column.getIsSorted() === 'desc') {
                  column.clearSorting(); // Clear sorting (return to default)
                } else {
                  column.toggleSorting(false); // Set to asc
                }
              }}
            >
              {t('ctint-mf-cdss.qmAdmin.jobHistory.columns.username')}
            </SortingButton>
          );
        },
        cell: ({ row }) => (
          <span className="text-sm">{row.original.username}</span>
        ),
        enableSorting: true,
      },
      {
        accessorKey: 'timeRangeStart',
        header: ({ column }) => {
          return (
            <SortingButton
              sorting={
                column.getIsSorted() === 'asc'
                  ? 'asc'
                  : column.getIsSorted() === 'desc'
                    ? 'desc'
                    : false
              }
              onClick={() => {
                if (column.getIsSorted() === 'asc') {
                  column.toggleSorting(true); // Set to desc
                } else if (column.getIsSorted() === 'desc') {
                  column.clearSorting(); // Clear sorting (return to default)
                } else {
                  column.toggleSorting(false); // Set to asc
                }
              }}
            >
              {t('ctint-mf-cdss.qmAdmin.jobHistory.columns.timeRangeStart')}
            </SortingButton>
          );
        },
        cell: ({ row }) => (
          <span className="text-sm">
            {formatUTCToLocal(row.original.timeRangeStart)}
          </span>
        ),
        enableSorting: true,
        sortingFn: 'datetime',
      },
      {
        accessorKey: 'timeRangeEnd',
        header: ({ column }) => {
          return (
            <SortingButton
              sorting={
                column.getIsSorted() === 'asc'
                  ? 'asc'
                  : column.getIsSorted() === 'desc'
                    ? 'desc'
                    : false
              }
              onClick={() => {
                if (column.getIsSorted() === 'asc') {
                  column.toggleSorting(true); // Set to desc
                } else if (column.getIsSorted() === 'desc') {
                  column.clearSorting(); // Clear sorting (return to default)
                } else {
                  column.toggleSorting(false); // Set to asc
                }
              }}
            >
              {t('ctint-mf-cdss.qmAdmin.jobHistory.columns.timeRangeEnd')}
            </SortingButton>
          );
        },
        cell: ({ row }) => (
          <span className="text-sm">
            {formatUTCToLocal(row.original.timeRangeEnd)}
          </span>
        ),
        enableSorting: true,
        sortingFn: 'datetime',
      },
      {
        accessorKey: 'totalCount',
        header: ({ column }) => {
          return (
            <SortingButton
              sorting={
                column.getIsSorted() === 'asc'
                  ? 'asc'
                  : column.getIsSorted() === 'desc'
                    ? 'desc'
                    : false
              }
              onClick={() => {
                if (column.getIsSorted() === 'asc') {
                  column.toggleSorting(true); // Set to desc
                } else if (column.getIsSorted() === 'desc') {
                  column.clearSorting(); // Clear sorting (return to default)
                } else {
                  column.toggleSorting(false); // Set to asc
                }
              }}
            >
              {t('ctint-mf-cdss.qmAdmin.jobHistory.columns.totalCount')}
            </SortingButton>
          );
        },
        cell: ({ row }) => (
          <span className="text-sm text-center">
            {row.original.totalCount.toLocaleString()}
          </span>
        ),
        enableSorting: true,
        sortingFn: 'alphanumeric',
      },
      {
        accessorKey: 'rerunSuccessCount',
        header: ({ column }) => {
          return (
            <SortingButton
              sorting={
                column.getIsSorted() === 'asc'
                  ? 'asc'
                  : column.getIsSorted() === 'desc'
                    ? 'desc'
                    : false
              }
              onClick={() => {
                if (column.getIsSorted() === 'asc') {
                  column.toggleSorting(true); // Set to desc
                } else if (column.getIsSorted() === 'desc') {
                  column.clearSorting(); // Clear sorting (return to default)
                } else {
                  column.toggleSorting(false); // Set to asc
                }
              }}
            >
              {t('ctint-mf-cdss.qmAdmin.jobHistory.columns.successCount')}
            </SortingButton>
          );
        },
        cell: ({ row }) => (
          <span className="text-sm text-center text-green-600 font-semibold">
            {row.original.rerunSuccessCount.toLocaleString()}
          </span>
        ),
        enableSorting: true,
        sortingFn: 'alphanumeric',
      },
      {
        accessorKey: 'rerunFailCount',
        header: ({ column }) => {
          return (
            <SortingButton
              sorting={
                column.getIsSorted() === 'asc'
                  ? 'asc'
                  : column.getIsSorted() === 'desc'
                    ? 'desc'
                    : false
              }
              onClick={() => {
                if (column.getIsSorted() === 'asc') {
                  column.toggleSorting(true); // Set to desc
                } else if (column.getIsSorted() === 'desc') {
                  column.clearSorting(); // Clear sorting (return to default)
                } else {
                  column.toggleSorting(false); // Set to asc
                }
              }}
            >
              {t('ctint-mf-cdss.qmAdmin.jobHistory.columns.failedCount')}
            </SortingButton>
          );
        },
        cell: ({ row }) => (
          <span
            className={`text-sm text-center ${
              row.original.rerunFailCount > 0
                ? 'text-red-600 font-semibold'
                : ''
            }`}
          >
            {row.original.rerunFailCount.toLocaleString()}
          </span>
        ),
        enableSorting: true,
        sortingFn: 'alphanumeric',
      },
    ],
    [t]
  );

  // Job history query
  const { data: jobHistoryData, isFetching: isLoading } = useQuery({
    queryKey: ['jobHistory'],
    queryFn: async () => {
      const response = await fireGetRerunHistory(
        1,
        50,
        'DESC',
        'processTime',
        basePath
      );
      return response.data;
    },
  });

  return (
    <Panel containerClassName="h-full overflow-hidden flex flex-col">
      <div className="p-6 h-full flex flex-col gap-6">
        {/* Header - Fixed */}
        {/* <div className="flex-shrink-0">
          <h2 className="text-t5 font-bold mb-4 flex items-center gap-2">
            <History size={20} />
            {t('ctint-mf-cdss.qmAdmin.jobHistory.title')}
          </h2>
        </div> */}

        {/* Job History DataTable - Scrollable */}
        <div className="flex-1 min-h-0 overflow-hidden">
          <DataTable
            data={jobHistoryData?.items || []}
            columns={columns}
            loading={isLoading}
            emptyMessage={t('ctint-mf-cdss.qmAdmin.jobHistory.noRecords')}
            paginationConfig={{
              enable: true,
              pageIndex: 0,
              pageSize: 50,
            }}
          />
        </div>
      </div>
    </Panel>
  );
};

// Create a client for this component
const queryClient = new QueryClient();

export default function QMJobHistory() {
  return (
    <QueryClientProvider client={queryClient}>
      <QMJobHistoryContent />
    </QueryClientProvider>
  );
}
