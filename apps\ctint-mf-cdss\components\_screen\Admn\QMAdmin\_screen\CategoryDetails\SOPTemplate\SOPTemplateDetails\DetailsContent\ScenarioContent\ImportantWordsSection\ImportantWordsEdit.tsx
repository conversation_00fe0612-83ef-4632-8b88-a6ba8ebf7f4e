'use client';

import React, { useState } from 'react';
import Button from '@cdss-modules/design-system/components/_ui/Button';

interface ImportantWordsEditProps {
  importantWords: string;
  onSave: (newImportantWords: string) => void;
  onCancel: () => void;
}

export const ImportantWordsEdit: React.FC<ImportantWordsEditProps> = ({
  importantWords,
  onSave,
  onCancel,
}) => {
  const [value, setValue] = useState(importantWords || '');

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setValue(e.target.value);
  };

  const handleSave = () => {
    onSave(value);
  };

  return (
    <div className="space-y-3">
      <textarea
        className="w-full p-2 border rounded-md min-h-[150px]"
        value={value}
        onChange={handleChange}
        placeholder="Enter important words (comma separated)..."
      />
      <div className="flex justify-end space-x-2">
        <Button
          variant="secondary"
          onClick={onCancel}
        >
          Cancel
        </Button>
        <Button
          variant="primary"
          onClick={handleSave}
        >
          Save
        </Button>
      </div>
    </div>
  );
};

export default ImportantWordsEdit;
