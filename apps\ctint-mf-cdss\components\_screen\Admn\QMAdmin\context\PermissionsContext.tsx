import { createContext, useContext, ReactNode } from 'react';
import { useQuery } from '@tanstack/react-query';

// Mock API function - replace with actual API call
// TODO: Replace with real API when ready to implement proper permissions
const fetchUserPermissions = async (
  categoryId: string
): Promise<{ permissions: Permission[]; userId: string }> => {
  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 500));

  // TEMPORARY: Return all permissions for current phase
  // When implementing real API, replace this with actual permission fetching logic
  return {
    permissions: ['create', 'edit', 'approve', 'view'], // All permissions granted temporarily
    userId: 'testing-user', // This should be the real user ID from auth system
  };
};

export type Permission = 'create' | 'edit' | 'approve' | 'view';
export type Status = 'Draft' | 'Pending Approval' | 'Approved' | 'Returned';

type TPermissionsContext = {
  permissions: Permission[];
  userId: string;
  hasPermission: (permission: Permission) => boolean;
  isLoading: boolean;
  error: Error | null;
  // Status-based permission checks
  canEdit: (status: Status, creatorId?: string) => boolean;
  canSubmitForApproval: (status: Status) => boolean;
  canApprove: (status: Status, creatorId?: string) => boolean;
  canReturn: (status: Status) => boolean;
  isOwner: (creatorId?: string) => boolean;
};

const PermissionsContext = createContext<TPermissionsContext | undefined>(
  undefined
);

type TPermissionsProviderProps = {
  children: ReactNode;
  categoryId: string;
};

export const PermissionsProvider = ({
  children,
  categoryId,
}: TPermissionsProviderProps) => {
  const {
    data = { permissions: [], userId: '' },
    isLoading,
    error,
  } = useQuery({
    queryKey: ['userPermissions', categoryId],
    queryFn: () => fetchUserPermissions(categoryId),
  });

  const { permissions, userId } = data;

  const hasPermission = (permission: Permission): boolean => {
    return permissions.includes(permission);
  };

  // Check if current user is the owner
  // TEMPORARY: For current phase, this can be used for logging but won't restrict actions
  const isOwner = (creatorId?: string): boolean => {
    console.log('PermissionsContext - isOwner check:', {
      creatorId,
      userId,
      isOwnerResult: !!creatorId && creatorId === userId,
    });

    return !!creatorId && creatorId === userId;
  };

  // Status-based permission checks
  // TEMPORARY: Modified to allow actions regardless of ownership for current phase
  const canEdit = (status: Status, creatorId?: string): boolean => {
    // TEMPORARY: Allow editing for all users who have edit permission
    // Original logic kept for future reference:
    // (status === 'Draft' && hasEditPermission && isOwnerResult) ||
    // (status === 'Returned' && hasEditPermission && isOwnerResult)

    const hasEditPermission = hasPermission('edit');
    const isOwnerResult = isOwner(creatorId);

    // TEMPORARY: Allow edit if user has edit permission and status allows it
    const canEditResult =
      (status === 'Draft' && hasEditPermission) ||
      (status === 'Returned' && hasEditPermission);

    console.log('PermissionsContext - canEdit check:', {
      status,
      creatorId,
      hasEditPermission,
      isOwnerResult,
      canEditResult,
      note: 'TEMPORARY: Ownership check bypassed for current phase',
    });

    return canEditResult;
  };

  const canSubmitForApproval = (
    status: Status,
    creatorId?: string
  ): boolean => {
    // TEMPORARY: Allow submission regardless of ownership
    // Original logic: isOwner(creatorId) check included
    return (
      (status === 'Draft' || status === 'Returned') && hasPermission('edit')
      // Ownership check temporarily removed: && isOwner(creatorId)
    );
  };

  const canApprove = (status: Status, creatorId?: string): boolean => {
    // TEMPORARY: Allow approval regardless of ownership
    // Original logic: !isOwner(creatorId) check included
    return (
      status === 'Pending Approval' && hasPermission('approve')
      // Ownership restriction temporarily removed: && !isOwner(creatorId)
    );
  };

  const canReturn = (status: Status): boolean => {
    return status === 'Pending Approval' && hasPermission('approve');
  };

  return (
    <PermissionsContext.Provider
      value={{
        permissions,
        userId,
        hasPermission,
        isLoading,
        error,
        canEdit,
        canSubmitForApproval,
        canApprove,
        canReturn,
        isOwner,
      }}
    >
      {children}
    </PermissionsContext.Provider>
  );
};

export const usePermissions = () => {
  const context = useContext(PermissionsContext);
  if (context === undefined) {
    throw new Error('usePermissions must be used within a PermissionsProvider');
  }
  return context;
};
