'use client';

import { Loader, SortingButton } from '@cdss-modules/design-system';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';
import {
  MoreVertical,
  Pencil,
  Trash,
  Users,
  User,
  Eye,
  UserPlus,
  Settings,
  Check,
  X,
} from 'lucide-react';
import { PermissionData } from '../../../types';
import { DataTable } from '@cdss-modules/design-system/components/_ui/DataTable';
import { ColumnDef, Table as TableType, Row } from '@tanstack/react-table';
import { useState } from 'react';
import Checkbox from '@cdss-modules/design-system/components/_ui/Checkbox';
import Pagination from '@cdss-modules/design-system/components/_ui/Pagination';
import Button from '@cdss-modules/design-system/components/_ui/Button';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@cdss-modules/design-system/components/_ui/DropdownMenu';

import {
  Popup,
  PopupContent,
} from '@cdss-modules/design-system/components/_ui/Popup';

export interface PermissionsTableProps {
  data: PermissionData[] | undefined;
  isLoading: boolean;
  onRowClick?: (item: PermissionData) => void;
  totalItems?: number;
  totalPages?: number;
  currentPage?: number;
  perPage?: number;
  onPageChange?: (page: number) => void;
  onPerPageChange?: (value: number) => void;
  orderBy?: string;
  order?: 'ASC' | 'DESC';
  onSort?: (orderBy: string, order: 'ASC' | 'DESC' | undefined) => void;
  onEdit?: (item: PermissionData) => void;
  onDelete?: (id: number) => void;
  onManageMembers?: (group: PermissionData) => void;
}

export const PermissionsTable = ({
  data,
  isLoading,
  onRowClick,
  totalItems = 0,
  totalPages = 0,
  currentPage = 1,
  perPage = 10,
  onPageChange,
  onPerPageChange,
  orderBy,
  order,
  onSort,
  onEdit,
  onDelete,
  onManageMembers,
}: PermissionsTableProps) => {
  const { t } = useTranslation();
  const [rowSelection, setRowSelection] = useState({});
  const [selectedItem, setSelectedItem] = useState<PermissionData | null>(null);
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);

  const handleRowClick = (item: PermissionData) => {
    setSelectedItem(item);
    setIsDetailModalOpen(true);
    if (onRowClick) {
      onRowClick(item);
    }
  };

  const handleEditClick = (item: PermissionData) => {
    if (onEdit) {
      onEdit(item);
    }
  };

  const handleDeleteClick = (id: number) => {
    if (window.confirm('Are you sure you want to delete this user/group?')) {
      if (onDelete) {
        onDelete(id);
      }
    }
  };

  const handleManageMembersClick = (group: PermissionData) => {
    if (onManageMembers) {
      onManageMembers(group);
    }
  };

  const handleHeaderSortClick = (columnId: string) => {
    if (onSort) {
      if (orderBy === columnId) {
        if (order === 'ASC') {
          onSort(columnId, 'DESC');
        } else if (order === 'DESC') {
          onSort('', undefined);
        } else {
          onSort(columnId, 'ASC');
        }
      } else {
        onSort(columnId, 'ASC');
      }
    }
  };

  // Helper function to render permission status with icon
  const renderPermissionStatus = (
    permissions: string[],
    permission: string
  ) => {
    const hasPermission = permissions.includes(permission);
    return (
      <div className="flex justify-center items-center h-full py-2">
        {hasPermission ? (
          <div className="flex items-center justify-center w-6 h-6 rounded-full bg-green-100">
            <Check
              size={14}
              className="text-green-600"
            />
          </div>
        ) : (
          <div className="flex items-center justify-center w-6 h-6 rounded-full bg-gray-100">
            <X
              size={14}
              className="text-gray-400"
            />
          </div>
        )}
      </div>
    );
  };

  const columns: ColumnDef<PermissionData>[] = [
    {
      id: 'select',
      header: ({ table }: any) => (
        <div
          className="bg-white z-30"
          onClick={(e) => e.stopPropagation()}
        >
          <Checkbox
            checked={
              table.getIsAllPageRowsSelected() ||
              (table.getIsSomePageRowsSelected() && 'indeterminate')
            }
            onChange={(e: any) => {
              const isSelected = e?.target.checked;
              table.toggleAllPageRowsSelected(isSelected);
            }}
          />
        </div>
      ),
      cell: ({ row }: any) => (
        <div
          className="z-0"
          onClick={(e) => e.stopPropagation()}
        >
          <Checkbox
            checked={row.getIsSelected()}
            onChange={(e: any) => {
              const isSelected = e?.target.checked;
              row.toggleSelected(isSelected);
            }}
          />
        </div>
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: 'name',
      id: 'name',
      header: ({ column }) => (
        <SortingButton
          sorting={
            orderBy === 'name'
              ? order === 'ASC'
                ? 'asc'
                : order === 'DESC'
                  ? 'desc'
                  : false
              : false
          }
          onClick={() => handleHeaderSortClick('name')}
        >
          Name
        </SortingButton>
      ),
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <div
            className={`p-1 rounded-full ${
              row.original.type === 'Group' ? 'bg-purple-100' : 'bg-blue-100'
            }`}
          >
            {row.original.type === 'Group' ? (
              <Users
                size={14}
                className="text-purple-600"
              />
            ) : (
              <User
                size={14}
                className="text-blue-600"
              />
            )}
          </div>
          <div>
            <div className="font-medium">{row.getValue('name')}</div>
            {row.original.email && (
              <div className="text-xs text-gray-500">{row.original.email}</div>
            )}
          </div>
        </div>
      ),
      enableSorting: true,
    },
    {
      accessorKey: 'type',
      id: 'type',
      header: ({ column }) => (
        <SortingButton
          sorting={
            orderBy === 'type'
              ? order === 'ASC'
                ? 'asc'
                : order === 'DESC'
                  ? 'desc'
                  : false
              : false
          }
          onClick={() => handleHeaderSortClick('type')}
        >
          Type
        </SortingButton>
      ),
      cell: ({ row }) => {
        const type = row.getValue('type') as string;
        return (
          <div
            className={`inline-flex items-center gap-1 px-2 py-1 rounded-md text-xs font-medium ${
              type === 'Group'
                ? 'bg-purple-100 text-purple-800'
                : 'bg-blue-100 text-blue-800'
            }`}
          >
            {type === 'Group' ? <Users size={12} /> : <User size={12} />}
            {type}
          </div>
        );
      },
      enableSorting: true,
    },
    // Individual permission columns
    {
      id: 'create',
      header: () => (
        <div className="w-20 flex justify-center items-center h-full">
          <div className="font-medium text-center">Create</div>
        </div>
      ),
      cell: ({ row }) => (
        <div className="w-20">
          {renderPermissionStatus(row.original.permissions, 'Create')}
        </div>
      ),
      enableSorting: false,
      size: 80,
      minSize: 80,
      maxSize: 80,
    },
    {
      id: 'edit',
      header: () => (
        <div className="w-20 flex justify-center items-center h-full">
          <div className="font-medium text-center">Edit</div>
        </div>
      ),
      cell: ({ row }) => (
        <div className="w-20">
          {renderPermissionStatus(row.original.permissions, 'Edit')}
        </div>
      ),
      enableSorting: false,
      size: 80,
      minSize: 80,
      maxSize: 80,
    },
    {
      id: 'submit',
      header: () => (
        <div className="w-20 flex justify-center items-center h-full">
          <div className="font-medium text-center">Submit</div>
        </div>
      ),
      cell: ({ row }) => (
        <div className="w-20">
          {renderPermissionStatus(row.original.permissions, 'Submit')}
        </div>
      ),
      enableSorting: false,
      size: 80,
      minSize: 80,
      maxSize: 80,
    },
    {
      id: 'approve',
      header: () => (
        <div className="w-20 flex justify-center items-center h-full">
          <div className="font-medium text-center">Approve</div>
        </div>
      ),
      cell: ({ row }) => (
        <div className="w-20">
          {renderPermissionStatus(row.original.permissions, 'Approve')}
        </div>
      ),
      enableSorting: false,
      size: 80,
      minSize: 80,
      maxSize: 80,
    },
    {
      id: 'view',
      header: () => (
        <div className="w-20 flex justify-center items-center h-full">
          <div className="font-medium text-center">View</div>
        </div>
      ),
      cell: ({ row }) => (
        <div className="w-20">
          {renderPermissionStatus(row.original.permissions, 'View')}
        </div>
      ),
      enableSorting: false,
      size: 80,
      minSize: 80,
      maxSize: 80,
    },
    {
      id: 'admin',
      header: () => (
        <div className="w-20 flex justify-center items-center h-full">
          <div className="font-medium text-center">Admin</div>
        </div>
      ),
      cell: ({ row }) => (
        <div className="w-20">
          {renderPermissionStatus(row.original.permissions, 'Admin')}
        </div>
      ),
      enableSorting: false,
      size: 80,
      minSize: 80,
      maxSize: 80,
    },
    {
      id: 'actions',
      header: () => <div className="w-full"></div>,
      cell: ({ row }) => (
        <div
          className="flex gap-x-2 z-0"
          onClick={(e) => e.stopPropagation()}
        >
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <button className="rounded p-1 hover:bg-gray-100">
                <MoreVertical size={18} />
              </button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem
                className="cursor-pointer flex items-center gap-2"
                onClick={() => handleRowClick(row.original)}
              >
                <Eye size={16} />
                <span>View Details</span>
              </DropdownMenuItem>
              <DropdownMenuItem
                className="cursor-pointer flex items-center gap-2"
                onClick={() => handleEditClick(row.original)}
              >
                <Pencil size={16} />
                <span>Edit</span>
              </DropdownMenuItem>
              {row.original.type === 'Group' && (
                <DropdownMenuItem
                  className="cursor-pointer flex items-center gap-2"
                  onClick={() => handleManageMembersClick(row.original)}
                >
                  <Settings size={16} />
                  <span>Manage Members</span>
                </DropdownMenuItem>
              )}
              <DropdownMenuItem
                className="cursor-pointer flex items-center gap-2 text-red-600"
                onClick={() => handleDeleteClick(row.original.id)}
              >
                <Trash size={16} />
                <span>Delete</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      ),
      enableSorting: false,
      enableHiding: false,
    },
  ];

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-10 flex-1">
        <Loader size={64} />
      </div>
    );
  }

  return (
    <>
      <div className="flex-1 overflow-auto h-full flex flex-col p-6">
        <div className="flex-1 h-0">
          <DataTable<PermissionData>
            data={data || []}
            columns={columns}
            loading={isLoading}
            emptyMessage="No users or groups found"
            resize={true}
            onClickRow={(row: Row<PermissionData>) =>
              handleRowClick(row.original)
            }
            rowSelection={rowSelection}
            setRowSelection={setRowSelection}
            pageSizeOnChange={onPerPageChange}
          />
        </div>

        {/* Custom pagination control */}
        {totalItems > 0 && (
          <Pagination
            current={currentPage}
            perPage={perPage}
            total={totalPages}
            totalCount={totalItems}
            onChange={(page) => onPageChange?.(page)}
            handleOnPrevious={() => {
              if (currentPage > 1) {
                onPageChange?.(currentPage - 1);
              }
            }}
            handleOnNext={() => {
              if (currentPage < totalPages) {
                onPageChange?.(currentPage + 1);
              }
            }}
            handlePerPageSetter={(newPerPage) => {
              if (onPerPageChange) {
                const pageSize =
                  typeof newPerPage === 'string'
                    ? parseInt(newPerPage, 10)
                    : newPerPage;
                if (!isNaN(pageSize)) {
                  onPerPageChange(pageSize);
                }
              }
            }}
          />
        )}
      </div>

      {/* Details Modal */}
      <Popup
        open={isDetailModalOpen}
        onOpenChange={setIsDetailModalOpen}
      >
        <PopupContent
          className="max-w-4xl max-h-[80vh] overflow-y-auto"
          title={`${selectedItem?.type} Details: ${selectedItem?.name || ''}`}
        >
          <div className="p-6">
            {selectedItem && (
              <div className="space-y-6">
                {/* Basic Information */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-3">
                    <div>
                      <label className="text-sm font-medium text-gray-700">
                        Name
                      </label>
                      <p className="text-sm text-gray-900 mt-1">
                        {selectedItem.name}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700">
                        Type
                      </label>
                      <div className="mt-1">
                        <div
                          className={`inline-flex items-center gap-1 px-2 py-1 rounded-md text-xs font-medium ${
                            selectedItem.type === 'Group'
                              ? 'bg-purple-100 text-purple-800'
                              : 'bg-blue-100 text-blue-800'
                          }`}
                        >
                          {selectedItem.type === 'Group' ? (
                            <Users size={12} />
                          ) : (
                            <User size={12} />
                          )}
                          {selectedItem.type}
                        </div>
                      </div>
                    </div>
                    {selectedItem.email && (
                      <div>
                        <label className="text-sm font-medium text-gray-700">
                          Email
                        </label>
                        <p className="text-sm text-gray-900 mt-1">
                          {selectedItem.email}
                        </p>
                      </div>
                    )}
                    {selectedItem.description && (
                      <div>
                        <label className="text-sm font-medium text-gray-700">
                          Description
                        </label>
                        <p className="text-sm text-gray-900 mt-1">
                          {selectedItem.description}
                        </p>
                      </div>
                    )}
                  </div>

                  <div className="space-y-3">
                    <div>
                      <label className="text-sm font-medium text-gray-700">
                        Department
                      </label>
                      <p className="text-sm text-gray-900 mt-1">
                        {selectedItem.department || '-'}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700">
                        Created By
                      </label>
                      <p className="text-sm text-gray-900 mt-1">
                        {selectedItem.createdBy}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700">
                        Created Date
                      </label>
                      <p className="text-sm text-gray-900 mt-1">
                        {selectedItem.createdDate}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700">
                        Last Updated
                      </label>
                      <p className="text-sm text-gray-900 mt-1">
                        {selectedItem.lastUpdated}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Permissions */}
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-3 block">
                    Assigned Permissions ({selectedItem.permissions.length})
                  </label>
                  <div className="border rounded-lg p-4 bg-gray-50">
                    <div className="flex flex-wrap gap-2">
                      {selectedItem.permissions.map((permission) => {
                        const permissionColors: Record<string, string> = {
                          Create: 'bg-green-100 text-green-800',
                          Edit: 'bg-blue-100 text-blue-800',
                          Submit: 'bg-yellow-100 text-yellow-800',
                          Approve: 'bg-purple-100 text-purple-800',
                          View: 'bg-gray-100 text-gray-800',
                          Admin: 'bg-red-100 text-red-800',
                        };
                        return (
                          <div
                            key={permission}
                            className={`inline-flex items-center px-3 py-2 rounded-md text-sm font-medium ${
                              permissionColors[permission] ||
                              'bg-gray-100 text-gray-800'
                            }`}
                          >
                            {permission}
                          </div>
                        );
                      })}
                    </div>
                  </div>
                </div>

                {/* Group Members */}
                {selectedItem.type === 'Group' &&
                  selectedItem.members &&
                  selectedItem.members.length > 0 && (
                    <div>
                      <label className="text-sm font-medium text-gray-700 mb-3 block">
                        Group Members ({selectedItem.members.length})
                      </label>
                      <div className="border rounded-lg p-4 bg-gray-50">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                          {selectedItem.members.map((member, index) => (
                            <div
                              key={index}
                              className="flex items-center gap-3 p-3 bg-white rounded-md border"
                            >
                              <div className="p-2 rounded-full bg-blue-100">
                                <User
                                  size={16}
                                  className="text-blue-600"
                                />
                              </div>
                              <div className="flex-1">
                                <p className="font-medium text-sm">{member}</p>
                                <p className="text-xs text-gray-500">Member</p>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}

                {/* Action Buttons */}
                <div className="flex justify-end gap-3 pt-4 border-t">
                  <Button
                    variant="secondary"
                    onClick={() => setIsDetailModalOpen(false)}
                  >
                    Close
                  </Button>
                  <Button
                    variant="secondary"
                    onClick={() => {
                      setIsDetailModalOpen(false);
                      handleEditClick(selectedItem);
                    }}
                  >
                    <Pencil
                      size={16}
                      className="mr-2"
                    />
                    Edit {selectedItem.type}
                  </Button>
                  {selectedItem.type === 'Group' && (
                    <Button
                      variant="primary"
                      onClick={() => {
                        setIsDetailModalOpen(false);
                        handleManageMembersClick(selectedItem);
                      }}
                    >
                      <Settings
                        size={16}
                        className="mr-2"
                      />
                      Manage Members
                    </Button>
                  )}
                </div>
              </div>
            )}
          </div>
        </PopupContent>
      </Popup>
    </>
  );
};
