'use client';

import { Panel } from '@cdss-modules/design-system';
import { useState, useEffect, useMemo } from 'react';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';
import { debounce } from 'lodash';

import { PermissionsTable } from './PermissionsTable';
import { PermissionsHeader } from './PermissionsHeader';
import { PermissionData } from '../../../types';
import { useSorting } from '../../../hooks/useSorting';

interface PermissionsTabProps {
  categoryId: string;
}

// Mock data for demonstration - this will be replaced with real API calls
const generateMockPermissions = (): PermissionData[] => [
  {
    id: 1,
    name: 'QM Admin Team',
    type: 'Group',
    permissions: ['Admin', 'Create', 'Edit', 'Submit', 'Approve', 'View'],
    description: 'Main administrative group for Quality Management',
    members: ['<PERSON>', '<PERSON>', '<PERSON>'],
    department: 'Quality Management',
    createdBy: 'System Admin',
    createdDate: '2024-01-15',
    lastUpdated: '2024-01-20',
  },
  {
    id: 2,
    name: '<PERSON>',
    type: 'User',
    permissions: ['Create', 'Edit', 'Submit'],
    email: '<EMAIL>',
    department: 'Quality Management',
    createdBy: 'John Smith',
    createdDate: '2024-01-16',
    lastUpdated: '2024-01-18',
  },
  {
    id: 3,
    name: 'QM Reviewers',
    type: 'Group',
    permissions: ['Approve', 'View'],
    description:
      'Group responsible for reviewing and approving quality metrics',
    members: ['Sarah Johnson', 'David Brown'],
    department: 'Quality Management',
    createdBy: 'John Smith',
    createdDate: '2024-01-17',
    lastUpdated: '2024-01-19',
  },
  {
    id: 4,
    name: 'Alice Brown',
    type: 'User',
    permissions: ['View'],
    email: '<EMAIL>',
    department: 'Operations',
    createdBy: 'John Smith',
    createdDate: '2024-01-18',
    lastUpdated: '2024-01-18',
  },
  {
    id: 5,
    name: 'Team Leads',
    type: 'Group',
    permissions: ['Create', 'Edit', 'Submit', 'View'],
    description: 'Group for team leaders who manage quality processes',
    members: ['Charlie Wilson', 'Emma Davis', 'Robert Taylor'],
    department: 'Multiple',
    createdBy: 'System Admin',
    createdDate: '2024-01-19',
    lastUpdated: '2024-01-21',
  },
];

export const PermissionsTab = ({ categoryId }: PermissionsTabProps) => {
  const { t } = useTranslation();

  // State management
  const [searchValue, setSearchValue] = useState('');
  const [debouncedSearchValue, setDebouncedSearchValue] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [perPage, setPerPage] = useState(10);
  const [isLoading, setIsLoading] = useState(false);

  // Use the sorting hook
  const { orderBy, order, handleSortChange } = useSorting({
    defaultOrderBy: 'name',
    defaultOrder: 'ASC',
  });

  // Mock data - in real implementation, this would come from API
  const [permissionsData, setPermissionsData] = useState<PermissionData[]>(
    generateMockPermissions()
  );

  // Debounced search
  const debouncedSearch = debounce((value: string) => {
    setDebouncedSearchValue(value);
    setCurrentPage(1); // Reset to first page on search
  }, 400);

  useEffect(() => {
    debouncedSearch(searchValue);
    return () => {
      debouncedSearch.cancel();
    };
  }, [searchValue, debouncedSearch]);

  // Filter and sort data based on search and sorting criteria
  const filteredAndSortedData = useMemo(() => {
    let filtered = permissionsData;

    // Apply search filter
    if (debouncedSearchValue.trim()) {
      const searchLower = debouncedSearchValue.toLowerCase();
      filtered = permissionsData.filter(
        (item) =>
          item.name.toLowerCase().includes(searchLower) ||
          item.type.toLowerCase().includes(searchLower) ||
          (item.description &&
            item.description.toLowerCase().includes(searchLower)) ||
          (item.email && item.email.toLowerCase().includes(searchLower)) ||
          (item.department &&
            item.department.toLowerCase().includes(searchLower)) ||
          item.permissions.some((p) => p.toLowerCase().includes(searchLower)) ||
          (item.members &&
            item.members.some((m) => m.toLowerCase().includes(searchLower))) ||
          item.createdBy.toLowerCase().includes(searchLower)
      );
    }

    // Apply sorting
    if (orderBy) {
      filtered = [...filtered].sort((a, b) => {
        const aValue = a[orderBy as keyof PermissionData];
        const bValue = b[orderBy as keyof PermissionData];

        // Handle undefined values
        if (!aValue && !bValue) return 0;
        if (!aValue) return order === 'ASC' ? -1 : 1;
        if (!bValue) return order === 'ASC' ? 1 : -1;

        if (aValue < bValue) return order === 'ASC' ? -1 : 1;
        if (aValue > bValue) return order === 'ASC' ? 1 : -1;
        return 0;
      });
    }

    return filtered;
  }, [permissionsData, debouncedSearchValue, orderBy, order]);

  // Pagination calculations
  const totalItems = filteredAndSortedData.length;
  const totalPages = Math.ceil(totalItems / perPage);
  const startIndex = (currentPage - 1) * perPage;
  const paginatedData = filteredAndSortedData.slice(
    startIndex,
    startIndex + perPage
  );

  // Handlers
  const handleSearchChange = (value: string) => {
    setSearchValue(value);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handlePerPageChange = (value: number) => {
    setPerPage(value);
    setCurrentPage(1);
  };

  const handleTableSortChange = (
    newOrderBy: string,
    newOrder: 'ASC' | 'DESC' | undefined
  ) => {
    handleSortChange(newOrderBy, newOrder);
    setCurrentPage(1);
  };

  const handleCreateUser = async (values: Record<string, string>) => {
    setIsLoading(true);

    // Mock API call - replace with real implementation
    await new Promise((resolve) => setTimeout(resolve, 1000));

    const permissions = values.permissions
      ? values.permissions
          .split(',')
          .map((p) => p.trim())
          .filter((p) => p)
      : [];

    const newUser: PermissionData = {
      id: Math.max(...permissionsData.map((p) => p.id), 0) + 1,
      name: values.name,
      type: 'User',
      permissions: permissions as (
        | 'Create'
        | 'Edit'
        | 'Submit'
        | 'Approve'
        | 'View'
        | 'Admin'
      )[],
      email: values.email,
      department: values.department || undefined,
      createdBy: 'Current User', // This would come from auth context
      createdDate: new Date().toISOString().split('T')[0],
      lastUpdated: new Date().toISOString().split('T')[0],
    };

    setPermissionsData((prev) => [newUser, ...prev]);
    setIsLoading(false);

    console.log('Created user:', newUser);
  };

  const handleCreateGroup = async (values: Record<string, string>) => {
    setIsLoading(true);

    // Mock API call - replace with real implementation
    await new Promise((resolve) => setTimeout(resolve, 1000));

    const permissions = values.permissions
      ? values.permissions
          .split(',')
          .map((p) => p.trim())
          .filter((p) => p)
      : [];

    const members = values.members
      ? values.members
          .split(',')
          .map((m) => m.trim())
          .filter((m) => m)
      : [];

    const newGroup: PermissionData = {
      id: Math.max(...permissionsData.map((p) => p.id), 0) + 1,
      name: values.name,
      type: 'Group',
      permissions: permissions as (
        | 'Create'
        | 'Edit'
        | 'Submit'
        | 'Approve'
        | 'View'
        | 'Admin'
      )[],
      description: values.description || undefined,
      members: members.length > 0 ? members : undefined,
      department: values.department || undefined,
      createdBy: 'Current User', // This would come from auth context
      createdDate: new Date().toISOString().split('T')[0],
      lastUpdated: new Date().toISOString().split('T')[0],
    };

    setPermissionsData((prev) => [newGroup, ...prev]);
    setIsLoading(false);

    console.log('Created group:', newGroup);
  };

  const handleEditItem = async (item: PermissionData) => {
    // This would open an edit modal or navigate to edit page
    console.log('Edit item:', item);
    // TODO: Implement edit functionality
  };

  const handleDeleteItem = async (id: number) => {
    setIsLoading(true);

    // Mock API call - replace with real implementation
    await new Promise((resolve) => setTimeout(resolve, 500));

    setPermissionsData((prev) => prev.filter((p) => p.id !== id));
    setIsLoading(false);

    console.log('Deleted item with id:', id);
  };

  const handleManageMembers = async (group: PermissionData) => {
    // This would open a members management modal
    console.log('Manage members for group:', group);
    // TODO: Implement member management functionality
  };

  const handleRowClick = (item: PermissionData) => {
    console.log('Row clicked:', item);
    // The table component handles the detail modal internally
  };

  return (
    <Panel containerClassName="h-full overflow-hidden flex flex-col">
      <PermissionsHeader
        searchValue={searchValue}
        setSearchValue={handleSearchChange}
        onCreateUser={handleCreateUser}
        onCreateGroup={handleCreateGroup}
      />
      <PermissionsTable
        data={paginatedData}
        isLoading={isLoading}
        onRowClick={handleRowClick}
        totalItems={totalItems}
        totalPages={totalPages}
        currentPage={currentPage}
        perPage={perPage}
        onPageChange={handlePageChange}
        onPerPageChange={handlePerPageChange}
        orderBy={orderBy}
        order={order}
        onSort={handleTableSortChange}
        onEdit={handleEditItem}
        onDelete={handleDeleteItem}
        onManageMembers={handleManageMembers}
      />
    </Panel>
  );
};

export default PermissionsTab;
