'use client';

import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';

// Generic content mapping item structure based on the API response
export interface ContentMappingItem {
  id: string;
  contentDefinitionId: string;
  standardScriptId?: string;
  type: string;
  keyDefinition?: string | null;
  value: string;
  regexFormula?: string | null;
  useModel?: boolean;
  source?: string;
  category?: string;
  formId?: string;
  remark?: string;
  language?: string;
  displayValue?: string;
  englishDisplay?: string;
  mandarinDisplay?: string;
  platform?: string;
  tenant?: string;
  createdBy?: string;
  updatedBy?: string;
  createTime?: string;
  updateTime?: string;
}

// Editable version for form management
export interface EditableContentMappingItem extends ContentMappingItem {
  isEditing?: boolean;
  originalValues?: Partial<ContentMappingItem>;
  hasChanges?: boolean;
  isNew?: boolean;
  markedForDeletion?: boolean;
  justSaved?: boolean;
}

// Main form data structure
export interface ContentMappingFormData {
  id?: string | null;
  name: string;
  status: string;
  version: string;
  createdBy?: string;
  approvedBy?: string | null;
  lastUpdated?: string;
  type: string; // metadata, script, etc.
  mappings: EditableContentMappingItem[];
  mappingsPagination?: {
    total: number;
    totalPages: number;
    currentPage: number;
    pageSize: number;
  };
}

// Configuration for which fields are relevant for different types
export interface FieldConfiguration {
  [fieldName: string]: {
    visible: boolean;
    required: boolean;
    editable: boolean;
    label?: string;
  };
}

// Helper to create deep copy
const deepCopyFormData = (
  data: ContentMappingFormData
): ContentMappingFormData => {
  return {
    ...data,
    mappings: data.mappings.map((mapping) => ({
      ...mapping,
      originalValues: mapping.originalValues
        ? { ...mapping.originalValues }
        : undefined,
    })),
  };
};

const initialFormState: ContentMappingFormData = {
  name: '',
  status: 'Draft',
  version: '1.0',
  approvedBy: null,
  lastUpdated: '',
  type: 'metadata',
  mappings: [],
};

interface ContentMappingStoreState {
  // Category and context
  categoryId: string | null;
  contentType: string; // metadata, script, etc.

  // Form Data State
  formData: ContentMappingFormData;
  originalFormData: ContentMappingFormData | null;
  isLoading: boolean;
  isSubmitting: boolean;
  isDirty: boolean;
  error: string | null;
  validationErrors: Record<string, string>;
  mappingValidationErrors: Record<string, Record<string, string>>;

  // Bulk import state
  bulkImportData: EditableContentMappingItem[];
  isBulkImportModalOpen: boolean;
  isBulkImportLoading: boolean;
  bulkImportError: string | null;

  // Field configuration for current type
  fieldConfig: FieldConfiguration;

  // Callback for post-save actions
  onSaveSuccess?: (newId?: number) => void;

  // Actions
  setContentType: (type: string) => void;
  setFieldConfiguration: (config: FieldConfiguration) => void;
  createNew: (categoryId: string, type?: string) => void;
  updateMetadataField: <
    K extends keyof Omit<ContentMappingFormData, 'mappings'>,
  >(
    field: K,
    value: ContentMappingFormData[K]
  ) => void;

  // Mapping actions
  addMappingRow: () => void;
  updateMappingField: (
    mappingId: string,
    field: keyof ContentMappingItem,
    value: any
  ) => void;
  removeMappingRow: (mappingId: string) => void;
  toggleMappingEdit: (mappingId: string, isEditing: boolean) => void;
  revertMappingRow: (mappingId: string) => void;
  validateAndSaveRow: (
    mappingId: string,
    formValues: Partial<ContentMappingItem>
  ) => boolean;

  // Global actions
  loadExistingData: (
    mainData: {
      id?: string;
      name: string;
      status: string;
      version: string;
      createdBy?: string;
      approvedBy?: string;
      lastUpdated?: string;
      type?: string;
    },
    mappings: ContentMappingItem[],
    pagination?: {
      total: number;
      totalPages: number;
      currentPage: number;
      pageSize: number;
    }
  ) => void;
  resetForm: () => void;
  setPendingApprovalStatus: () => void;
  setCategoryId: (categoryId: string) => void;
  setOnSaveSuccessCallback: (callback: (newId?: number) => void) => void;
  clear: () => void;

  // Validation helpers
  getRowValidationErrors: (mappingId: string) => Record<string, string>;
  isFieldVisible: (fieldName: string) => boolean;
  isFieldRequired: (fieldName: string) => boolean;
  isFieldEditable: (fieldName: string) => boolean;
  getFieldLabel: (fieldName: string) => string;

  // Helper to check if there are any unsaved new rows
  hasUnsavedNewRows: () => boolean;

  // Helper to check if any row is currently being edited
  hasRowsBeingEdited: () => boolean;

  // Bulk import actions
  openBulkImportModal: () => void;
  closeBulkImportModal: () => void;
  setBulkImportData: (data: EditableContentMappingItem[]) => void;
  updateBulkImportRow: (
    index: number,
    field: keyof ContentMappingItem,
    value: any
  ) => void;
  removeBulkImportRow: (index: number) => void;
  processBulkImport: (multiCreateMutation?: any) => Promise<boolean>;
  downloadTemplate: () => void;
}

// Generate a proper UUID-like string for new mappings
const newMappingId = () => {
  return 'temp-' + crypto.randomUUID();
};

// Default field configurations for different types
const getDefaultFieldConfig = (type: string): FieldConfiguration => {
  switch (type) {
    case 'metadata':
      return {
        keyDefinition: {
          visible: true,
          required: true,
          editable: true,
          label: 'ctint-mf-cdss.qmAdmin.contentMapping.fields.keyDefinition',
        },
        value: {
          visible: true,
          required: false,
          editable: true,
          label: 'ctint-mf-cdss.qmAdmin.contentMapping.fields.value',
        },
        displayValue: {
          visible: true,
          required: false,
          editable: true,
          label: 'ctint-mf-cdss.qmAdmin.contentMapping.fields.displayValue',
        },
        // englishDisplay: {
        //   visible: true,
        //   required: false,
        //   editable: true,
        //   label: 'ctint-mf-cdss.qmAdmin.contentMapping.fields.englishDisplay',
        // },
        // mandarinDisplay: {
        //   visible: true,
        //   required: false,
        //   editable: true,
        //   label: 'ctint-mf-cdss.qmAdmin.contentMapping.fields.mandarinDisplay',
        // },
        standardScriptId: { visible: false, required: false, editable: false },
        regexFormula: { visible: false, required: false, editable: false },
        useModel: { visible: false, required: false, editable: false },
        source: { visible: false, required: false, editable: false },
        category: { visible: false, required: false, editable: false },
        formId: { visible: false, required: false, editable: false },
        remark: { visible: false, required: false, editable: false },
      };
    case 'sttKeyword':
      return {
        keyDefinition: {
          visible: true,
          required: true,
          editable: true,
          label: 'ctint-mf-cdss.qmAdmin.contentMapping.fields.keyDefinition',
        },
        value: {
          visible: true,
          required: false,
          editable: true,
          label: 'ctint-mf-cdss.qmAdmin.contentMapping.fields.value',
        },
        displayValue: {
          visible: true,
          required: false,
          editable: true,
          label: 'ctint-mf-cdss.qmAdmin.contentMapping.fields.displayValue',
        },
        // englishDisplay: {
        //   visible: true,
        //   required: false,
        //   editable: true,
        //   label: 'ctint-mf-cdss.qmAdmin.contentMapping.fields.englishDisplay',
        // },
        // mandarinDisplay: {
        //   visible: true,
        //   required: false,
        //   editable: true,
        //   label: 'ctint-mf-cdss.qmAdmin.contentMapping.fields.mandarinDisplay',
        // },
        standardScriptId: { visible: false, required: false, editable: false },
        regexFormula: { visible: false, required: false, editable: false },
        useModel: { visible: false, required: false, editable: false },
        source: { visible: false, required: false, editable: false },
        category: { visible: false, required: false, editable: false },
        formId: { visible: false, required: false, editable: false },
        remark: { visible: false, required: false, editable: false },
      };
    case 'sensitiveKeyword':
      return {
        keyDefinition: {
          visible: true,
          required: true,
          editable: true,
          label: 'ctint-mf-cdss.qmAdmin.contentMapping.fields.keyDefinition',
        },
        type: {
          visible: true,
          required: true,
          editable: true,
          label: 'ctint-mf-cdss.qmAdmin.contentMapping.fields.type',
        },
        value: {
          visible: true,
          required: false,
          editable: true,
          label: 'ctint-mf-cdss.qmAdmin.contentMapping.fields.value',
        },
        // mandarinDisplay: {
        //   visible: true,
        //   required: false,
        //   editable: true,
        //   label: 'ctint-mf-cdss.qmAdmin.contentMapping.fields.mandarinDisplay',
        // },
        displayValue: {
          visible: true,
          required: false,
          editable: true,
          label: 'ctint-mf-cdss.qmAdmin.contentMapping.fields.displayValue',
        },
        // englishDisplay: {
        //   visible: true,
        //   required: false,
        //   editable: true,
        //   label: 'ctint-mf-cdss.qmAdmin.contentMapping.fields.englishDisplay',
        // },
        standardScriptId: { visible: false, required: false, editable: false },
        regexFormula: { visible: false, required: false, editable: false },
        useModel: { visible: false, required: false, editable: false },
        source: { visible: false, required: false, editable: false },
        category: { visible: false, required: false, editable: false },
        formId: { visible: false, required: false, editable: false },
        remark: { visible: false, required: false, editable: false },
      };
    case 'dictionary':
      return {
        keyDefinition: {
          visible: true,
          required: true,
          editable: true,
          label: 'ctint-mf-cdss.qmAdmin.contentMapping.fields.keyDefinition',
        },
        // type: {
        //   visible: true,
        //   required: true,
        //   editable: true,
        //   label: 'ctint-mf-cdss.qmAdmin.contentMapping.fields.type',
        // },
        type: { visible: false, required: false, editable: false },
        value: {
          visible: true,
          required: false,
          editable: true,
          label: 'ctint-mf-cdss.qmAdmin.contentMapping.fields.value',
        },
        regexFormula: {
          visible: true,
          required: false,
          editable: true,
          label: 'ctint-mf-cdss.qmAdmin.contentMapping.fields.regexFormula',
        },
        useModel: {
          visible: true,
          required: false,
          editable: true,
          label: 'ctint-mf-cdss.qmAdmin.contentMapping.fields.useModel',
        },
        displayValue: {
          visible: true,
          required: false,
          editable: true,
          label: 'ctint-mf-cdss.qmAdmin.contentMapping.fields.displayValue',
        },
        // englishDisplay: {
        //   visible: true,
        //   required: false,
        //   editable: true,
        //   label: 'ctint-mf-cdss.qmAdmin.contentMapping.fields.englishDisplay',
        // },

        // mandarinDisplay: {
        //   visible: true,
        //   required: false,
        //   editable: true,
        //   label: 'ctint-mf-cdss.qmAdmin.contentMapping.fields.mandarinDisplay',
        // },
        // keyDefinition: { visible: false, required: false, editable: false },
        standardScriptId: { visible: false, required: false, editable: false },
        source: { visible: false, required: false, editable: false },
        category: { visible: false, required: false, editable: false },
        formId: { visible: false, required: false, editable: false },
        remark: { visible: false, required: false, editable: false },
      };
    default:
      // Show all fields by default for unknown types
      return {
        value: {
          visible: true,
          required: true,
          editable: true,
          label: 'ctint-mf-cdss.qmAdmin.contentMapping.fields.value',
        },
        keyDefinition: {
          visible: true,
          required: false,
          editable: true,
          label: 'ctint-mf-cdss.qmAdmin.contentMapping.fields.keyDefinition',
        },
        englishDisplay: {
          visible: true,
          required: false,
          editable: true,
          label: 'ctint-mf-cdss.qmAdmin.contentMapping.fields.englishDisplay',
        },
        mandarinDisplay: {
          visible: true,
          required: false,
          editable: true,
          label: 'ctint-mf-cdss.qmAdmin.contentMapping.fields.mandarinDisplay',
        },
        displayValue: {
          visible: true,
          required: false,
          editable: true,
          label: 'ctint-mf-cdss.qmAdmin.contentMapping.fields.displayValue',
        },
        standardScriptId: {
          visible: true,
          required: false,
          editable: true,
          label: 'ctint-mf-cdss.qmAdmin.contentMapping.fields.standardScriptId',
        },
        regexFormula: {
          visible: true,
          required: false,
          editable: true,
          label: 'ctint-mf-cdss.qmAdmin.contentMapping.fields.regexFormula',
        },
        useModel: {
          visible: true,
          required: false,
          editable: true,
          label: 'ctint-mf-cdss.qmAdmin.contentMapping.fields.useModel',
        },
        source: {
          visible: true,
          required: false,
          editable: true,
          label: 'ctint-mf-cdss.qmAdmin.contentMapping.fields.source',
        },
        category: {
          visible: true,
          required: false,
          editable: true,
          label: 'ctint-mf-cdss.qmAdmin.contentMapping.fields.category',
        },
        formId: {
          visible: true,
          required: false,
          editable: true,
          label: 'ctint-mf-cdss.qmAdmin.contentMapping.fields.formId',
        },
        remark: {
          visible: true,
          required: false,
          editable: true,
          label: 'ctint-mf-cdss.qmAdmin.contentMapping.fields.remark',
        },
      };
  }
};

export const useContentMappingStore = create<ContentMappingStoreState>()(
  immer(
    (set, get): ContentMappingStoreState => ({
      // Initial State
      categoryId: null,
      contentType: 'metadata',
      formData: { ...initialFormState },
      originalFormData: null,
      isLoading: false,
      isSubmitting: false,
      isDirty: false,
      error: null,
      validationErrors: {},
      mappingValidationErrors: {},
      bulkImportData: [],
      isBulkImportModalOpen: false,
      isBulkImportLoading: false,
      bulkImportError: null,
      fieldConfig: getDefaultFieldConfig('metadata'),

      setContentType: (type) =>
        set((state) => {
          state.contentType = type;
          state.formData.type = type;
          state.fieldConfig = getDefaultFieldConfig(type);
        }),

      setFieldConfiguration: (config) =>
        set((state) => {
          state.fieldConfig = config;
        }),

      setCategoryId: (categoryId) =>
        set((state) => {
          state.categoryId = categoryId;
        }),

      createNew: (categoryId, type = 'metadata') =>
        set((state) => {
          state.categoryId = categoryId;
          state.contentType = type;
          state.fieldConfig = getDefaultFieldConfig(type);
          const newForm: ContentMappingFormData = {
            ...initialFormState,
            type,
            mappings: [],
          };
          state.formData = newForm;
          state.originalFormData = deepCopyFormData(newForm);
          state.isDirty = false;
          state.error = null;
          state.validationErrors = {};
          state.mappingValidationErrors = {};
          state.isLoading = false;
        }),

      loadExistingData: (mainData, mappings, pagination) =>
        set((state) => {
          const type = mainData.type || 'metadata';
          state.contentType = type;
          state.fieldConfig = getDefaultFieldConfig(type);

          console.log('Store: Loading existing data with mappings:', mappings);

          const loadedFormData: ContentMappingFormData = {
            id: mainData.id,
            name: mainData.name,
            status: mainData.status,
            version: mainData.version,
            createdBy: mainData.createdBy,
            approvedBy: mainData.approvedBy || null,
            lastUpdated: mainData.lastUpdated,
            type,
            mappings: mappings.map((m) => {
              const mappingWithId = {
                ...m,
                // Ensure every mapping has an ID - generate one if missing
                id: m.id || newMappingId(),
                isEditing: false,
                originalValues: { ...m },
                hasChanges: false,
                isNew: false,
                markedForDeletion: false,
              };

              console.log(
                'Store: Processing mapping with ID:',
                mappingWithId.id,
                'Original ID:',
                m.id
              );

              return mappingWithId;
            }),
            mappingsPagination: pagination,
          };

          console.log('Store: Final loaded form data:', loadedFormData);

          state.formData = loadedFormData;
          state.originalFormData = deepCopyFormData(loadedFormData);
          state.isDirty = false;
          state.error = null;
          state.validationErrors = {};
          state.mappingValidationErrors = {};
        }),

      updateMetadataField: (field, value) =>
        set((state) => {
          state.formData[field] = value;
          state.isDirty = true;
          if (state.validationErrors[field]) {
            delete state.validationErrors[field];
          }
        }),

      addMappingRow: () =>
        set((state) => {
          const newId = newMappingId();
          // Set default type based on content type
          const defaultType =
            state.contentType === 'dictionary'
              ? 'data_dict'
              : state.contentType === 'sensitiveKeyword'
                ? 'sensitive_word'
                : state.contentType;

          const newRow: EditableContentMappingItem = {
            id: newId,
            contentDefinitionId: '0',
            type: defaultType,
            value: '',
            keyDefinition: '',
            englishDisplay: '',
            mandarinDisplay: '',
            displayValue: '',
            standardScriptId: '',
            regexFormula: '',
            useModel: false,
            source: '',
            category: '',
            formId: '',
            remark: '',
            language: 'en',
            platform: 'web',
            tenant: 'ctint',
            createdBy: '',
            updatedBy: '',
            isEditing: true,
            originalValues: undefined,
            hasChanges: false,
            isNew: true,
            markedForDeletion: false,
          };
          state.formData.mappings.unshift(newRow);
          state.isDirty = true;
        }),

      updateMappingField: (mappingId, field, value) =>
        set((state) => {
          const mappingIndex = state.formData.mappings.findIndex(
            (m) => m.id === mappingId
          );
          if (mappingIndex !== -1) {
            const mapping = state.formData.mappings[mappingIndex];

            // Special handling for useModel field to ensure it's always a boolean
            if (field === 'useModel') {
              (mapping as any)[field] = Boolean(value);
            } else {
              (mapping as any)[field] = value;
            }

            if (mapping.originalValues) {
              let hasActualChanges = false;
              for (const key in mapping.originalValues) {
                const typedKey = key as keyof ContentMappingItem;
                if (
                  mapping.originalValues[typedKey] !==
                  (mapping as any)[typedKey]
                ) {
                  hasActualChanges = true;
                  break;
                }
              }
              mapping.hasChanges = hasActualChanges;
            } else {
              mapping.hasChanges = true;
            }
            state.isDirty = true;

            if (
              state.mappingValidationErrors[mappingId] &&
              state.mappingValidationErrors[mappingId][field]
            ) {
              delete state.mappingValidationErrors[mappingId][field];
              if (
                Object.keys(state.mappingValidationErrors[mappingId]).length ===
                0
              ) {
                delete state.mappingValidationErrors[mappingId];
              }
            }
          }
        }),

      removeMappingRow: (mappingId) =>
        set((state) => {
          const mappingIndex = state.formData.mappings.findIndex(
            (m) => m.id === mappingId
          );
          if (mappingIndex !== -1) {
            if (state.formData.mappings[mappingIndex].isNew) {
              state.formData.mappings.splice(mappingIndex, 1);
            } else {
              state.formData.mappings[mappingIndex].markedForDeletion =
                !state.formData.mappings[mappingIndex].markedForDeletion;
              state.formData.mappings[mappingIndex].isEditing = false;
            }
            state.isDirty = true;
          }
        }),

      toggleMappingEdit: (mappingId, isEditing) =>
        set((state) => {
          const mapping = state.formData.mappings.find(
            (m) => m.id === mappingId
          );
          if (mapping && !mapping.markedForDeletion) {
            mapping.isEditing = isEditing;
            if (!isEditing && mapping.originalValues) {
              let hasActualChanges = false;
              for (const key in mapping.originalValues) {
                const typedKey = key as keyof ContentMappingItem;
                if (
                  mapping.originalValues[typedKey] !==
                  (mapping as any)[typedKey]
                ) {
                  hasActualChanges = true;
                  break;
                }
              }
              mapping.hasChanges = hasActualChanges;
            }
          }
        }),

      revertMappingRow: (mappingId) =>
        set((state) => {
          const mappingIndex = state.formData.mappings.findIndex(
            (m) => m.id === mappingId
          );
          if (mappingIndex !== -1) {
            const mapping = state.formData.mappings[mappingIndex];
            if (mapping.isNew) {
              state.formData.mappings.splice(mappingIndex, 1);
            } else if (mapping.originalValues) {
              state.formData.mappings[mappingIndex] = {
                ...(mapping.originalValues as ContentMappingItem),
                id: mapping.id,
                isEditing: false,
                originalValues: mapping.originalValues,
                hasChanges: false,
                isNew: false,
                markedForDeletion: false,
              };
            }

            let overallDirty = false;
            for (const m of state.formData.mappings) {
              if (m.hasChanges || m.markedForDeletion || m.isNew) {
                overallDirty = true;
                break;
              }
            }
            state.isDirty = overallDirty;
          }
        }),

      resetForm: () =>
        set((state) => {
          if (state.originalFormData) {
            state.formData = deepCopyFormData(state.originalFormData);
          } else {
            const freshFormData: ContentMappingFormData = {
              ...initialFormState,
              type: state.contentType,
              mappings: [],
            };
            state.formData = freshFormData;
          }
          state.isDirty = false;
          state.validationErrors = {};
          state.mappingValidationErrors = {};
        }),

      setPendingApprovalStatus: () =>
        set((state) => {
          if (state.formData) {
            state.formData.status = 'Pending Approval';
            state.isDirty = true;
          }
        }),

      clear: () =>
        set((state) => {
          state.formData = { ...initialFormState, type: state.contentType };
          state.originalFormData = null;
          state.isDirty = false;
          state.error = null;
          state.validationErrors = {};
          state.mappingValidationErrors = {};
          state.isLoading = false;
          state.isSubmitting = false;
        }),

      setOnSaveSuccessCallback: (callback) =>
        set((state) => {
          state.onSaveSuccess = callback;
        }),

      validateAndSaveRow: (mappingId, formValues) => {
        const { formData, fieldConfig, contentType } = get();
        const mapping = formData.mappings.find((m) => m.id === mappingId);
        if (!mapping) return false;

        // Validate based on field configuration
        const errors: Record<string, string> = {};
        Object.keys(fieldConfig).forEach((fieldName) => {
          const config = fieldConfig[fieldName];
          if (config.required && config.visible) {
            let value = (formValues as any)[fieldName];

            // If no value in formValues, check the existing mapping data
            if (!value || (typeof value === 'string' && !value.trim())) {
              value = (mapping as any)[fieldName];
            }

            // Apply default values for specific fields if still empty
            if (!value || (typeof value === 'string' && !value.trim())) {
              if (fieldName === 'type') {
                if (contentType === 'dictionary') {
                  value = 'data_dict'; // Apply default for dictionary type field
                } else if (contentType === 'sensitiveKeyword') {
                  value = 'sensitive_word'; // Apply default for sensitive keyword type field
                }
              }
            }

            // Final validation check
            if (!value || (typeof value === 'string' && !value.trim())) {
              errors[fieldName] = `${config.label || fieldName} is required`;
            } else {
              // If we found a default value, add it to formValues for saving
              (formValues as any)[fieldName] = value;
            }
          }
        });

        set((state) => {
          if (state.mappingValidationErrors[mappingId]) {
            delete state.mappingValidationErrors[mappingId];
          }

          if (Object.keys(errors).length > 0) {
            state.mappingValidationErrors[mappingId] = errors;
            return;
          }

          const mappingIndex = state.formData.mappings.findIndex(
            (m) => m.id === mappingId
          );
          if (mappingIndex !== -1) {
            const mapping = state.formData.mappings[mappingIndex];
            Object.assign(mapping, formValues);

            if (mapping.originalValues) {
              let hasActualChanges = false;
              for (const key in mapping.originalValues) {
                const typedKey = key as keyof ContentMappingItem;
                if (
                  mapping.originalValues[typedKey] !==
                  (mapping as any)[typedKey]
                ) {
                  hasActualChanges = true;
                  break;
                }
              }
              mapping.hasChanges = hasActualChanges;
            } else {
              mapping.hasChanges = true;
            }

            mapping.isEditing = false;
            state.isDirty = true;
          }
        });

        return Object.keys(errors).length === 0;
      },

      getRowValidationErrors: (mappingId) => {
        const { mappingValidationErrors } = get();
        return mappingValidationErrors[mappingId] || {};
      },

      isFieldVisible: (fieldName) => {
        const { fieldConfig } = get();
        return fieldConfig[fieldName]?.visible ?? true;
      },

      isFieldRequired: (fieldName) => {
        const { fieldConfig } = get();
        return fieldConfig[fieldName]?.required ?? false;
      },

      isFieldEditable: (fieldName) => {
        const { fieldConfig } = get();
        return fieldConfig[fieldName]?.editable ?? true;
      },

      getFieldLabel: (fieldName) => {
        const { fieldConfig } = get();
        return fieldConfig[fieldName]?.label ?? fieldName;
      },

      // Helper to check if there are any unsaved new rows
      hasUnsavedNewRows: () => {
        const { formData } = get();
        return formData.mappings.some(
          (mapping) => mapping.isNew && mapping.isEditing
        );
      },

      // Helper to check if any row is currently being edited
      hasRowsBeingEdited: () => {
        const { formData } = get();
        return formData.mappings.some((mapping) => mapping.isEditing);
      },

      // Bulk import actions
      openBulkImportModal: () =>
        set((state) => {
          state.isBulkImportModalOpen = true;
          state.bulkImportError = null;
        }),

      closeBulkImportModal: () =>
        set((state) => {
          state.isBulkImportModalOpen = false;
          state.bulkImportData = [];
          state.bulkImportError = null;
        }),

      setBulkImportData: (data) =>
        set((state) => {
          state.bulkImportData = data.map((item, index) => ({
            ...item,
            // Ensure type field has a default value based on content type
            type:
              item.type ||
              (state.contentType === 'dictionary'
                ? 'data_dict'
                : state.contentType === 'sensitiveKeyword'
                  ? 'sensitive_word'
                  : state.contentType),
            // id: Date.now() + index, // Generate temporary IDs
            isNew: true,
            isEditing: false,
            hasChanges: false,
            markedForDeletion: false,
          }));
        }),

      updateBulkImportRow: (index, field, value) =>
        set((state) => {
          if (state.bulkImportData[index]) {
            // Special handling for useModel field to ensure it's always a boolean
            if (field === 'useModel') {
              (state.bulkImportData[index] as any)[field] = Boolean(value);
            } else {
              (state.bulkImportData[index] as any)[field] = value;
            }
          }
        }),

      removeBulkImportRow: (index) =>
        set((state) => {
          state.bulkImportData.splice(index, 1);
        }),

      processBulkImport: async (multiCreateMutation?: any) => {
        if (!multiCreateMutation) {
          set((state) => {
            state.bulkImportError = 'Multi-create mutation is not available';
          });
          return false;
        }

        const { bulkImportData, contentType, formData } = get();

        if (bulkImportData.length === 0) {
          set((state) => {
            state.bulkImportError = 'No data to import';
          });
          return false;
        }

        set((state) => {
          state.isBulkImportLoading = true;
          state.bulkImportError = null;
        });

        try {
          // Get the relationship ID from formData
          const relationshipId = formData.id;
          if (!relationshipId) {
            throw new Error('No relationship ID available for import');
          }

          // Prepare the items for multi-create
          const items = bulkImportData.map((item) => ({
            type:
              item.type ||
              (contentType === 'dictionary'
                ? 'data_dict'
                : contentType === 'sensitiveKeyword'
                  ? 'sensitive_word'
                  : contentType),
            keyDefinition: item.keyDefinition || '',
            value: item.value || '',
            displayValue: item.displayValue || '',
            englishDisplay: item.englishDisplay || '',
            mandarinDisplay: item.mandarinDisplay || '',
            relationshipId: relationshipId.toString(),
            language: item.language || 'zh',
            platform: item.platform || 'web',
            regexFormula: item.regexFormula || '',
            useModel: item.useModel || false,
            source: item.source || '',
            category: item.category || '',
            formId: item.formId || '',
            remark: item.remark || '',
          }));

          // Call the multi-create API with correct parameter names
          await multiCreateMutation.mutateAsync({
            contentType: contentType,
            request: { items },
          });

          set((state) => {
            state.isBulkImportLoading = false;
            state.isBulkImportModalOpen = false;
            state.bulkImportData = [];
            state.isDirty = true;
          });

          return true;
        } catch (error) {
          set((state) => {
            state.isBulkImportLoading = false;
            state.bulkImportError =
              error instanceof Error ? error.message : 'Unknown error occurred';
          });
          return false;
        }
      },

      downloadTemplate: () => {
        const { fieldConfig, contentType } = get();

        // Get visible fields for the template
        const visibleFields = Object.keys(fieldConfig).filter(
          (field) => fieldConfig[field].visible
        );

        // Create CSV content
        const headers = visibleFields.map(
          (field) => fieldConfig[field].label || field
        );
        const csvContent = [
          headers.join(','),
          // Add example row
          visibleFields.map(() => '').join(','),
        ].join('\n');

        // Create and download file
        const blob = new Blob([csvContent], {
          type: 'text/csv;charset=utf-8;',
        });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `${contentType}_template.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      },
    })
  )
);
