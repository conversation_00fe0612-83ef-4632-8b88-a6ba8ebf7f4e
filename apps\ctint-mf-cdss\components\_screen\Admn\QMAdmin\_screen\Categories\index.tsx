import { Panel } from '@cdss-modules/design-system';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useState, useEffect } from 'react';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';
import { debounce } from 'lodash';

// Import types
import { CategoriesTable } from '../../_ui/CategoriesList/CategoriesTable';
import { Header } from '../../_ui/CategoriesList/Header';

// Import the queries and mutations
import {
  useCategoriesUnifiedQuery,
  useUnifiedCreateDefinitionMutation,
} from '../unified-queries';
import { useSorting } from '../../hooks/useSorting';

// ======================================
// Main Component
// ======================================
export const SOPCategoriesBody = () => {
  // Initialize state with default values
  const [searchValue, setSearchValue] = useState('');
  const [debouncedSearchValue, setDebouncedSearchValue] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(50);

  // Use the sorting hook
  const { orderBy, order, handleSortChange } = useSorting({
    defaultOrderBy: 'createTime',
    defaultOrder: 'ASC',
  });

  // Debounced search handler
  const debouncedSearch = debounce((value: string) => {
    setDebouncedSearchValue(value);
  }, 1000);

  // Update debounced value when search input changes
  useEffect(() => {
    debouncedSearch(searchValue);
  }, [searchValue, debouncedSearch]);

  // Reset to first page when search changes
  useEffect(() => {
    setCurrentPage(1);
  }, [debouncedSearchValue]);

  // Data fetching with React Query using the custom hook
  const { data, isLoading, isFetching } = useCategoriesUnifiedQuery(
    undefined, // Categories don't need parentId
    debouncedSearchValue,
    '',
    currentPage,
    pageSize,
    orderBy, // Pass orderBy
    order // Pass order
  );

  console.log('Categories List API response:', data);

  // Use the create mutation
  const createCategoryMutation = useUnifiedCreateDefinitionMutation();

  const handleCreateCategory = async (categoryName: string) => {
    createCategoryMutation.mutate({
      name: categoryName,
      relationshipType: 'category',
    });
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handlePerPageChange = (value: number) => {
    setPageSize(value);
    const newPage = 1;
    setCurrentPage(newPage); // Reset to page 1 when changing items per page
  };

  const handleSearchChange = (value: string) => {
    setSearchValue(value);
  };

  // Handler for sorting changes from CategoriesTable - now uses the hook
  const handleTableSortChange = (
    newOrderBy: string,
    newOrder: 'ASC' | 'DESC' | undefined
  ) => {
    handleSortChange(newOrderBy, newOrder);
    setCurrentPage(1); // Reset to first page on sort change
  };

  return (
    <Panel containerClassName="h-full overflow-hidden flex flex-col">
      <Header
        searchValue={searchValue}
        setSearchValue={handleSearchChange}
        onCreateClick={handleCreateCategory}
      />
      <CategoriesTable
        data={data?.data?.items}
        isLoading={isLoading || isFetching}
        totalItems={data?.data?.totalCount || 0}
        totalPages={data?.data?.totalPage || 0}
        currentPage={currentPage}
        perPage={pageSize}
        onPageChange={handlePageChange}
        onPerPageChange={handlePerPageChange}
        orderBy={orderBy} // Pass orderBy to table
        order={order} // Pass order to table
        onSort={handleTableSortChange} // Pass sort handler to table
      />
    </Panel>
  );
};

// Create a client
const queryClient = new QueryClient();

const SOPCategoriesScreen = () => {
  return (
    <QueryClientProvider client={queryClient}>
      <SOPCategoriesBody />
    </QueryClientProvider>
  );
};

export default SOPCategoriesScreen;
