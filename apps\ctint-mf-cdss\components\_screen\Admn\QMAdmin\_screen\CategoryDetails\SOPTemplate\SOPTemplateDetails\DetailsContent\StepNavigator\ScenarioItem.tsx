'use client';

import { Trash2 } from 'lucide-react';
import { ScenarioData } from '../../../../sop-template-queries';
import { cn } from '@cdss-modules/design-system/lib/utils';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';

interface ScenarioItemProps {
  scenario: ScenarioData;
  isActive: boolean;
  onScenarioChange: (scenarioId: string) => void;
  onRemoveScenario?: (scenarioId: string, scenarioName: string) => void;
}

export const ScenarioItem = ({
  scenario,
  isActive,
  onScenarioChange,
  onRemoveScenario,
}: ScenarioItemProps) => {
  const { t } = useTranslation();
  const handleScenarioClick = () => {
    onScenarioChange(scenario.id);
  };

  const handleRemoveClick = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent triggering scenario selection
    if (onRemoveScenario) {
      onRemoveScenario(scenario.id, scenario.name);
    }
  };

  return (
    <button
      onClick={handleScenarioClick}
      className={cn(
        'flex justify-between items-center p-1 pl-4 pr-[27px] hover:bg-primary-100 w-full',
        {
          'bg-primary-200': isActive,
        }
      )}
    >
      <span className="flex-grow text-left text-sm py-1">
        {t('ctint-mf-cdss.qmAdmin.sopTemplates.stepNavigator.scenario')}{' '}
        {scenario.name}
      </span>
      <div className="min-w-[24px] flex justify-end">
        {onRemoveScenario && (
          <button
            type="button"
            onClick={handleRemoveClick}
            className="p-1 rounded hover:bg-gray-100 text-red-500 transition-colors duration-150"
            aria-label={t(
              'ctint-mf-cdss.qmAdmin.sopTemplates.stepNavigator.removeScenario'
            )}
            title={t(
              'ctint-mf-cdss.qmAdmin.sopTemplates.stepNavigator.removeScenario'
            )}
          >
            <Trash2 size={12} />
          </button>
        )}
      </div>
    </button>
  );
};
