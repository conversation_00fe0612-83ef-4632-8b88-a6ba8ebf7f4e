import { TBreadcrumbItem } from '@cdss-modules/design-system/components/_ui/Breadcrumb';

const BreadcrumbDemo = () => {
  const breadcrumbs = [
    {
      label: 'Home',
      link: '/',
    },
    {
      label: 'Detail',
      link: 'Detail',
    },
  ];

  return (
    <div className="flex items-center">
      {breadcrumbs.map((item: TBreadcrumbItem, index) => {
        return (
          <div
            key={item.label}
            className="cursor-pointer mr-2 text-body"
          >
            {item.label}
            {index !== breadcrumbs.length - 1 && <span> / </span>}
          </div>
        );
      })}
    </div>
  );
};

export default BreadcrumbDemo;
