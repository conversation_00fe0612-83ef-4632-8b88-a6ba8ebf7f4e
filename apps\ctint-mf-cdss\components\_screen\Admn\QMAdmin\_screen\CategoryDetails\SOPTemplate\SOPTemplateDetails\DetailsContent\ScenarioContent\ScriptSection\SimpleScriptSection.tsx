'use client';

import React, { useState, useEffect } from 'react';
import Input from '@cdss-modules/design-system/components/_ui/Input';
import IconDropdownArrow from '@cdss-modules/design-system/components/_ui/Icon/IconDropdownArrow';
import { cn } from '@cdss-modules/design-system/lib/utils';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';

interface SimpleScriptSectionProps {
  script: string;
  scriptEng: string;
  similarity?: number;
  onScriptChange?: (value: string) => void;
  onSimilarityChange?: (value: number) => void;
  disabled?: boolean;
}

export const SimpleScriptSection: React.FC<SimpleScriptSectionProps> = ({
  script,
  scriptEng,
  similarity,
  onScriptChange,
  onSimilarityChange,
  disabled = false,
}) => {
  const { t } = useTranslation();
  const [isExpanded, setIsExpanded] = useState(true);
  const [scriptValue, setScriptValue] = useState(script || '');
  const [scriptEngValue, setScriptEngValue] = useState(scriptEng || '');
  // Convert from decimal (0-1) to percentage (0-100) for display
  const [similarityValue, setSimilarityValue] = useState(
    Math.round((similarity || 0) * 100)
  );

  // Sync local state with prop changes
  useEffect(() => {
    setScriptValue(script || '');
    setScriptEngValue(scriptEng || '');
  }, [script]);

  useEffect(() => {
    setSimilarityValue(Math.round((similarity || 0) * 100));
  }, [similarity]);

  const handleScriptChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    if (!disabled) {
      const value = e.target.value;
      setScriptValue(value);
      onScriptChange?.(value);
    }
  };

  const handleSimilarityChange = (value: string | number) => {
    if (!disabled) {
      const stringValue = value.toString();
      // Allow empty string or valid numbers
      if (
        stringValue === '' ||
        (/^\d+$/.test(stringValue) &&
          parseInt(stringValue) >= 0 &&
          parseInt(stringValue) <= 100)
      ) {
        const numValue = stringValue === '' ? 0 : parseInt(stringValue);
        setSimilarityValue(numValue);
        // Convert from percentage (0-100) to decimal (0-1) for storage
        onSimilarityChange?.(numValue / 100);
      }
    }
  };

  return (
    <div className="h-fit">
      <button
        aria-label={t(
          'ctint-mf-cdss.qmAdmin.sopTemplates.scriptSection.script'
        )}
        aria-expanded={isExpanded}
        type="button"
        className="flex justify-between items-center mb-3 w-full px-2 border-b-2"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <h4 className="text-sm">
          {t('ctint-mf-cdss.qmAdmin.sopTemplates.scriptSection.script')}
        </h4>

        <IconDropdownArrow
          alt={t(
            'ctint-mf-cdss.qmAdmin.sopTemplates.scriptSection.dropdownArrow'
          )}
          size="8"
          className={cn(
            'transition-transform duration-150 ease-in-out',
            isExpanded ? 'rotate-180' : ''
          )}
        />
      </button>

      <div
        className={cn(
          'overflow-hidden transition-all duration-150 ease-in-out',
          isExpanded ? 'max-h-[600px] opacity-100' : 'max-h-0 opacity-0'
        )}
      >
        <div
          className={cn(
            'transition-transform duration-150 ease-in-out',
            isExpanded ? 'translate-y-0' : '-translate-y-2'
          )}
        >
          <div className="space-y-4 pt-4 px-2 pb-4">
            {/* Similarity Input */}
            <div className="flex justify-start items-center gap-2">
              <span className="text-sm font-medium">
                {t(
                  'ctint-mf-cdss.qmAdmin.sopTemplates.scriptSection.similarity'
                )}{' '}
                ≥
              </span>
              <div className="relative w-20">
                <Input
                  type="text"
                  value={similarityValue.toString()}
                  onChange={handleSimilarityChange}
                  disabled={disabled}
                  className="pr-6 text-center"
                  placeholder="80"
                  min="0"
                  max="100"
                />
                <span className="absolute right-2 top-1/2 transform -translate-y-1/2 text-sm text-gray-500 pointer-events-none">
                  %
                </span>
              </div>
            </div>

            {/* Script Textarea */}
            <div className="space-y-2">
              <label className="text-sm font-medium">
                {t('ctint-mf-cdss.qmAdmin.sopTemplates.scriptSection.content')}
              </label>
              <textarea
                value={scriptValue}
                onChange={handleScriptChange}
                placeholder={
                  disabled
                    ? ''
                    : t(
                        'ctint-mf-cdss.qmAdmin.sopTemplates.scriptSection.enterScriptContent'
                      )
                }
                disabled={true}
                rows={7}
                className={cn(
                  'w-full p-3 border rounded-md text-sm resize-vertical min-h-[150px]',
                  'focus:outline-none focus:ring-1 focus:ring-primary focus:border-transparent',
                  disabled && 'bg-gray-50 cursor-not-allowed'
                )}
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">
                {t(
                  'ctint-mf-cdss.qmAdmin.sopTemplates.scriptSection.contentEng'
                )}
              </label>
              <textarea
                value={scriptEngValue}
                onChange={handleScriptChange}
                placeholder={
                  disabled
                    ? ''
                    : t(
                        'ctint-mf-cdss.qmAdmin.sopTemplates.scriptSection.enterScriptContent'
                      )
                }
                disabled={true}
                rows={7}
                className={cn(
                  'w-full p-3 border rounded-md text-sm resize-vertical min-h-[150px]',
                  'focus:outline-none focus:ring-1 focus:ring-primary focus:border-transparent',
                  disabled && 'bg-gray-50 cursor-not-allowed'
                )}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SimpleScriptSection;
