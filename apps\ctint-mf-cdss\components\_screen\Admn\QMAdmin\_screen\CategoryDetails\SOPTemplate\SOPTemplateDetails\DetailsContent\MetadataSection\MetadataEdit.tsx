'use client';

import React, { useState } from 'react';
import Button from '@cdss-modules/design-system/components/_ui/Button';
import { StepMetadata } from '../../types';

interface MetadataEditProps {
  metadata: StepMetadata[];
  onSave: (newMetadata: StepMetadata[]) => void;
  onCancel: () => void;
}

export const MetadataEdit: React.FC<MetadataEditProps> = ({
  metadata,
  onSave,
  onCancel,
}) => {
  const [items, setItems] = useState<StepMetadata[]>(metadata || []);

  const handleChange = (
    index: number,
    field: keyof StepMetadata,
    value: string
  ) => {
    const newItems = [...items];
    newItems[index] = { ...newItems[index], [field]: value };
    setItems(newItems);
  };

  const handleAdd = () => {
    setItems([...items, { field1: '', field2: '', operator: '', value: '' }]);
  };

  const handleRemove = (index: number) => {
    setItems(items.filter((_, i) => i !== index));
  };

  const handleSave = () => {
    onSave(items);
  };

  return (
    <div className="space-y-4">
      {items.map((item, index) => (
        <div
          key={index}
          className="p-3 bg-white border rounded-md relative"
        >
          <button
            className="absolute top-2 right-2 text-red-500 hover:text-red-700"
            onClick={() => handleRemove(index)}
          >
            Remove
          </button>
          <div className="grid grid-cols-2 gap-3 mb-3">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Field 1
              </label>
              <input
                type="text"
                className="w-full p-2 border rounded-md"
                value={item.field1}
                onChange={(e) => handleChange(index, 'field1', e.target.value)}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Field 2
              </label>
              <input
                type="text"
                className="w-full p-2 border rounded-md"
                value={item.field2}
                onChange={(e) => handleChange(index, 'field2', e.target.value)}
              />
            </div>
          </div>
          <div className="grid grid-cols-2 gap-3">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Operator
              </label>
              <select
                className="w-full p-2 border rounded-md"
                value={item.operator}
                onChange={(e) =>
                  handleChange(index, 'operator', e.target.value)
                }
              >
                <option value="">Select operator</option>
                <option value="equals">Equals</option>
                <option value="contains">Contains</option>
                <option value="startsWith">Starts With</option>
                <option value="endsWith">Ends With</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Value
              </label>
              <input
                type="text"
                className="w-full p-2 border rounded-md"
                value={item.value}
                onChange={(e) => handleChange(index, 'value', e.target.value)}
              />
            </div>
          </div>
        </div>
      ))}

      <div>
        <Button
          variant="secondary"
          onClick={handleAdd}
          className="mr-2"
        >
          Add Metadata
        </Button>
      </div>

      <div className="flex justify-end space-x-2 mt-4">
        <Button
          variant="secondary"
          onClick={onCancel}
        >
          Cancel
        </Button>
        <Button
          variant="primary"
          onClick={handleSave}
        >
          Save
        </Button>
      </div>
    </div>
  );
};

export default MetadataEdit;
