'use client';

import React, { useState } from 'react';
import Button from '@cdss-modules/design-system/components/_ui/Button';
import { Select } from '@cdss-modules/design-system/components/_ui/Select';
import HighlightWithinTextarea from 'react-highlight-within-textarea';

import { cn } from '@cdss-modules/design-system/lib/utils';
import IconDropdownArrow from '@cdss-modules/design-system/components/_ui/Icon/IconDropdownArrow';

interface EnhancedTextareaProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  label: string;
  disabled?: boolean;
}

export const EnhancedTextarea: React.FC<EnhancedTextareaProps> = ({
  value,
  onChange,
  placeholder,
  label,
  disabled = false,
}) => {
  const [showDictionary, setShowDictionary] = useState(false);

  const handleChange = disabled
    ? () => {
        /* no-op */
      }
    : onChange;

  // Extract dictionary patterns from text
  const extractDictionaryPatterns = (text: string) => {
    const matches = text.match(/%_[^%]*_%/g);
    return matches ? [...new Set(matches)] : [];
  };

  // Extract metadata patterns from text
  const extractMetadataPatterns = (text: string) => {
    const matches = text.match(/\{\{[^}]*\}\}/g);
    return matches ? [...new Set(matches)] : [];
  };

  const dictionaryPatterns = extractDictionaryPatterns(value);
  const metadataPatterns = extractMetadataPatterns(value);

  // Highlight configuration for react-highlight-within-textarea
  const highlight = [
    {
      highlight: /%_[^%]*_%/g,
      className: 'text-orange-500 bg-transparent',
    },
    {
      highlight: /\{\{[^}]*\}\}/g,
      className: 'text-tertiary-500 bg-transparent',
    },
  ];

  return (
    <div className="space-y-2">
      <div className="flex justify-between items-center">
        <span className="text-sm font-medium">{label}</span>
      </div>

      {/* Enhanced Textarea with Syntax Highlighting */}
      <div
        className={cn(
          'border rounded-md p-2 font-mono text-sm leading-5',
          disabled && 'bg-gray-50'
        )}
      >
        <div className="relative  min-h-[150px]">
          <HighlightWithinTextarea
            value={value}
            highlight={highlight}
            onChange={handleChange}
            placeholder={disabled ? '' : placeholder}
          />
          {disabled && <div className="absolute inset-0 cursor-not-allowed" />}
          <button
            onClick={() => setShowDictionary(!showDictionary)}
            className="text-xs absolute bottom-2 right-0 flex items-center gap-2 hover:bg-gray-100 p-1 rounded-md px-2"
          >
            <span className="text-xs text-primary-500">
              Dictionary {dictionaryPatterns.length}
            </span>
            <span className="text-xs text-tertiary-500">
              Metadata {metadataPatterns.length}
            </span>
            <IconDropdownArrow
              className={cn(
                'transition-transform duration-300',
                showDictionary ? 'rotate-180' : ''
              )}
              size="8"
            />
          </button>
        </div>

        {/* Dictionary and Metadata Panel - Shows what's been typed */}
        {showDictionary && (
          <div className="border rounded-md p-4 bg-gray-50">
            <div className="grid grid-cols-2 gap-6">
              {/* Dictionary Section */}
              <div>
                <h4 className="text-sm font-semibold mb-2 text-gray-700">
                  Dictionary
                </h4>
                <div className="space-y-2">
                  {dictionaryPatterns.length > 0 ? (
                    dictionaryPatterns.map((pattern, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between gap-2"
                      >
                        <div className="flex items-center gap-2 flex-1">
                          <span className="text-orange-500 bg-orange-100 px-2 py-1 rounded text-xs font-mono">
                            {pattern}
                          </span>
                          <select className="text-xs border rounded px-1 py-0.5">
                            <option>AND</option>
                            <option>OR</option>
                          </select>
                        </div>
                      </div>
                    ))
                  ) : (
                    <p className="text-gray-500 text-xs italic">
                      No dictionary patterns found
                    </p>
                  )}
                </div>
              </div>

              {/* Metadata Section */}
              <div>
                <h4 className="text-sm font-semibold mb-2 text-gray-700">
                  Metadata
                </h4>
                <div className="space-y-2">
                  {metadataPatterns.length > 0 ? (
                    metadataPatterns.map((pattern, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between gap-2"
                      >
                        <div className="flex items-center gap-2 flex-1">
                          <span className="text-blue-500 bg-blue-100 px-2 py-1 rounded text-xs font-mono">
                            {pattern}
                          </span>
                          <select className="text-xs border rounded px-1 py-0.5">
                            <option>AND</option>
                            <option>OR</option>
                          </select>
                        </div>
                      </div>
                    ))
                  ) : (
                    <p className="text-gray-500 text-xs italic">
                      No metadata patterns found
                    </p>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default EnhancedTextarea;
