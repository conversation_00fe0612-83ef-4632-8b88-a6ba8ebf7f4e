import { Auth<PERSON><PERSON><PERSON>, <PERSON>, useRouteHandler } from '@cdss-modules/design-system';
import AnnouncerDemo from '../../_ui/AnnouncerDemo';

export function Detail() {
  const { toPath } = useRouteHandler();
  return (
    <AuthChecker
      unAuthorizedComponent={
        <Panel>
          <h2 className="p-6 font-bold text-t6">
            You are unauthorized to use this feature.
          </h2>
        </Panel>
      }
    >
      <AnnouncerDemo
        testId="detail"
        titleI18n="ctint-mf-announcer.announcerDetail.title"
        descI18n="ctint-mf-announcer.announcerDetail.desc"
        btnLabelI18n="ctint-mf-announcer.announcerDetail.btnLabel"
        onClickButton={() => toPath('/')}
      />
    </AuthChecker>
  );
}

export default Detail;
