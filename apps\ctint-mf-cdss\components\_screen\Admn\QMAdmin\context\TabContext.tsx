import React, {
  createContext,
  useContext,
  useState,
  ReactNode,
  useEffect,
} from 'react';

import { useTabsContext } from '@cdss-modules/design-system/context/TabsContext';
import { useUnifiedDefinitionByIdQuery } from '../_screen/definition-queries';

export type TTabType =
  | 'sopTemplate'
  | 'metadata'
  | 'dictionary'
  | 'sttKeywords'
  | 'sensitiveKeywords'
  | 'permissions';

export type TTabContextType = {
  selectedItem: {
    type: TTabType | null;
    id: string | null;
  };
  setSelectedItem: (item: { type: TTabType | null; id: string | null }) => void;
  handleBackToList: () => void;
  categoryName: string | undefined;
  isCategoryLoading: boolean;
};

const TabContext = createContext<TTabContextType | undefined>(undefined);

export const TabProvider: React.FC<{
  children: ReactNode;
  categoryId: string;
}> = ({ categoryId, children }) => {
  const [selectedItem, setSelectedItem] = useState<{
    type: TTabType | null;
    id: string | null;
  }>({
    type: null,
    id: null,
  });

  // Use the design-system tab context for resetting tabs
  const { onChangeTab: setActiveTab } = useTabsContext();

  // Fetch category details to get the name (always needed)
  const { data: categoryResponse, isLoading: isCategoryLoading } =
    useUnifiedDefinitionByIdQuery(categoryId);

  const category = categoryResponse?.data;

  // Reset to sopTemplate when categoryId changes
  useEffect(() => {
    setActiveTab('sopTemplate');
    setSelectedItem({ type: null, id: null });
  }, [categoryId]); // Remove setActiveTab from dependencies

  const handleBackToList = () => {
    setSelectedItem({ type: null, id: null });
  };

  return (
    <TabContext.Provider
      value={{
        selectedItem,
        setSelectedItem,
        handleBackToList,
        categoryName: category?.name,
        isCategoryLoading,
      }}
    >
      {children}
    </TabContext.Provider>
  );
};

export const useTab = () => {
  const context = useContext(TabContext);
  if (context === undefined) {
    throw new Error('useTab must be used within a TabProvider');
  }
  return context;
};
