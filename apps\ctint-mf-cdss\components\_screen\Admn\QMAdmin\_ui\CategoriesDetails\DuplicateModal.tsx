'use client';

import { useState } from 'react';
import { Button } from '@cdss-modules/design-system';
import Input from '@cdss-modules/design-system/components/_ui/Input';
import {
  Popup,
  PopupContent,
} from '@cdss-modules/design-system/components/_ui/Popup';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';

interface DuplicateModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (newVersion: string) => void;
  currentVersion?: string;
  itemName?: string;
  isSubmitting?: boolean;
}

export const DuplicateModal: React.FC<DuplicateModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  currentVersion = '',
  itemName = '',
  isSubmitting = false,
}) => {
  const { t } = useTranslation();
  const [newVersion, setNewVersion] = useState('');
  const [error, setError] = useState('');

  const handleSubmit = () => {
    // Basic validation
    if (!newVersion.trim()) {
      setError(t('ctint-mf-cdss.qmAdmin.duplicateModal.versionNameRequired'));
      return;
    }

    if (newVersion.trim() === currentVersion) {
      setError(
        t('ctint-mf-cdss.qmAdmin.duplicateModal.versionMustBeDifferent')
      );
      return;
    }

    // Clear error and proceed
    setError('');
    onConfirm(newVersion.trim());
  };

  const handleClose = () => {
    setNewVersion('');
    setError('');
    onClose();
  };

  const handleVersionChange = (value: string | number | boolean) => {
    setNewVersion(String(value));
    if (error) {
      setError(''); // Clear error when user starts typing
    }
  };

  return (
    <Popup
      open={isOpen}
      onOpenChange={handleClose}
    >
      <PopupContent
        title={t('ctint-mf-cdss.qmAdmin.duplicateModal.title')}
        className="max-w-md"
      >
        <div className="p-6">
          <div className="mb-4">
            <p className="text-gray-600 mb-4">
              {t('ctint-mf-cdss.qmAdmin.duplicateModal.createNewVersion')}{' '}
              <strong>{itemName}</strong>{' '}
              {t('ctint-mf-cdss.qmAdmin.duplicateModal.withDataCopied')}
            </p>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {t('ctint-mf-cdss.qmAdmin.duplicateModal.currentVersion')}{' '}
                <span className="font-normal text-gray-500">
                  {currentVersion}
                </span>
              </label>
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {t('ctint-mf-cdss.qmAdmin.duplicateModal.newVersionName')} *
              </label>
              <Input
                value={newVersion}
                onChange={handleVersionChange}
                placeholder={t(
                  'ctint-mf-cdss.qmAdmin.duplicateModal.enterNewVersionName'
                )}
                size="s"
                disabled={isSubmitting}
                className={error ? 'border-red-500 bg-red-50' : ''}
              />
              {error && <p className="text-red-600 text-sm mt-1">{error}</p>}
            </div>
          </div>

          <div className="flex justify-end gap-3">
            <Button
              variant="secondary"
              onClick={handleClose}
              disabled={isSubmitting}
            >
              {t('ctint-mf-cdss.qmAdmin.duplicateModal.cancel')}
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={isSubmitting || !newVersion.trim()}
            >
              {isSubmitting
                ? t('ctint-mf-cdss.qmAdmin.duplicateModal.creating')
                : t('ctint-mf-cdss.qmAdmin.duplicateModal.createDuplicate')}
            </Button>
          </div>
        </div>
      </PopupContent>
    </Popup>
  );
};

export default DuplicateModal;
