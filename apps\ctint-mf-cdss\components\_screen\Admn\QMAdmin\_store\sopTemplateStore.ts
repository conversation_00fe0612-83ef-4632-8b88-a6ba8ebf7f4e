'use client';

import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import {
  StepData,
  ScenarioData,
  SOPTemplateDetailedData,
  StandardScriptRule,
} from '../_screen/CategoryDetails/sop-template-queries';
import { Status } from '../_screen/CategoryDetails/SOPTemplate/SOPTemplateDetails/types';

// Helper to create a deep copy of the template data
const deepCopyTemplateData = (
  data: SOPTemplateDetailedData
): SOPTemplateDetailedData => {
  return {
    ...data,
    steps: data.steps?.map((step: StepData) => ({
      ...step,
      scenarios: step.scenarios.map((scenario: ScenarioData) => ({
        ...scenario,
        standardScriptRules: scenario.standardScriptRules.map(
          (rule: StandardScriptRule) => ({ ...rule })
        ),
      })),
    })),
  };
};

interface ISOPTemplateState {
  // Template Data State
  templateData: SOPTemplateDetailedData | null;
  originalTemplateData: SOPTemplateDetailedData | null;
  isLoading: boolean;
  isError: boolean;
  isDirty: boolean;
  validationErrors: Record<string, string>;

  // Track changed similarity values
  changedSimilarityValues: Record<
    string,
    { standardScriptId: string; similarityRequired: number }
  >;

  // Navigation State
  currentStep: number;
  currentScenario: string;

  // Edit Mode State
  isEditing: boolean;

  // Derived state getters
  getCurrentStepData: () => StepData | undefined;
  getCurrentScenarioData: () => ScenarioData | undefined;
  getSteps: () => StepData[];

  // Template Management Actions
  loadTemplateData: (data: SOPTemplateDetailedData) => void;
  resetTemplateData: () => void;
  updateTemplateField: (
    field: keyof SOPTemplateDetailedData,
    value: any
  ) => void;

  // Navigation Actions
  setCurrentStep: (stepId: number) => void;
  setCurrentScenario: (scenarioId: string) => void;
  initializeNavigation: (steps: StepData[]) => void;
  resetNavigation: () => void;

  // Step Management Actions
  addStep: () => void;
  removeStep: (stepId: number) => boolean;
  updateStepField: (stepId: number, field: keyof StepData, value: any) => void;

  // Scenario Management Actions
  addScenario: (stepId: number) => void;
  removeScenario: (stepId: number, scenarioId: string) => boolean;
  updateScenarioField: (
    stepId: number,
    scenarioId: string,
    field: keyof ScenarioData,
    value: any
  ) => void;

  // Persistence Actions
  saveTemplate: () => Promise<void>;
  submitForApproval: () => Promise<void>;

  // Edit Mode Actions
  setIsEditing: (isEditing: boolean) => void;
  enterEditMode: () => void;
  exitEditMode: () => void;
  cancelEdit: () => void;
  saveAndExitEditMode: (
    onSave?: (data: SOPTemplateDetailedData) => void
  ) => void;
  submitForApprovalAction: (onStatusChange?: (status: Status) => void) => void;
  approveTemplate: (onStatusChange?: (status: Status) => void) => void;
  returnTemplate: (onStatusChange?: (status: Status) => void) => void;

  // Utility Actions
  setLoading: (loading: boolean) => void;
  setError: (error: boolean) => void;
  clearValidationErrors: () => void;
  resetStore: () => void;
  getChangedSimilarityValues: () => {
    standardScriptId: string;
    similarityRequired: number;
  }[];
  clearChangedSimilarityValues: () => void;
}

export const useSOPTemplateStore = create<ISOPTemplateState>()(
  immer((set, get) => ({
    // Initial State
    templateData: null,
    originalTemplateData: null,
    isLoading: false,
    isError: false,
    isDirty: false,
    validationErrors: {},
    currentStep: 0,
    currentScenario: '',

    // Track changed similarity values
    changedSimilarityValues: {},

    // Edit Mode Initial State
    isEditing: false,

    // Derived state getters
    getCurrentStepData: () => {
      const { templateData, currentStep } = get();
      return templateData?.steps?.find(
        (step: StepData) => step.id === currentStep
      );
    },

    getCurrentScenarioData: () => {
      const { templateData, currentStep, currentScenario } = get();
      const currentStepData = templateData?.steps?.find(
        (step: StepData) => step.id === currentStep
      );
      return currentStepData?.scenarios.find(
        (scenario: ScenarioData) => scenario.id === currentScenario
      );
    },

    getSteps: () => {
      const { templateData } = get();
      return templateData?.steps || [];
    },

    // Template Management Actions
    loadTemplateData: (data) =>
      set((state) => {
        state.templateData = data;
        state.originalTemplateData = deepCopyTemplateData(data);
        state.isDirty = false;
        state.isError = false;
        state.validationErrors = {};

        // Only initialize navigation if we don't have any current navigation
        // This prevents overriding navigation set by add/remove operations
        if (state.currentStep === 0 && state.currentScenario === '') {
          const steps = data.steps || [];
          if (steps.length > 0) {
            state.currentStep = steps[0].id;
            if (steps[0].scenarios.length > 0) {
              state.currentScenario = steps[0].scenarios[0].id;
            } else {
              state.currentScenario = '';
            }
          } else {
            state.currentStep = 0;
            state.currentScenario = '';
          }
        }
      }),

    resetTemplateData: () =>
      set((state) => {
        if (state.originalTemplateData) {
          state.templateData = deepCopyTemplateData(state.originalTemplateData);
          state.isDirty = false;
          state.validationErrors = {};
        }
      }),

    updateTemplateField: (field, value) =>
      set((state) => {
        if (state.templateData) {
          (state.templateData[field] as any) = value;
          state.isDirty = true;

          // Clear validation error for this field if exists
          const fieldKey = String(field);
          if (state.validationErrors[fieldKey]) {
            delete state.validationErrors[fieldKey];
          }
        }
      }),

    // Navigation Actions
    setCurrentStep: (stepId) =>
      set((state) => {
        state.currentStep = stepId;

        // Auto-select first scenario of the new step if available
        const newStepData = state.templateData?.steps?.find(
          (step: StepData) => step.id === stepId
        );
        if (newStepData && newStepData.scenarios.length > 0) {
          state.currentScenario = newStepData.scenarios[0].id;
        } else {
          state.currentScenario = '';
        }
      }),

    setCurrentScenario: (scenarioId) =>
      set((state) => {
        state.currentScenario = scenarioId;
      }),

    initializeNavigation: (steps) =>
      set((state) => {
        // Set initial step and scenario if steps exist
        if (steps.length > 0) {
          state.currentStep = steps[0].id;
          if (steps[0].scenarios.length > 0) {
            state.currentScenario = steps[0].scenarios[0].id;
          } else {
            state.currentScenario = '';
          }
        } else {
          state.currentStep = 0;
          state.currentScenario = '';
        }
      }),

    resetNavigation: () =>
      set((state) => {
        state.currentStep = 0;
        state.currentScenario = '';
      }),

    // Step Management Actions
    addStep: () =>
      set((state) => {
        if (!state.templateData) return;

        const steps = state.templateData.steps || [];
        const newStepId =
          steps.length > 0
            ? Math.max(...steps.map((step: StepData) => step.id)) + 1
            : 1;

        const newStep: StepData = {
          id: newStepId,
          name: `Step ${newStepId}`,
          scenarios: [],
        };

        state.templateData.steps.push(newStep);
        state.isDirty = true;

        // Auto-navigate to the new step
        state.currentStep = newStepId;
        state.currentScenario = '';
      }),

    removeStep: (stepId) => {
      set((state) => {
        if (!state.templateData?.steps) return;

        state.templateData.steps = state.templateData.steps.filter(
          (step: StepData) => step.id !== stepId
        );
        state.isDirty = true;

        // Handle navigation after step removal
        const remainingSteps = state.templateData.steps;
        if (state.currentStep === stepId) {
          if (remainingSteps.length > 0) {
            state.currentStep = remainingSteps[0].id;
            if (remainingSteps[0].scenarios.length > 0) {
              state.currentScenario = remainingSteps[0].scenarios[0].id;
            } else {
              state.currentScenario = '';
            }
          } else {
            state.currentStep = 0;
            state.currentScenario = '';
          }
        }
      });

      return true;
    },

    updateStepField: (stepId, field, value) =>
      set((state) => {
        if (!state.templateData?.steps) return;

        const stepIndex = state.templateData.steps.findIndex(
          (step: StepData) => step.id === stepId
        );

        if (stepIndex !== -1) {
          (state.templateData.steps[stepIndex] as any)[field] = value;
          state.isDirty = true;
        }
      }),

    // Scenario Management Actions
    addScenario: (stepId) =>
      set((state) => {
        if (!state.templateData?.steps) return;

        const stepIndex = state.templateData.steps.findIndex(
          (step: StepData) => step.id === stepId
        );

        if (stepIndex === -1) return;

        const stepScenarios = state.templateData.steps[stepIndex].scenarios;
        const newScenarioId = `${stepId}-${stepScenarios.length + 1}`;

        const newScenario: ScenarioData = {
          id: newScenarioId,
          standardScriptId: `temp-${Date.now()}`,
          name: `Scenario ${newScenarioId}`,
          script: '',
          scriptEng: '',
          scriptMandarin: '',
          helpContent: '',
          participant: 'Agent',
          doSimilarity: true,
          doExtraction: false,
          similarityRequired: 0.8,
          useDictionary: false,
          doMetadataRef: false,
          standardScriptRules: [],
          scriptOrder: stepScenarios.length + 1,
          createTime: new Date().toISOString(),
          updateTime: null,
          createBy: '',
          updateBy: '',
        };

        state.templateData.steps[stepIndex].scenarios.push(newScenario);
        state.isDirty = true;

        // Auto-navigate to the new scenario
        state.currentStep = stepId;
        state.currentScenario = newScenarioId;
      }),

    removeScenario: (stepId, scenarioId) => {
      set((state) => {
        if (!state.templateData?.steps) return;

        const stepIndex = state.templateData.steps.findIndex(
          (step: StepData) => step.id === stepId
        );

        if (stepIndex === -1) return;

        const step = state.templateData.steps[stepIndex];
        step.scenarios = step.scenarios.filter(
          (scenario: ScenarioData) => scenario.id !== scenarioId
        );
        state.isDirty = true;

        // Handle navigation after scenario removal
        if (state.currentScenario === scenarioId) {
          if (step.scenarios.length > 0) {
            state.currentScenario = step.scenarios[0].id;
          } else {
            state.currentScenario = '';
          }
        }
      });

      return true;
    },

    updateScenarioField: (stepId, scenarioId, field, value) =>
      set((state) => {
        if (!state.templateData?.steps) return;

        const stepIndex = state.templateData.steps.findIndex(
          (step: StepData) => step.id === stepId
        );

        if (stepIndex === -1) return;

        const scenarioIndex = state.templateData.steps[
          stepIndex
        ].scenarios.findIndex(
          (scenario: ScenarioData) => scenario.id === scenarioId
        );

        if (scenarioIndex !== -1) {
          const scenario =
            state.templateData.steps[stepIndex].scenarios[scenarioIndex];

          // If updating similarityRequired, track it in the changes map
          if (field === 'similarityRequired' && scenario.standardScriptId) {
            const changeKey = `${stepId}-${scenarioId}`;
            const numericValue =
              typeof value === 'string' ? parseFloat(value) : value;

            console.log(`Tracking similarity change for ${changeKey}:`, {
              standardScriptId: scenario.standardScriptId,
              oldValue: scenario.similarityRequired,
              newValue: numericValue,
            });

            state.changedSimilarityValues[changeKey] = {
              standardScriptId: scenario.standardScriptId,
              similarityRequired: numericValue,
            };
          }

          // Use type assertion to handle the new optional fields
          (scenario as any)[field] = value;
          state.isDirty = true;
        }
      }),

    // Persistence Actions
    saveTemplate: async () => {
      const { templateData } = get();

      set((state) => {
        state.isLoading = true;
        state.isError = false;
      });

      try {
        // In a real app, this would be an API call
        await new Promise((resolve) => setTimeout(resolve, 500));

        console.log('Template saved:', templateData);

        set((state) => {
          if (state.templateData) {
            state.originalTemplateData = deepCopyTemplateData(
              state.templateData
            );
            state.isDirty = false;
          }
        });
      } catch (error) {
        console.error('Error saving template:', error);
        set((state) => {
          state.isError = true;
        });
      } finally {
        set((state) => {
          state.isLoading = false;
        });
      }
    },

    submitForApproval: async () => {
      const { templateData } = get();

      set((state) => {
        state.isLoading = true;
        state.isError = false;
      });

      try {
        // In a real app, this would be an API call
        await new Promise((resolve) => setTimeout(resolve, 500));

        console.log('Template submitted for approval:', templateData);

        set((state) => {
          if (state.templateData) {
            state.templateData.status = 'Pending Approval';
            state.originalTemplateData = deepCopyTemplateData(
              state.templateData
            );
            state.isDirty = false;
          }
        });
      } catch (error) {
        console.error('Error submitting template:', error);
        set((state) => {
          state.isError = true;
        });
      } finally {
        set((state) => {
          state.isLoading = false;
        });
      }
    },

    // Utility Actions
    setLoading: (loading) =>
      set((state) => {
        state.isLoading = loading;
      }),

    setError: (error) =>
      set((state) => {
        state.isError = error;
      }),

    clearValidationErrors: () =>
      set((state) => {
        state.validationErrors = {};
      }),

    // Edit Mode Actions
    setIsEditing: (isEditing) =>
      set((state) => {
        state.isEditing = isEditing;
      }),

    enterEditMode: () =>
      set((state) => {
        state.isEditing = true;
      }),

    exitEditMode: () =>
      set((state) => {
        state.isEditing = false;
      }),

    cancelEdit: () =>
      set((state) => {
        if (state.originalTemplateData) {
          state.templateData = deepCopyTemplateData(state.originalTemplateData);
          state.isDirty = false;
          state.isEditing = false;

          // Reset navigation to the original state
          const steps = state.templateData.steps || [];
          if (steps.length > 0) {
            state.currentStep = steps[0].id;
            if (steps[0].scenarios.length > 0) {
              state.currentScenario = steps[0].scenarios[0].id;
            } else {
              state.currentScenario = '';
            }
          } else {
            state.currentStep = 0;
            state.currentScenario = '';
          }
        }
      }),

    saveAndExitEditMode: (onSave) =>
      set((state) => {
        if (state.templateData && onSave) {
          // Create a deep copy to avoid proxy revocation issues
          const templateDataCopy = deepCopyTemplateData(state.templateData);
          // Call the save callback with the copied template data
          onSave(templateDataCopy);
        }

        // Update the original data and exit edit mode
        if (state.templateData) {
          state.originalTemplateData = deepCopyTemplateData(state.templateData);
          state.isDirty = false;
          state.isEditing = false;
        }
      }),

    submitForApprovalAction: (onStatusChange) => {
      const state = get();
      if (onStatusChange) {
        onStatusChange('Pending Approval');
      }
    },

    approveTemplate: (onStatusChange) => {
      const state = get();
      if (onStatusChange) {
        onStatusChange('Approved');
      }
    },

    returnTemplate: (onStatusChange) => {
      const state = get();
      if (onStatusChange) {
        onStatusChange('Returned');
      }
    },

    // Helper to get changed similarity values
    getChangedSimilarityValues: () => {
      const { changedSimilarityValues } = get();
      return Object.values(changedSimilarityValues);
    },

    clearChangedSimilarityValues: () =>
      set((state) => {
        state.changedSimilarityValues = {};
      }),

    resetStore: () =>
      set((state) => {
        state.templateData = null;
        state.originalTemplateData = null;
        state.isLoading = false;
        state.isError = false;
        state.isDirty = false;
        state.validationErrors = {};
        state.currentStep = 0;
        state.currentScenario = '';
        state.isEditing = false;
        state.changedSimilarityValues = {};
      }),
  }))
);
