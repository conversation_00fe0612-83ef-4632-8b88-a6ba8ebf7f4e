import { Auth<PERSON><PERSON><PERSON>, <PERSON>, useRouteHand<PERSON> } from '@cdss-modules/design-system';
import AnnouncerDemo from '../../_ui/AnnouncerDemo';
export function Main() {
  const { toPath } = useRouteHandler();
  return (
    <AuthChecker
      // requiredPemissions={{
      //   global: {
      //     portals: ['ctint-mf-announcer'],
      //   },
      //   user: {
      //     permissions: ['ctint-mf-announcer.application.visit'],
      //   },
      // }}
      unAuthorizedComponent={
        <Panel>
          <h2 className="p-6 font-bold text-t6">
            You are unauthorized to use this feature.
          </h2>
        </Panel>
      }
    >
      <AnnouncerDemo
        testId="home"
        titleI18n="ctint-mf-announcer.announcerHome.title"
        descI18n="ctint-mf-announcer.announcerHome.desc"
        btnLabelI18n="ctint-mf-announcer.announcerHome.btnLabel"
        onClickButton={() => toPath('/detail')}
      />
    </AuthChecker>
  );
}

export default Main;
