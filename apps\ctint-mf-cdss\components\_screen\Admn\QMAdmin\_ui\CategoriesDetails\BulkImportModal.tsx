'use client';

import { useState, useRef, useMemo } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>up<PERSON>ontent,
  PopupTrigger,
} from '@cdss-modules/design-system/components/_ui/Popup';
import { <PERSON><PERSON>, Loader, toast } from '@cdss-modules/design-system';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';
import { DataTable } from '@cdss-modules/design-system/components/_ui/DataTable';
import Input from '@cdss-modules/design-system/components/_ui/Input';
import Switch from '@cdss-modules/design-system/components/_ui/Switch';
import { ColumnDef } from '@tanstack/react-table';
import { Trash2, Upload, Download } from 'lucide-react';
import {
  useContentMappingStore,
  EditableContentMappingItem,
  ContentMappingItem,
} from '../../_store/contentMappingStore';

interface BulkImportModalProps {
  isOpen: boolean;
  onClose: () => void;
  multiCreateMutation?: any; // The multi-create mutation hook
}

// Simple CSV parser (for Excel files saved as CSV)
const parseCSV = (csvText: string): any[] => {
  const lines = csvText.split('\n').filter((line) => line.trim());
  if (lines.length < 2) return [];

  const headers = lines[0].split(',').map((h) => h.trim().replace(/"/g, ''));
  const rows = [];

  for (let i = 1; i < lines.length; i++) {
    const values = lines[i].split(',').map((v) => v.trim().replace(/"/g, ''));
    if (values.some((v) => v)) {
      // Skip empty rows
      const row: any = {};
      headers.forEach((header, index) => {
        row[header.toLowerCase().replace(/ /g, '')] = values[index] || '';
      });
      rows.push(row);
    }
  }

  return rows;
};

// Map CSV headers to our field names
const mapCSVToFields = (
  csvData: any[],
  fieldConfig: any,
  contentType: string
): ContentMappingItem[] => {
  return csvData.map((row) => {
    const mappedRow: any = {
      contentDefinitionId: 0,
      type: contentType === 'dictionary' ? 'data_dict' : contentType,
      language: 'en',
      platform: 'web',
      tenant: 'ctint',
      createdBy: '',
      updatedBy: '',
    };

    // Map common field variations
    const fieldMapping: Record<string, string[]> = {
      keyDefinition: ['key', 'keydefinition', 'keydef', 'definition'],
      value: ['value', 'val'],
      type: ['type', 'datatype', 'data_type'],
      englishDisplay: ['englishdisplay', 'english', 'en', 'engdisplay'],
      mandarinDisplay: ['mandarindisplay', 'mandarin', 'putonghua', 'zh'],
      displayValue: ['displayvalue', 'cantonese', 'display', 'canton'],
      standardScriptId: ['standardscriptid', 'scriptid', 'script'],
      regexFormula: ['regexformula', 'regex', 'formula'],
      useModel: ['usemodel', 'model', 'use_model'],
      source: ['source', 'src'],
      category: ['category', 'cat'],
      formId: ['formid', 'form'],
      remark: ['remark', 'remarks', 'note', 'notes'],
    };

    // Try to match CSV columns to our fields
    Object.keys(fieldMapping).forEach((fieldName) => {
      const possibleKeys = fieldMapping[fieldName];
      const csvKeys = Object.keys(row);

      for (const possibleKey of possibleKeys) {
        const matchingKey = csvKeys.find(
          (key) =>
            key.toLowerCase().includes(possibleKey.toLowerCase()) ||
            possibleKey.toLowerCase().includes(key.toLowerCase())
        );

        if (matchingKey && row[matchingKey]) {
          let fieldValue = row[matchingKey];

          // Special handling for useModel boolean field
          if (fieldName === 'useModel') {
            const lowerValue = String(fieldValue).toLowerCase();
            fieldValue =
              lowerValue === 'true' ||
              lowerValue === '1' ||
              lowerValue === 'yes' ||
              lowerValue === 'on';
          }

          mappedRow[fieldName] = fieldValue;
          break;
        }
      }
    });

    return mappedRow as ContentMappingItem;
  });
};

export const BulkImportModal = ({
  isOpen,
  onClose,
  multiCreateMutation,
}: BulkImportModalProps) => {
  const { t } = useTranslation();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [dragOver, setDragOver] = useState(false);

  const {
    bulkImportData,
    isBulkImportLoading,
    bulkImportError,
    fieldConfig,
    contentType,
    setBulkImportData,
    updateBulkImportRow,
    removeBulkImportRow,
    processBulkImport,
    downloadTemplate,
    closeBulkImportModal,
  } = useContentMappingStore();

  const handleFileSelect = (file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result as string;
      try {
        const csvData = parseCSV(content);
        const mappedData = mapCSVToFields(csvData, fieldConfig, contentType);
        setBulkImportData(mappedData);
      } catch (error) {
        console.error('Error parsing file:', error);
      }
    };
    reader.readAsText(file);
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
    const file = e.dataTransfer.files[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  };

  const handleSave = async () => {
    try {
      const success = await processBulkImport(multiCreateMutation);
      if (success) {
        toast({
          variant: 'success',
          title: t('ctint-mf-cdss.qmAdmin.bulkImportModal.success'),
          description: `${bulkImportData.length} ${t('ctint-mf-cdss.qmAdmin.bulkImportModal.itemsImportedSuccessfully')}`,
        });
        onClose();
      } else {
        toast({
          variant: 'error',
          title: t('ctint-mf-cdss.qmAdmin.bulkImportModal.importFailed'),
          description:
            bulkImportError ||
            t('ctint-mf-cdss.qmAdmin.bulkImportModal.failedToImportData'),
        });
      }
    } catch (error) {
      toast({
        variant: 'error',
        title: t('ctint-mf-cdss.qmAdmin.bulkImportModal.importFailed'),
        description:
          error instanceof Error
            ? error.message
            : t('ctint-mf-cdss.qmAdmin.bulkImportModal.unknownErrorOccurred'),
      });
    }
  };

  // Create columns for the preview table - memoized to prevent input focus loss
  const columns: ColumnDef<EditableContentMappingItem>[] = useMemo(
    () => [
      {
        id: 'actions',
        header: t('ctint-mf-cdss.qmAdmin.bulkImportModal.actions'),
        cell: ({ row }) => (
          <button
            onClick={() => removeBulkImportRow(row.index)}
            className="p-1 text-red-600 hover:bg-red-50 rounded"
            title={t('ctint-mf-cdss.qmAdmin.bulkImportModal.removeRow')}
          >
            <Trash2 size={16} />
          </button>
        ),
        enableSorting: false,
      },
      ...Object.keys(fieldConfig)
        .filter((field) => fieldConfig[field].visible)
        .map((field) => ({
          accessorKey: field,
          header: t(fieldConfig[field].label || field),
          cell: ({ row }: any) => {
            const value = (row.original as any)[field] || '';

            // Handle useModel field as a toggle switch
            if (field === 'useModel') {
              const isChecked =
                value === true ||
                value === 'true' ||
                value === 'on' ||
                value === 1 ||
                value === '1';

              return (
                <Switch
                  size="s"
                  checked={isChecked}
                  onChange={(e) =>
                    updateBulkImportRow(
                      row.index,
                      field as keyof ContentMappingItem,
                      e.target.checked // Pass actual boolean value
                    )
                  }
                />
              );
            }

            return (
              <Input
                value={value}
                onChange={(newValue) =>
                  updateBulkImportRow(
                    row.index,
                    field as keyof ContentMappingItem,
                    newValue
                  )
                }
                size="s"
                className="min-w-[120px]"
              />
            );
          },
        })),
    ],
    [fieldConfig, updateBulkImportRow, removeBulkImportRow]
  );

  return (
    <Popup
      open={isOpen}
      onOpenChange={onClose}
    >
      <PopupContent
        title={t('ctint-mf-cdss.qmAdmin.bulkImportModal.title')}
        className="max-w-6xl max-h-[90vh] flex flex-col"
      >
        <div className="p-6 flex-1 flex flex-col min-h-0">
          {bulkImportError && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md text-red-700">
              {bulkImportError}
            </div>
          )}

          {/* File Upload Section */}
          {bulkImportData.length === 0 && (
            <div className="mb-6">
              <div className="flex gap-3 mb-4">
                <Button
                  variant="secondary"
                  beforeIcon={<Download size={16} />}
                  onClick={downloadTemplate}
                  className="flex items-center"
                >
                  <span className="ml-1">
                    {t(
                      'ctint-mf-cdss.qmAdmin.bulkImportModal.downloadTemplate'
                    )}
                  </span>
                </Button>
                <Button
                  variant="secondary"
                  beforeIcon={<Upload size={16} />}
                  onClick={() => fileInputRef.current?.click()}
                  className="flex items-center"
                >
                  <span className="ml-1">
                    {t('ctint-mf-cdss.qmAdmin.bulkImportModal.chooseFile')}
                  </span>
                </Button>
              </div>

              {/* Drag & Drop Area */}
              <div
                className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                  dragOver
                    ? 'border-blue-400 bg-blue-50'
                    : 'border-gray-300 hover:border-gray-400'
                }`}
                onDrop={handleDrop}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
              >
                <Upload
                  className="mx-auto mb-4 text-gray-400"
                  size={48}
                />
                <p className="text-lg font-medium text-gray-700 mb-2">
                  {t('ctint-mf-cdss.qmAdmin.bulkImportModal.dragAndDrop')}
                </p>
                <p className="text-gray-500">
                  {t('ctint-mf-cdss.qmAdmin.bulkImportModal.orClickChooseFile')}
                </p>
                <p className="text-sm text-gray-400 mt-2">
                  {t('ctint-mf-cdss.qmAdmin.bulkImportModal.supportedFormats')}
                </p>
              </div>

              <input
                ref={fileInputRef}
                type="file"
                accept=".csv,.xlsx,.xls"
                onChange={handleFileInputChange}
                className="hidden"
              />
            </div>
          )}

          {/* Preview Table */}
          {bulkImportData.length > 0 && (
            <div className="flex-1 flex flex-col min-h-0">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium">
                  {t('ctint-mf-cdss.qmAdmin.bulkImportModal.preview')} (
                  {bulkImportData.length}{' '}
                  {t('ctint-mf-cdss.qmAdmin.bulkImportModal.rows')})
                </h3>
                <Button
                  variant="secondary"
                  onClick={() => setBulkImportData([])}
                >
                  {t('ctint-mf-cdss.qmAdmin.bulkImportModal.clearAll')}
                </Button>
              </div>

              <div className="flex-1 overflow-auto border rounded-lg">
                <DataTable
                  data={bulkImportData}
                  columns={columns}
                  loading={false}
                  emptyMessage={t(
                    'ctint-mf-cdss.qmAdmin.bulkImportModal.noDataToPreview'
                  )}
                  resize={true}
                />
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end gap-3 mt-6 pt-4 border-t">
            <Button
              variant="secondary"
              onClick={onClose}
              disabled={isBulkImportLoading}
            >
              {t('ctint-mf-cdss.qmAdmin.actionButtons.cancel')}
            </Button>
            {bulkImportData.length > 0 && (
              <Button
                onClick={handleSave}
                disabled={isBulkImportLoading}
                className="flex items-center gap-2"
              >
                {isBulkImportLoading ? (
                  <>
                    <Loader size={16} />
                    {t('ctint-mf-cdss.qmAdmin.bulkImportModal.importing')}
                  </>
                ) : (
                  t('ctint-mf-cdss.qmAdmin.bulkImportModal.importData')
                )}
              </Button>
            )}
          </div>
        </div>
      </PopupContent>
    </Popup>
  );
};
