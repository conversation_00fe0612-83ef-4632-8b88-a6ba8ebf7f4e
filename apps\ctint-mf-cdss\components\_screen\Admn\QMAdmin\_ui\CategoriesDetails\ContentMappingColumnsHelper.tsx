import { ColumnDef } from '@tanstack/react-table';
import { useState, useRef, useEffect } from 'react';
import { Check, Edit, Trash2, X, Plus, Undo2 } from 'lucide-react';
import { SortingButton } from '@cdss-modules/design-system';
import Checkbox from '@cdss-modules/design-system/components/_ui/Checkbox';
import Input from '@cdss-modules/design-system/components/_ui/Input';
import Switch from '@cdss-modules/design-system/components/_ui/Switch';
import { Select } from '@cdss-modules/design-system/components/_ui/Select';
import {
  Popup,
  PopupContent,
  PopupTrigger,
} from '@cdss-modules/design-system/components/_ui/Popup';
import { Button } from '@cdss-modules/design-system';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';

import {
  EditableContentMappingItem,
  FieldConfiguration,
  ContentMappingItem,
} from '../../_store/contentMappingStore';
import IconPencil from '@cdss-modules/design-system/components/_ui/Icon/IconPencil';
import { cn } from '@cdss-modules/design-system/lib/utils';
import IconPlus from '@cdss-modules/design-system/components/_ui/Icon/IconPlus';

// Type options for sensitive keyword mappings
const SENSITIVE_KEYWORD_TYPE_OPTIONS = [
  {
    id: 'sensitive_word',
    labelKey: 'ctint-mf-cdss.qmAdmin.contentMapping.typeOptions.sensitive_word',
    label: 'Sensitive Word',
    value: 'sensitive_word',
  },
  {
    id: 'additional_recording',
    labelKey:
      'ctint-mf-cdss.qmAdmin.contentMapping.typeOptions.additional_recording',
    label: 'Additional Recording',
    value: 'additional_recording',
  },
  {
    id: 'customer_response',
    labelKey:
      'ctint-mf-cdss.qmAdmin.contentMapping.typeOptions.customer_response',
    label: 'Customer Response',
    value: 'customer_response',
  },
  {
    id: 'important_word',
    labelKey: 'ctint-mf-cdss.qmAdmin.contentMapping.typeOptions.important_word',
    label: 'Important Word',
    value: 'important_word',
  },
];

// Function to get translated type options
const getTranslatedTypeOptions = (
  t?: (key: string, fallback?: string) => string
) => {
  return SENSITIVE_KEYWORD_TYPE_OPTIONS.map((option) => ({
    ...option,
    label: t ? t(option.labelKey, option.label) : option.label,
  }));
};

export interface SortHandler {
  orderBy?: string;
  order?: 'ASC' | 'DESC';
  onSort?: (orderBy: string, order: 'ASC' | 'DESC' | undefined) => void;
}

export interface ContentMappingTableActions {
  onAddMappingRow?: () => void;
  onUpdateMappingField?: (
    mappingId: string,
    field: keyof ContentMappingItem,
    value: any
  ) => void;
  onRemoveMappingRow?: (mappingId: string) => void | Promise<void>;
  onToggleMappingEdit?: (mappingId: string, isEditing: boolean) => void;
  onRevertMappingRow?: (mappingId: string) => void;
  onValidateAndSaveRow?: (
    mappingId: string,
    formValues: Partial<ContentMappingItem>
  ) => boolean | Promise<boolean>;
  getRowValidationErrors?: (mappingId: string) => Record<string, string>;
}

interface EditableCellProps {
  row: any;
  field: keyof ContentMappingItem;
  initialValue: string;
  editableFields: (keyof ContentMappingItem)[];
  onUpdateLocalValue: (field: keyof ContentMappingItem, value: string) => void;
  validationErrors: Record<string, string>;
  t?: (key: string, fallback?: string) => string;
}

// Component for editable cell
const EditableCell: React.FC<EditableCellProps> = ({
  row,
  field,
  initialValue,
  editableFields,
  onUpdateLocalValue,
  validationErrors,
  t,
}) => {
  // Properly convert initialValue to string, especially for boolean fields
  const getInitialStringValue = (
    value: any,
    fieldName: keyof ContentMappingItem
  ) => {
    if (fieldName === 'useModel') {
      // For useModel, convert boolean to string representation
      return String(Boolean(value));
    }
    if (fieldName === 'type') {
      // For type field, ensure we have a default value for sensitive keywords
      return String(value || 'sensitive_word');
    }
    return String(value || '');
  };

  const [value, setValue] = useState(
    getInitialStringValue(initialValue, field)
  );

  // Initialize default value for type field if it's empty
  useEffect(() => {
    if (field === 'type' && (!value || value.trim() === '')) {
      const defaultValue = 'sensitive_word'; // Default for sensitive keywords
      setValue(defaultValue);
      onUpdateLocalValue(field, defaultValue);
    }
  }, [field, value, onUpdateLocalValue]);

  const handleValueChange = (newValue: string | number | boolean) => {
    if (field === 'useModel') {
      // For useModel field, store actual boolean value
      setValue(String(newValue));
      onUpdateLocalValue(field, newValue as any); // Pass actual boolean
    } else {
      // For other fields, convert to string as before
      const stringValue = String(newValue);
      setValue(stringValue);
      onUpdateLocalValue(field, stringValue);
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Tab') {
      event.preventDefault();
      const currentIndex = editableFields.indexOf(field);
      if (currentIndex === -1) return;

      const nextIndex = currentIndex + (event.shiftKey ? -1 : 1);

      if (nextIndex >= 0 && nextIndex < editableFields.length) {
        const nextField = editableFields[nextIndex];
        const nextInputId = `editable-input-${row.original.id}-${nextField}`;
        const nextInput = document.getElementById(
          nextInputId
        ) as HTMLInputElement;
        nextInput?.focus();
      }
    }
  };

  const fieldError = validationErrors[field];
  const hasError = Boolean(fieldError);
  const inputClass = hasError ? 'border-red-500 bg-red-50' : '';

  // Handle useModel field as a toggle switch
  if (field === 'useModel') {
    const isChecked = value === 'true';

    return (
      <div className="relative">
        <Switch
          size="s"
          checked={isChecked}
          onChange={(e) => handleValueChange(e.target.checked)}
          onClick={(e) => e.stopPropagation()}
        />
        {hasError && (
          <div className="absolute top-full left-0 z-10 mt-1 text-xs text-red-600 bg-red-50 border border-red-200 rounded px-2 py-1 shadow-lg whitespace-nowrap">
            {fieldError}
          </div>
        )}
      </div>
    );
  }

  // Handle type field as a select dropdown
  if (field === 'type') {
    // Ensure we have a default value for new rows
    const selectValue = value || 'sensitive_word';

    return (
      <div className="relative">
        <Select
          options={getTranslatedTypeOptions(t)}
          value={selectValue}
          onChange={(newValue) => handleValueChange(newValue)}
          placeholder="Select type"
          isPagination={false}
          triggerClassName={hasError ? 'border-red-500 bg-red-50' : ''}
        />
        {hasError && (
          <div className="absolute top-full left-0 z-10 mt-1 text-xs text-red-600 bg-red-50 border border-red-200 rounded px-2 py-1 shadow-lg whitespace-nowrap">
            {fieldError}
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="relative">
      <Input
        id={`editable-input-${row.original.id}-${field}`}
        value={value}
        onChange={handleValueChange}
        onKeyDown={handleKeyDown}
        size="s"
        onClick={(e) => e.stopPropagation()}
        className={inputClass}
      />
      {hasError && (
        <div className="absolute top-full left-0 z-10 mt-1 text-xs text-red-600 bg-red-50 border border-red-200 rounded px-2 py-1 shadow-lg whitespace-nowrap">
          {fieldError}
        </div>
      )}
    </div>
  );
};

// Component for changed field display
const ChangedFieldDisplay: React.FC<{
  originalValue?: string;
  currentValue: string;
  field?: keyof ContentMappingItem;
  t?: (key: string, fallback?: string) => string;
}> = ({ originalValue, currentValue, field, t }) => {
  if (originalValue === currentValue) return <div>{currentValue}</div>;

  // Special handling for type field to show labels
  if (field === 'type') {
    const originalOption = getTranslatedTypeOptions(t).find(
      (option) => option.value === originalValue
    );
    const currentOption = getTranslatedTypeOptions(t).find(
      (option) => option.value === currentValue
    );

    return (
      <div className="space-y-1">
        <div className="text-gray-400 line-through text-xs">
          {originalOption?.label || originalValue}
        </div>
        <div className="text-blue-600 font-medium">
          {currentOption?.label || currentValue}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-1">
      <div className="text-gray-400 line-through text-xs">{originalValue}</div>
      <div className="text-blue-600 font-medium">{currentValue}</div>
    </div>
  );
};

// Confirmation popup component
const SaveConfirmationPopup: React.FC<{
  onConfirm: () => void;
  onCancel: () => void;
  children: React.ReactNode;
}> = ({ onConfirm, onCancel, children }) => {
  const { t } = useTranslation();
  const [open, setOpen] = useState(false);

  const handleConfirm = () => {
    onConfirm();
    setOpen(false);
  };

  const handleCancel = () => {
    onCancel();
    setOpen(false);
  };

  return (
    <Popup
      open={open}
      onOpenChange={setOpen}
    >
      <PopupTrigger asChild>{children}</PopupTrigger>
      <PopupContent
        title={t(
          'ctint-mf-cdss.qmAdmin.contentMapping.popupModals.saveConfirmation.title',
          'Confirm Save Changes'
        )}
        className="max-w-md"
      >
        <div className="p-6">
          <p className="text-gray-600 mb-6">
            {t(
              'ctint-mf-cdss.qmAdmin.contentMapping.popupModals.saveConfirmation.message',
              'Are you sure you want to save the changes to this row?'
            )}
          </p>
          <div className="flex justify-end gap-3">
            <Button
              variant="secondary"
              onClick={handleCancel}
            >
              {t(
                'ctint-mf-cdss.qmAdmin.contentMapping.popupModals.saveConfirmation.cancel',
                'Cancel'
              )}
            </Button>
            <Button onClick={handleConfirm}>
              {t(
                'ctint-mf-cdss.qmAdmin.contentMapping.popupModals.saveConfirmation.confirm',
                'Confirm Save'
              )}
            </Button>
          </div>
        </div>
      </PopupContent>
    </Popup>
  );
};

// Deletion confirmation popup component
const DeleteConfirmationPopup: React.FC<{
  onConfirm: () => void;
  onCancel: () => void;
  children: React.ReactNode;
}> = ({ onConfirm, onCancel, children }) => {
  const { t } = useTranslation();
  const [open, setOpen] = useState(false);

  const handleConfirm = () => {
    onConfirm();
    setOpen(false);
  };

  const handleCancel = () => {
    onCancel();
    setOpen(false);
  };

  return (
    <Popup
      open={open}
      onOpenChange={setOpen}
    >
      <PopupTrigger asChild>{children}</PopupTrigger>
      <PopupContent
        title={t(
          'ctint-mf-cdss.qmAdmin.contentMapping.popupModals.deleteConfirmation.title',
          'Confirm Delete Row'
        )}
        className="max-w-md"
      >
        <div className="p-6">
          <p className="text-gray-600 mb-6">
            {t(
              'ctint-mf-cdss.qmAdmin.contentMapping.popupModals.deleteConfirmation.message',
              'Are you sure you want to permanently delete this row? This action cannot be undone.'
            )}
          </p>
          <div className="flex justify-end gap-3">
            <Button
              variant="secondary"
              onClick={handleCancel}
            >
              {t(
                'ctint-mf-cdss.qmAdmin.contentMapping.popupModals.deleteConfirmation.cancel',
                'Cancel'
              )}
            </Button>
            <Button onClick={handleConfirm}>
              {t(
                'ctint-mf-cdss.qmAdmin.contentMapping.popupModals.deleteConfirmation.confirm',
                'Delete'
              )}
            </Button>
          </div>
        </div>
      </PopupContent>
    </Popup>
  );
};

// Helper function to create select column
export const createSelectColumn = <
  T extends EditableContentMappingItem,
>(): ColumnDef<T> => ({
  id: 'select',
  header: ({ table }: any) => (
    <div
      className="bg-white z-30"
      onClick={(e) => e.stopPropagation()}
    >
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && 'indeterminate')
        }
        onChange={(e: any) => {
          const isSelected = e?.target.checked;
          table.toggleAllPageRowsSelected(isSelected);
        }}
      />
    </div>
  ),
  cell: ({ row }: any) => (
    <div
      className="z-0"
      onClick={(e) => e.stopPropagation()}
    >
      <Checkbox
        checked={row.getIsSelected()}
        onChange={(e: any) => {
          const isSelected = e?.target.checked;
          row.toggleSelected(isSelected);
        }}
      />
    </div>
  ),
  enableSorting: false,
  enableHiding: false,
});

// Helper function to create editable content mapping column
export const createContentMappingColumn = <
  T extends EditableContentMappingItem,
>(
  field: keyof ContentMappingItem,
  fieldConfig: FieldConfiguration,
  editableFields: (keyof ContentMappingItem)[],
  actions: ContentMappingTableActions,
  formValuesRef: React.MutableRefObject<
    Record<string, Partial<ContentMappingItem>>
  >,
  sortHandler?: SortHandler,
  t?: (key: string, fallback?: string) => string // Add translation function
): ColumnDef<T> => {
  const config = fieldConfig[field];
  if (!config || !config.visible) {
    return null as any; // Field not visible, return null (will be filtered out)
  }

  const handleHeaderSortClick = (columnId: string) => {
    if (sortHandler?.onSort) {
      if (sortHandler.orderBy === columnId) {
        // Column is already sorted, toggle direction or clear
        if (sortHandler.order === 'ASC') {
          sortHandler.onSort(columnId, 'DESC');
        } else if (sortHandler.order === 'DESC') {
          sortHandler.onSort('', undefined); // Clear sort by passing empty orderBy
        } else {
          sortHandler.onSort(columnId, 'ASC'); // This shouldn't happen, but just in case
        }
      } else {
        // New column to sort
        sortHandler.onSort(columnId, 'ASC');
      }
    }
  };

  return {
    accessorKey: field,
    id: field,
    header: sortHandler
      ? ({ column }) => (
          <SortingButton
            sorting={
              sortHandler.orderBy === field
                ? sortHandler.order === 'ASC'
                  ? 'asc'
                  : sortHandler.order === 'DESC'
                    ? 'desc'
                    : false
                : false
            }
            onClick={() => handleHeaderSortClick(field)}
          >
            {t ? t(config.label || field, field) : config.label || field}
          </SortingButton>
        )
      : t
        ? t(config.label || field, field)
        : config.label || field,
    cell: ({ row }) => {
      const rowData = row.original;

      // Update local form values helper
      const updateLocalFormValue = (
        fieldName: keyof ContentMappingItem,
        value: string
      ) => {
        if (!rowData.id) {
          console.warn('Cannot update form value for row without ID');
          return;
        }
        if (!formValuesRef.current[rowData.id]) {
          formValuesRef.current[rowData.id] = {};
        }
        (formValuesRef.current[rowData.id] as any)[fieldName] = value;
      };

      // Get validation errors for this row
      const rowErrors =
        (rowData.id && actions.getRowValidationErrors?.(rowData.id)) || {};

      if (rowData.isEditing && config.editable) {
        return (
          <EditableCell
            row={row}
            field={field}
            initialValue={(rowData as any)[field]}
            editableFields={editableFields}
            onUpdateLocalValue={updateLocalFormValue}
            validationErrors={rowErrors}
            t={t}
          />
        );
      }

      // Show changed field if there are changes
      if (rowData.hasChanges && rowData.originalValues) {
        const originalValue = (rowData.originalValues as any)[field];
        const currentValue = (rowData as any)[field];
        if (originalValue !== currentValue) {
          return (
            <ChangedFieldDisplay
              originalValue={originalValue}
              currentValue={currentValue}
              field={field}
              t={t}
            />
          );
        }
      }

      // Special display for useModel boolean field in read-only mode
      if (field === 'useModel') {
        const fieldValue = (rowData as any)[field];
        // Handle both boolean and string representations
        const isTrue =
          fieldValue === true ||
          fieldValue === 'true' ||
          fieldValue === 1 ||
          fieldValue === '1';
        return (
          <div className="flex items-center">
            <Switch
              size="s"
              checked={isTrue}
              disabled={true}
              className="pointer-events-none"
            />
          </div>
        );
      }

      // Special display for type field in read-only mode
      if (field === 'type') {
        const typeValue = (rowData as any)[field];
        const typeOption = getTranslatedTypeOptions(t).find(
          (option) => option.value === typeValue
        );
        return <div>{typeOption?.label || typeValue || ''}</div>;
      }

      return <div>{(rowData as any)[field] || ''}</div>;
    },
    enableSorting: !!sortHandler,
  };
};

// Helper function to create actions column
export const createActionsColumn = <T extends EditableContentMappingItem>(
  actions: ContentMappingTableActions,
  formValuesRef: React.MutableRefObject<
    Record<string, Partial<ContentMappingItem>>
  >,
  isEditMode: boolean,
  canEdit: boolean,
  hasUnsavedNewRows = false,
  hasRowsBeingEdited = false,
  t?: (key: string, fallback?: string) => string
): ColumnDef<T> => {
  return {
    id: 'actions',
    header: () => (
      <div className="w-full">
        {isEditMode && canEdit && actions.onAddMappingRow && (
          <button
            type="button"
            className={cn(
              `rounded-md p-1 transition-colors`,
              hasUnsavedNewRows || hasRowsBeingEdited
                ? 'opacity-50 cursor-not-allowed bg-gray-200 text-gray-400'
                : 'cursor-pointer hover:text-primary'
            )}
            onClick={
              hasUnsavedNewRows || hasRowsBeingEdited
                ? undefined
                : actions.onAddMappingRow
            }
            disabled={hasUnsavedNewRows || hasRowsBeingEdited}
            title={
              hasUnsavedNewRows || hasRowsBeingEdited
                ? t
                  ? t(
                      'ctint-mf-cdss.qmAdmin.contentMapping.popupModals.addRow.disabledTooltip',
                      'Please save or cancel the current editing row before adding a new row'
                    )
                  : 'Please save or cancel the current editing row before adding a new row'
                : t
                  ? t(
                      'ctint-mf-cdss.qmAdmin.contentMapping.popupModals.addRow.tooltip',
                      'Add new mapping'
                    )
                  : 'Add new mapping'
            }
          >
            <Plus size={16} />
          </button>
        )}
      </div>
    ),
    cell: ({ row }) => {
      const rowData = row.original;

      const startEditingRow = (id: string): void => {
        if (!id) {
          console.error('Cannot start editing row without ID');
          return;
        }
        // Initialize form values with current row data
        formValuesRef.current[id] = {
          value: rowData.value,
          englishDisplay: rowData.englishDisplay,
          mandarinDisplay: rowData.mandarinDisplay,
          displayValue: rowData.displayValue,
          keyDefinition: rowData.keyDefinition,
          standardScriptId: rowData.standardScriptId,
          regexFormula: rowData.regexFormula,
          useModel: rowData.useModel, // Include useModel field
          type: rowData.type, // Include type field
          source: rowData.source,
          category: rowData.category,
          formId: rowData.formId,
          remark: rowData.remark,
        };
        actions.onToggleMappingEdit?.(id, true);
      };

      const saveEditedRow = async (id: string): Promise<void> => {
        if (!id) {
          console.error('Cannot save row without ID');
          return;
        }
        const formValues = formValuesRef.current[id];
        if (!formValues || !actions.onValidateAndSaveRow) {
          console.error('No form values found for row', id);
          return;
        }

        try {
          const result = actions.onValidateAndSaveRow(id, formValues);
          const isValid = result instanceof Promise ? await result : result;

          if (isValid) {
            delete formValuesRef.current[id];
          }
        } catch (error) {
          console.error('Error saving row:', error);
        }
      };

      const cancelEditingRow = (id: string): void => {
        if (!id) {
          console.error('Cannot cancel editing row without ID');
          return;
        }
        delete formValuesRef.current[id];
        actions.onRevertMappingRow?.(id);
      };

      const deleteRow = async (id: string): Promise<void> => {
        if (!id) {
          console.error('Cannot delete row without ID');
          return;
        }
        try {
          delete formValuesRef.current[id];
          const result = actions.onRemoveMappingRow?.(id);
          if (result instanceof Promise) {
            await result;
          }
        } catch (error) {
          console.error('Error deleting row:', error);
        }
      };

      // Don't render actions if row doesn't have an ID
      if (!rowData.id) {
        return (
          <div className="flex gap-x-2 z-0 text-red-500 text-xs">No ID</div>
        );
      }

      // Check if this row should be disabled due to other rows being edited
      const isOtherRowBeingEdited = hasRowsBeingEdited && !rowData.isEditing;

      return (
        <div
          className="flex gap-x-1 z-0"
          onClick={(e) => e.stopPropagation()}
        >
          {rowData.isEditing ? (
            <>
              <SaveConfirmationPopup
                onConfirm={() => saveEditedRow(rowData.id)}
                onCancel={() => console.log('Save cancelled')}
              >
                <button
                  className="rounded p-1 hover:bg-green-100 text-green-600"
                  title={
                    t
                      ? t(
                          'ctint-mf-cdss.qmAdmin.contentMapping.popupModals.actions.saveChanges',
                          'Save changes'
                        )
                      : 'Save changes'
                  }
                >
                  <Check size={16} />
                </button>
              </SaveConfirmationPopup>
              <button
                className="rounded p-1 hover:bg-red-100 text-red-600"
                onClick={() => cancelEditingRow(rowData.id)}
                title={
                  t
                    ? t(
                        'ctint-mf-cdss.qmAdmin.contentMapping.popupModals.actions.cancelEditing',
                        'Cancel editing'
                      )
                    : 'Cancel editing'
                }
              >
                <X size={16} />
              </button>
            </>
          ) : (
            <>
              {canEdit && actions.onUpdateMappingField && (
                <button
                  className={cn(
                    `rounded p-1 transition-colors ${
                      isOtherRowBeingEdited
                        ? 'opacity-50 cursor-not-allowed bg-gray-200 text-gray-400'
                        : 'cursor-pointer hover:text-primary'
                    }`
                  )}
                  onClick={
                    isOtherRowBeingEdited
                      ? undefined
                      : () => startEditingRow(rowData.id)
                  }
                  disabled={isOtherRowBeingEdited}
                  title={
                    isOtherRowBeingEdited
                      ? t
                        ? t(
                            'ctint-mf-cdss.qmAdmin.contentMapping.popupModals.editRow.disabledTooltip',
                            'Please save or cancel the current editing row before editing this row'
                          )
                        : 'Please save or cancel the current editing row before editing this row'
                      : t
                        ? t(
                            'ctint-mf-cdss.qmAdmin.contentMapping.popupModals.editRow.tooltip',
                            'Edit row'
                          )
                        : 'Edit row'
                  }
                >
                  <IconPencil size="14" />
                </button>
              )}
              {canEdit && actions.onRemoveMappingRow && (
                <DeleteConfirmationPopup
                  onConfirm={() => deleteRow(rowData.id)}
                  onCancel={() => console.log('Delete cancelled')}
                >
                  <button
                    className={cn(
                      `rounded p-1 transition-colors ${
                        isOtherRowBeingEdited
                          ? 'opacity-50 cursor-not-allowed bg-gray-200 text-gray-400'
                          : 'cursor-pointer hover:text-red-600'
                      }`
                    )}
                    disabled={isOtherRowBeingEdited}
                    title={
                      isOtherRowBeingEdited
                        ? t
                          ? t(
                              'ctint-mf-cdss.qmAdmin.contentMapping.popupModals.deleteRow.disabledTooltip',
                              'Please save or cancel the current editing row before deleting this row'
                            )
                          : 'Please save or cancel the current editing row before deleting this row'
                        : t
                          ? t(
                              'ctint-mf-cdss.qmAdmin.contentMapping.popupModals.deleteRow.tooltip',
                              'Delete row'
                            )
                          : 'Delete row'
                    }
                  >
                    <Trash2 size={16} />
                  </button>
                </DeleteConfirmationPopup>
              )}
            </>
          )}
        </div>
      );
    },
    enableSorting: false,
    enableHiding: false,
  };
};

// Hook to manage form values ref
export const useContentMappingFormValues = () => {
  return useRef<Record<string, Partial<ContentMappingItem>>>({});
};

// Main function to create content mapping columns
export const createContentMappingColumns = <
  T extends EditableContentMappingItem,
>(
  fieldConfig: FieldConfiguration,
  actions: ContentMappingTableActions,
  isEditMode: boolean,
  canEdit: boolean,
  formValuesRef: React.MutableRefObject<
    Record<string, Partial<ContentMappingItem>>
  >,
  sortHandler?: SortHandler,
  hasUnsavedNewRows = false,
  hasRowsBeingEdited = false,
  t?: (key: string, fallback?: string) => string // Add translation function
): {
  columns: ColumnDef<T>[];
} => {
  // Get visible and editable fields
  const visibleFields = Object.keys(fieldConfig).filter(
    (field) => fieldConfig[field].visible
  ) as (keyof ContentMappingItem)[];

  const editableFields = visibleFields.filter(
    (field) => fieldConfig[field].editable
  );

  const columns: ColumnDef<T>[] = [
    // createSelectColumn<T>(),
    ...visibleFields
      .map((field) =>
        createContentMappingColumn<T>(
          field,
          fieldConfig,
          editableFields,
          actions,
          formValuesRef,
          sortHandler,
          t
        )
      )
      .filter(Boolean), // Remove null columns (invisible fields)
  ];

  // Only add actions column when in edit mode
  if (isEditMode) {
    columns.push(
      createActionsColumn<T>(
        actions,
        formValuesRef,
        isEditMode,
        canEdit,
        hasUnsavedNewRows,
        hasRowsBeingEdited,
        t
      )
    );
  }

  return { columns };
};
