'use client';

import React from 'react';

interface JumpingCriteriaViewProps {
  jumpingCriteria: string;
}

export const JumpingCriteriaView: React.FC<JumpingCriteriaViewProps> = ({
  jumpingCriteria,
}) => {
  return (
    <div className="p-2 bg-white border rounded-md min-h-[100px]">
      {jumpingCriteria || 'No jumping criteria content available'}
    </div>
  );
};

export default JumpingCriteriaView;
