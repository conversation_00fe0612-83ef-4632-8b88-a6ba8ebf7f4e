'use client';

import React, { useState } from 'react';
import Button from '@cdss-modules/design-system/components/_ui/Button';
import Input from '@cdss-modules/design-system/components/_ui/Input';
import EnhancedTextarea from './EnhancedTextarea';

interface ScriptSectionContentProps {
  script: string;
  onSave?: (newScript: string) => void;
  disabled?: boolean;
}

export const ScriptSectionContent: React.FC<ScriptSectionContentProps> = ({
  script,
  onSave,
  disabled = false,
}) => {
  const [englishValue, setEnglishValue] = useState(script || '');
  const [chinesePutonghuaValue, setChinesePutonghuaValue] = useState('');
  const [chineseCantoneseValue, setChineseCantoneseValue] = useState('');
  const [similarity, setSimilarity] = useState(80); // Default to 80%

  const handleSimilarityChange = (value: string | number) => {
    if (!disabled) {
      const stringValue = value.toString();
      // Allow empty string or valid numbers
      if (
        stringValue === '' ||
        (/^\d+$/.test(stringValue) &&
          parseInt(stringValue) >= 0 &&
          parseInt(stringValue) <= 100)
      ) {
        const numValue = stringValue === '' ? 0 : parseInt(stringValue);
        console.log('ScriptSectionEdit - Similarity changing to:', numValue);
        setSimilarity(numValue);
      }
    }
  };

  const handleSave = () => {
    if (!disabled && onSave) {
      onSave(englishValue);
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-start items-center gap-2">
        <span className="text-sm font-medium">Similarity ≥</span>
        <div className="relative w-20">
          <Input
            type="text"
            value={similarity.toString()}
            onChange={handleSimilarityChange}
            disabled={disabled}
            className="pr-6 text-center"
            size="s"
            placeholder="80"
            min="0"
            max="100"
          />
          <span className="absolute right-2 top-1/2 transform -translate-y-1/2 text-sm text-gray-500 pointer-events-none">
            %
          </span>
        </div>
      </div>

      <EnhancedTextarea
        label="English"
        value={englishValue}
        onChange={setEnglishValue}
        placeholder={disabled ? '' : 'Enter English script content below...'}
        disabled={disabled}
      />

      <EnhancedTextarea
        label="Chinese (Putonghua)"
        value={chinesePutonghuaValue}
        onChange={setChinesePutonghuaValue}
        placeholder={
          disabled ? '' : 'Enter Chinese (Putonghua) script content below...'
        }
        disabled={disabled}
      />

      <EnhancedTextarea
        label="Chinese (Cantonese)"
        value={chineseCantoneseValue}
        onChange={setChineseCantoneseValue}
        placeholder={
          disabled ? '' : 'Enter Chinese (Cantonese) script content below...'
        }
        disabled={disabled}
      />

      {!disabled && (
        <div className="flex justify-end">
          <Button
            variant="primary"
            onClick={handleSave}
          >
            Save
          </Button>
        </div>
      )}
    </div>
  );
};

export default ScriptSectionContent;
