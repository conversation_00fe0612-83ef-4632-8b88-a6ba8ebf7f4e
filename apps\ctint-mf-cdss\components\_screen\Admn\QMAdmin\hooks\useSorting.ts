import { useState, useCallback } from 'react';

export interface SortConfig {
  orderBy: string;
  order: 'ASC' | 'DESC';
}

export interface UseSortingOptions {
  defaultOrderBy?: string;
  defaultOrder?: 'ASC' | 'DESC';
  onSortChange?: (orderBy: string, order: 'ASC' | 'DESC') => void;
}

export interface UseSortingReturn {
  orderBy: string;
  order: 'ASC' | 'DESC';
  handleSortChange: (
    newOrderBy: string,
    newOrder: 'ASC' | 'DESC' | undefined
  ) => void;
  resetSort: () => void;
  sortConfig: SortConfig;
}

export const useSorting = ({
  defaultOrderBy = 'createdDate',
  defaultOrder = 'DESC',
  onSortChange,
}: UseSortingOptions = {}): UseSortingReturn => {
  const [orderBy, setOrderBy] = useState(defaultOrderBy);
  const [order, setOrder] = useState<'ASC' | 'DESC'>(defaultOrder);

  const handleSortChange = useCallback(
    (newOrderBy: string, newOrder: 'ASC' | 'DESC' | undefined) => {
      let finalOrderBy: string;
      let finalOrder: 'ASC' | 'DESC';

      if (newOrderBy === '' || newOrder === undefined) {
        // Clear sort - set to default
        finalOrderBy = defaultOrderBy;
        finalOrder = defaultOrder;
      } else {
        // Set specific sort
        finalOrderBy = newOrderBy;
        finalOrder = newOrder;
      }

      setOrderBy(finalOrderBy);
      setOrder(finalOrder);

      // Call the optional callback
      onSortChange?.(finalOrderBy, finalOrder);
    },
    [defaultOrderBy, defaultOrder, onSortChange]
  );

  const resetSort = useCallback(() => {
    setOrderBy(defaultOrderBy);
    setOrder(defaultOrder);
    onSortChange?.(defaultOrderBy, defaultOrder);
  }, [defaultOrderBy, defaultOrder, onSortChange]);

  return {
    orderBy,
    order,
    handleSortChange,
    resetSort,
    sortConfig: { orderBy, order },
  };
};
