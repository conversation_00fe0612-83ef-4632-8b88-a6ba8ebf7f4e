'use client';

import { useState, useMemo, useEffect, useCallback, useRef } from 'react';
import { Loader, toast } from '@cdss-modules/design-system';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';
import { useSearchParams } from 'react-router-dom';
// import { MetadataMappingTable } from './MetadataMappingTable'; // Will be re-evaluated based on store usage
// import { useMetadataMapping } from '../../../../hooks/useSOPData'; // Replaced by store

import { EditableBreadcrumb } from '../../../../_ui/CategoriesDetails/EditableBreadcrumb';
import { SearchBar } from '../../../../_ui/CategoriesDetails/SearchBar';
import { AdvancedSearchBar } from '../../../../_ui/CategoriesDetails/AdvancedSearchBar';
import { ActionButtons } from '../../../../_ui/CategoriesDetails/ActionButtons';
import {
  CommentSection,
  CommentType,
} from '../../../../_ui/CategoriesDetails/CommentSection';
import { usePermissions, Status } from '../../../../context/PermissionsContext';
import { useTab } from '../../../../context/TabContext';

import {
  useContentMappingStore,
  ContentMappingFormData,
  ContentMappingItem,
} from '../../../../_store/contentMappingStore';
import {
  useUnifiedDefinitionByIdQuery,
  useUnifiedUpdateDefinitionStatusMutation,
  useDuplicateDefinitionMutation,
} from '../../../definition-queries';

import {
  useContentMappingListQuery,
  useContentMappingCreateMutation,
  useContentMappingUpdateMutation,
  useContentMappingDeleteMutation,
  useContentMappingMultiCreateMutation,
  UnifiedMappingItem,
} from '../../../content-mapping-queries';

import { debounce } from 'lodash';

import {
  createContentMappingColumns,
  ContentMappingTableActions,
  useContentMappingFormValues,
} from '../../../../_ui/CategoriesDetails/ContentMappingColumnsHelper';
import { ContentMappingTable } from '../../../../_ui/CategoriesDetails/ContentMappingTable';
import { BulkImportModal } from '../../../../_ui/CategoriesDetails/BulkImportModal';
import { DuplicateModal } from '../../../../_ui/CategoriesDetails/DuplicateModal';
import { useSorting } from '../../../../hooks/useSorting';

interface MetadataDetailsProps {
  metadataId: string;
}

export const MetadataDetails = ({ metadataId }: MetadataDetailsProps) => {
  const { t } = useTranslation();
  const [searchParams] = useSearchParams();
  const [isEditMode, setIsEditMode] = useState(false);
  const [comments, setComments] = useState<CommentType[]>([]);
  const [isDuplicateModalOpen, setIsDuplicateModalOpen] = useState(false);
  const [isDuplicating, setIsDuplicating] = useState(false);

  const {
    formData,
    isLoading: isStoreLoading,
    error,
    updateMetadataField,
    resetForm,
    addMappingRow,
    updateMappingField,
    removeMappingRow,
    toggleMappingEdit,
    revertMappingRow,
    loadExistingData,
    createNew,
    isDirty,
    isSubmitting,
    clear,
    setCategoryId,
    setPendingApprovalStatus,
    validateAndSaveRow,
    getRowValidationErrors,
    setContentType,
    fieldConfig,
    hasUnsavedNewRows,
    hasRowsBeingEdited,
    isBulkImportModalOpen,
    openBulkImportModal,
    closeBulkImportModal,
  } = useContentMappingStore();

  // Local state for search and pagination of mappings
  const [searchValue, setSearchValue] = useState(''); // Default: empty string
  const [searchField, setSearchField] = useState('keyDefinition'); // Default search field
  const [currentPage, setCurrentPage] = useState(1); // Default: 1
  const [perPage, setPerPage] = useState(50); // Default: 50
  const [statusFilter, setStatusFilter] = useState('');

  // Use the sorting hook for the mappings table
  const { orderBy, order, handleSortChange } = useSorting({
    defaultOrderBy: 'createTime',
    defaultOrder: 'DESC',
  });

  const [debouncedSearchValue, setDebouncedSearchValue] = useState(searchValue);

  // Use useRef to create a stable debounced function that doesn't recreate on every render
  const debouncedSearchRef = useRef(
    debounce((value: string) => {
      setDebouncedSearchValue(value);
      setCurrentPage(1); // Reset to first page on new search
    }, 1000)
  );

  // Simplified useEffect without manual timeout management
  useEffect(() => {
    debouncedSearchRef.current(searchValue);
  }, [searchValue]);

  const {
    handleBackToList,
    categoryName: contextCategoryName,
    setSelectedItem,
  } = useTab();

  // Get categoryId from URL query parameter
  const categoryId = searchParams.get('id') || '1';

  // Use separate unified queries for metadata definition and mappings
  const {
    data: metadataDefinitionResponse,
    isLoading: isDefinitionLoading,
    error: definitionError,
    refetch: refetchDefinition,
  } = useUnifiedDefinitionByIdQuery(metadataId.toString());

  const name = metadataDefinitionResponse?.data?.name;
  const version = metadataDefinitionResponse?.data?.version;
  const status = metadataDefinitionResponse?.data?.status;
  const createdBy = metadataDefinitionResponse?.data?.createBy;

  const {
    data: mappingsResponse,
    isLoading: isMappingsLoading,
    error: mappingsError,
    refetch: refetchMappings,
  } = useContentMappingListQuery(
    'metadata',
    metadataId.toString(),
    debouncedSearchValue,
    statusFilter,
    currentPage,
    perPage,
    orderBy,
    order,
    searchField
  );

  // Initialize mapping mutation hooks
  const createMappingMutation = useContentMappingCreateMutation();
  const updateMappingMutation = useContentMappingUpdateMutation();
  const deleteMappingMutation = useContentMappingDeleteMutation();
  const multiCreateMappingMutation = useContentMappingMultiCreateMutation();

  // Initialize status update mutation hook
  const updateStatusMutation = useUnifiedUpdateDefinitionStatusMutation();

  // Initialize duplicate definition mutation hook
  const duplicateDefinitionMutation = useDuplicateDefinitionMutation();

  // Initialize form values ref for table editing
  const formValuesRef = useContentMappingFormValues();

  // Load metadata definition data into store when available
  useEffect(() => {
    if (metadataDefinitionResponse && !isDefinitionLoading) {
      setCategoryId(categoryId);
      setContentType('metadata'); // Set the content type to metadata

      // Transform API response to match our form structure
      const metadataDefinition = metadataDefinitionResponse.data;
      const mainData = {
        id: metadataDefinition.id,
        name: metadataDefinition.name,
        status: metadataDefinition.status,
        version: metadataDefinition.version,
        createdBy: metadataDefinition.createBy,
        approvedBy: metadataDefinition.approveBy || '',
        lastUpdated: metadataDefinition.updateTime,
        type: 'metadata',
      };

      // Only load if we don't have data or if the ID is different
      if (!formData?.id || formData.id !== metadataDefinition.id) {
        loadExistingData(mainData, [], {
          total: 0,
          totalPages: 0,
          currentPage: currentPage,
          pageSize: perPage,
        });
      }
    }
  }, [
    metadataDefinitionResponse,
    isDefinitionLoading,
    categoryId,
    setCategoryId,
    setContentType,
    loadExistingData,
    formData?.id, // Only track the ID to avoid full formData dependency
  ]);

  // Load mappings data into store when available - memoized to prevent excessive re-renders
  const mappingsDataMemo = useMemo(() => {
    if (!mappingsResponse || isMappingsLoading || !formData?.id) {
      return null;
    }

    console.log('Processing mappings response:', mappingsResponse.data.items);

    return mappingsResponse.data.items.map((mapping: UnifiedMappingItem) => {
      const processedMapping = {
        id: mapping.entityId, // Map entityId to id for internal use
        contentDefinitionId: mapping.relationshipId, // Use relationshipId as contentDefinitionId
        standardScriptId: mapping.standardScriptId || '',
        type: mapping.type,
        keyDefinition: mapping.keyDefinition,
        value: mapping.value,
        regexFormula: mapping.regexFormula || '',
        useModel: mapping.useModel || false,
        source: mapping.source || '',
        category: mapping.category || '',
        formId: mapping.formId || '',
        remark: mapping.remark || '',
        language: mapping.language,
        displayValue: mapping.displayValue,
        englishDisplay: mapping.englishDisplay,
        mandarinDisplay: mapping.mandarinDisplay,
        platform: mapping.platform,
        tenant: mapping.tenant,
        createdBy: mapping.createBy, // Map createBy to createdBy
        updatedBy: mapping.updateBy, // Map updateBy to updatedBy
        createTime: mapping.createTime,
        updateTime: mapping.updateTime || '',
      };

      console.log('Processed mapping:', processedMapping);

      if (!processedMapping.id) {
        console.error('Mapping without ID detected:', mapping);
      }

      return processedMapping;
    });
  }, [mappingsResponse, isMappingsLoading, formData?.id]);

  useEffect(() => {
    if (mappingsDataMemo && formData?.id) {
      // Update the store with mappings data while preserving existing metadata
      const currentMainData = {
        id: formData.id || '',
        name: formData.name,
        status: formData.status,
        version: formData.version,
        createdBy: formData.createdBy || '',
        approvedBy: formData.approvedBy || '',
        lastUpdated: formData.lastUpdated || '',
        type: formData.type,
      };

      loadExistingData(currentMainData, mappingsDataMemo, {
        total: mappingsResponse?.data.totalCount || 0,
        totalPages: mappingsResponse?.data.totalPage || 0,
        currentPage: currentPage,
        pageSize: perPage,
      });
    }
  }, [mappingsDataMemo, currentPage, perPage, loadExistingData]);

  // Cleanup store when component unmounts
  useEffect(() => {
    return () => {
      clear();
      // Cancel any pending debounced calls to prevent memory leaks
      debouncedSearchRef.current.cancel();
    };
  }, [clear]);

  const { canEdit, canApprove } = usePermissions();
  const isEditableByPermissions = canEdit(status as Status, createdBy);

  console.log('isEditableByPermissions', isEditableByPermissions);

  // Check if there are any rows currently being edited or incomplete new rows
  const hasIncompleteRows = useMemo(() => {
    if (!formData?.mappings) return false;

    return formData.mappings.some((mapping) => {
      if (mapping.isEditing) return true;
      if (mapping.isNew && !mapping.value?.trim()) {
        return true;
      }
      return false;
    });
  }, [formData?.mappings]);

  // Use server-side pagination data directly instead of client-side filtering
  const mappingsData = formData?.mappings || [];
  const totalItems = formData?.mappingsPagination?.total || 0;
  const totalPages = formData?.mappingsPagination?.totalPages || 0;

  const handlePageChange = async (newPage: number) => {
    setCurrentPage(newPage);
  };

  const handlePerPageChange = async (newPerPage: number) => {
    setPerPage(newPerPage);
    setCurrentPage(1); // Reset to first page when changing page size
  };

  const breadcrumbItems = [
    {
      label: t('ctint-mf-cdss.qmAdmin.sopCategories.title'),
      link: '/ctint/mf-cdss/admin/qm-admin',
    },
    {
      label:
        contextCategoryName || t('ctint-mf-cdss.qmAdmin.breadcrumb.category'),
      onClick: handleBackToList,
    },
    {
      label: t('ctint-mf-cdss.qmAdmin.metadata.title'),
      // onClick: handleBackToList,
    },
  ];

  const handleTitleChange = (value: string) => {
    updateMetadataField('name', value);
  };

  const handleVersionChange = (value: string) => {
    updateMetadataField('version', value);
  };

  const handleToggleEditMode = () => {
    if (isEditMode) {
      resetForm();
      setIsEditMode(false);
    } else if (isEditableByPermissions) {
      setIsEditMode(true);
    }
  };

  const handleSaveChanges = async () => {
    // Note: This would need to be updated to match the new API structure
    // For now, keeping the basic structure but this will need backend alignment
    console.log('Save changes with new store structure:', formData);
    setIsEditMode(false);
    // TODO: Implement actual save logic with the new structure
  };

  const handleSubmitForApproval = async () => {
    if (!metadataId) {
      console.error('Metadata ID is not available');
      toast({
        variant: 'error',
        title: 'Error',
        description: 'Metadata ID is not available',
      });
      return;
    }

    try {
      console.log('Submitting for approval with ID:', metadataId);
      const result = await updateStatusMutation.mutateAsync({
        id: metadataId,
        request: {
          status: 'Approved',
        },
      });

      console.log('Submit for approval result:', result);

      // Refetch the metadata definition to get updated data
      refetchDefinition();
      setIsEditMode(false);

      console.log('Metadata submitted for approval successfully');
    } catch (error) {
      console.error('Submit for approval failed:', error);
      // Additional error handling since the mutation might be throwing but API succeeds
      toast({
        variant: 'error',
        title: 'Error',
        description: `Failed to submit for approval: ${error instanceof Error ? error.message : 'Unknown error'}`,
      });
    }
  };

  const handleAddComment = (comment: CommentType) => {
    setComments([...comments, comment]);
  };

  const handleBulkImport = () => {
    openBulkImportModal();
  };

  const handleDuplicate = () => {
    setIsDuplicateModalOpen(true);
  };

  const handleDuplicateConfirm = async (newVersion: string) => {
    if (!formData || !metadataId) {
      toast({
        variant: 'error',
        title: 'Error',
        description: 'No metadata data available for duplication',
      });
      return;
    }

    setIsDuplicating(true);

    try {
      // Use the generic duplicate hook
      const result = await duplicateDefinitionMutation.mutateAsync({
        originalId: metadataId,
        newVersion: newVersion,
        parentId: categoryId,
        relationshipType: 'metadata',
        contentType: 'metadata',
      });

      // Close modal and set new item as selected
      setIsDuplicateModalOpen(false);

      // Set the new metadata as selected item - this will switch to the new item
      setSelectedItem({ type: 'metadata', id: result.newDefinitionId });
    } catch (error) {
      console.error('Failed to duplicate metadata:', error);
      // Error handling is already done in the hook
    } finally {
      setIsDuplicating(false);
    }
  };

  const handleDuplicateCancel = () => {
    setIsDuplicateModalOpen(false);
  };

  // Handler for sorting changes from table
  const handleTableSortChange = (
    newOrderBy: string,
    newOrder: 'ASC' | 'DESC' | undefined
  ) => {
    handleSortChange(newOrderBy, newOrder);
    setCurrentPage(1); // Reset to first page on sort change
  };

  // API Handlers for mapping operations
  const handleCreateMapping = async (mappingData: any) => {
    try {
      const createRequest = {
        type: 'metadata',
        keyDefinition: mappingData.keyDefinition || '',
        value: mappingData.value || '',
        language: mappingData.language || 'zh',
        displayValue: mappingData.displayValue || '',
        relationshipId: metadataId.toString(),
        englishDisplay: mappingData.englishDisplay || '',
        mandarinDisplay: mappingData.mandarinDisplay || '',
        tenant: mappingData.tenant || 'ccba',
        // platform: mappingData.platform || 'web',
        // regexFormula: mappingData.regexFormula || '',
        // source: mappingData.source || '',
        // category: mappingData.category || '',
        // formId: mappingData.formId || '',
        // remark: mappingData.remark || '',
      };

      const result = await createMappingMutation.mutateAsync({
        contentType: 'metadata',
        request: createRequest,
      });

      // Update the local store with the new mapping item
      if (result.data) {
        // The mutation will automatically invalidate the queries and refresh the list
        toast({
          variant: 'success',
          title: 'Success',
          description: 'Mapping item created successfully',
        });
      }

      return result.data;
    } catch (error) {
      console.error('Failed to create mapping:', error);
      toast({
        variant: 'error',
        title: 'Error',
        description: 'Failed to create mapping item',
      });
      throw error;
    }
  };

  const handleUpdateMapping = async (mappingId: string, mappingData: any) => {
    try {
      const updateRequest = {
        type: 'metadata',
        keyDefinition: mappingData.keyDefinition || '',
        value: mappingData.value || '',
        language: mappingData.language || 'zh',
        displayValue: mappingData.displayValue || '',
        relationshipId: metadataId.toString(),
        englishDisplay: mappingData.englishDisplay || '',
        mandarinDisplay: mappingData.mandarinDisplay || '',
        // platform: mappingData.platform || 'web',
        // regexFormula: mappingData.regexFormula || '',
        // source: mappingData.source || '',
        // category: mappingData.category || '',
        // formId: mappingData.formId || '',
        // remark: mappingData.remark || '',
      };

      const result = await updateMappingMutation.mutateAsync({
        contentType: 'metadata',
        id: mappingId,
        request: updateRequest,
      });

      // The mutation will automatically invalidate the queries and refresh the list
      toast({
        variant: 'success',
        title: 'Success',
        description: 'Mapping item updated successfully',
      });

      return result.data;
    } catch (error) {
      console.error('Failed to update mapping:', error);
      toast({
        variant: 'error',
        title: 'Error',
        description: 'Failed to update mapping item',
      });
      throw error;
    }
  };

  const handleDeleteMapping = async (mappingId: string) => {
    console.log('handleDeleteMapping', mappingId);

    if (!mappingId) {
      console.error('Cannot delete mapping: mappingId is undefined or empty');
      toast({
        variant: 'error',
        title: 'Error',
        description: 'Cannot delete mapping: No ID provided',
      });
      return;
    }

    try {
      await deleteMappingMutation.mutateAsync({
        contentType: 'metadata',
        id: mappingId,
      });

      // The mutation will automatically invalidate the queries and refresh the list
      toast({
        variant: 'success',
        title: 'Success',
        description: 'Mapping item deleted successfully',
      });
    } catch (error) {
      console.error('Failed to delete mapping:', error);
      toast({
        variant: 'error',
        title: 'Error',
        description: 'Failed to delete mapping item',
      });
      throw error;
    }
  };

  // Enhanced validate and save row that calls the API
  const handleValidateAndSaveRow = async (
    mappingId: string,
    formValues: Partial<ContentMappingItem>
  ): Promise<boolean> => {
    // First validate locally using the store
    const isValidLocally = validateAndSaveRow(mappingId, formValues);

    if (!isValidLocally) {
      return false;
    }

    try {
      const mapping = formData?.mappings.find((m) => m.id === mappingId);
      if (!mapping) {
        return false;
      }

      // Prepare the data for API call
      const apiData = {
        ...mapping,
        ...formValues,
      };

      if (mapping.isNew) {
        // Create new mapping
        await handleCreateMapping(apiData);
      } else {
        // Update existing mapping
        await handleUpdateMapping(mappingId, apiData);
      }

      return true;
    } catch (error) {
      console.error('Failed to save mapping:', error);
      return false;
    }
  };

  // Enhanced remove mapping row that calls the API
  const handleRemoveMappingRow = async (mappingId: string) => {
    console.log('handleRemoveMappingRow', mappingId);

    if (!mappingId) {
      console.error(
        'Cannot remove mapping row: mappingId is undefined or empty'
      );
      toast({
        variant: 'error',
        title: 'Error',
        description: 'Cannot remove mapping: No ID provided',
      });
      return;
    }

    const mapping = formData?.mappings.find((m) => m.id === mappingId);
    if (!mapping) {
      console.error('Mapping not found for ID:', mappingId);
      toast({
        variant: 'error',
        title: 'Error',
        description: 'Mapping not found',
      });
      return;
    }

    if (mapping.isNew) {
      // If it's a new row, just remove it locally
      removeMappingRow(mappingId);
    } else {
      // If it's an existing row, delete it from the API
      try {
        await handleDeleteMapping(mappingId);
        // The query invalidation will handle removing it from the UI
      } catch (error) {
        console.error('Failed to delete mapping:', error);
      }
    }
  };

  // Create table actions object
  const tableActions: ContentMappingTableActions = {
    // onAddMappingRow: addMappingRow,
    onUpdateMappingField: updateMappingField,
    // onRemoveMappingRow: handleRemoveMappingRow,
    onToggleMappingEdit: toggleMappingEdit,
    onRevertMappingRow: revertMappingRow,
    onValidateAndSaveRow: handleValidateAndSaveRow, // Use the enhanced handler
    getRowValidationErrors: getRowValidationErrors,
  };

  // Create a wrapper for the translation function to match the expected signature
  const translateFunction = (key: string, fallback?: string) => {
    return t(key, fallback || key);
  };

  // Create columns using the new helper with sorting
  const { columns } = createContentMappingColumns(
    fieldConfig,
    tableActions,
    isEditMode,
    isEditableByPermissions,
    formValuesRef,
    {
      orderBy,
      order,
      onSort: handleTableSortChange,
    },
    hasUnsavedNewRows(),
    hasRowsBeingEdited(),
    translateFunction // Pass the wrapped translation function
  );

  // Determine loading state - need both definition and mappings to be loaded
  const isLoading = isDefinitionLoading || isMappingsLoading;

  // Only show full-page loader on initial load, not on search refetches
  const isInitialLoading = isLoading && !formData;

  // Display loader only for initial load, not for search/filter operations
  if (isInitialLoading) {
    return (
      <div className="flex justify-center items-center py-10 flex-1">
        <Loader size={64} />
      </div>
    );
  }

  // Display error if there's a query error and no form data
  const queryError = definitionError || mappingsError;
  if (queryError && !formData) {
    return (
      <div className="flex justify-center items-center py-10 flex-1">
        <div className="text-center">
          <div className="text-red-600 text-lg font-medium mb-2">
            {t('qmadmin.error.title', 'Error')}
          </div>
          <div className="text-gray-600 mb-4">{queryError.message}</div>
          <button
            onClick={() => {
              refetchDefinition();
              refetchMappings();
            }}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            {t('qmadmin.error.retry', 'Retry')}
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col flex-1 overflow-y-auto">
      <EditableBreadcrumb
        items={breadcrumbItems}
        selectedItemName={name || ''}
        selectedItemStatus={status}
        selectedItemId={metadataId}
        onTitleChange={handleTitleChange}
        versionName={version}
        onVersionChange={handleVersionChange}
        isEditMode={isEditMode}
      />

      <div className="flex justify-between items-center p-6 pt-2">
        <div className="flex items-center gap-4">
          <AdvancedSearchBar
            contentType="metadata"
            searchValue={searchValue}
            searchField={searchField}
            onSearchChange={setSearchValue}
            onSearchFieldChange={setSearchField}
          />
        </div>
        <ActionButtons
          isEditable={isEditableByPermissions}
          isEditMode={isEditMode}
          toggleEditMode={handleToggleEditMode}
          canEdit={isEditableByPermissions}
          canApprove={canApprove(status as Status, createdBy)}
          hasComments={comments.length > 0}
          isSubmitting={isSubmitting || isDuplicating}
          status={status}
          onSaveChanges={handleSaveChanges}
          onRequestApproval={handleSubmitForApproval}
          isDirty={isDirty}
          hasIncompleteRows={hasIncompleteRows}
          onDuplicate={handleDuplicate}
          // onBulkImport={handleBulkImport}
        />
      </div>

      <CommentSection
        comments={comments}
        onAddComment={handleAddComment}
        isApprover={canApprove(status as Status, createdBy)}
        isReturned={status === 'Returned'}
      />

      <ContentMappingTable
        data={mappingsData}
        columns={columns}
        isLoading={isMappingsLoading}
        currentPage={currentPage}
        perPage={perPage}
        onPageChange={handlePageChange}
        onPerPageChange={handlePerPageChange}
        totalItems={totalItems}
        emptyMessage="No metadata mappings found"
      />

      {/* Remove Bulk Import Modal for now */}
      {/* <BulkImportModal
        isOpen={isBulkImportModalOpen}
        onClose={closeBulkImportModal}
        multiCreateMutation={multiCreateMappingMutation}
      /> */}

      <DuplicateModal
        isOpen={isDuplicateModalOpen}
        onClose={handleDuplicateCancel}
        onConfirm={handleDuplicateConfirm}
        currentVersion={version || ''}
        itemName={name || ''}
        isSubmitting={isDuplicating}
      />
    </div>
  );
};

export default MetadataDetails;
