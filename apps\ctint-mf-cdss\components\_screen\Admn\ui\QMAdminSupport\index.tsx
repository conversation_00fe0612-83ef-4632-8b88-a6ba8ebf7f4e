import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, toast } from '@cdss-modules/design-system';
import Field from '@cdss-modules/design-system/components/_ui/Field';
import Input from '@cdss-modules/design-system/components/_ui/Input';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import {
  QueryClient,
  QueryClientProvider,
  useMutation,
  useQuery,
} from '@tanstack/react-query';
import {
  Search,
  RotateCcw,
  ScanText,
  Volume2,
  X,
  Calendar,
  Clock,
  AlertCircle,
} from 'lucide-react';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';
import { useRouteHandler } from '@cdss-modules/design-system';
import {
  Popup,
  PopupContent,
  PopupTrigger,
} from '@cdss-modules/design-system/components/_ui/Popup';
import { formatDateTimeToUTC } from '@cdss-modules/design-system/lib/utils';
import {
  fireGetRecordingSummary,
  fireRerunRecordings,
} from '../../../../../lib/api';

// Types
interface RecordingSummary {
  totalCount: number;
  successCount: number;
  failCount: number;
}

interface RecordingSummaryResponse {
  data: RecordingSummary;
  error: string;
  isSuccess: boolean;
}

interface RerunResponse {
  data: null;
  error: string;
  isSuccess: boolean;
}

interface SearchParams {
  startTime: string;
  endTime: string;
}

// Component Props Types
interface SearchSectionProps {
  onSearch: (params: SearchParams) => void;
  isSearching: boolean;
  startTime: string;
  endTime: string;
  onStartTimeChange: (value: string) => void;
  onEndTimeChange: (value: string) => void;
}

interface StatisticsSectionProps {
  recordingsData: RecordingSummary | undefined;
  onRerun: (jobType: 'all' | 'failed') => void;
  isRerunning: boolean;
}

interface ConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  jobType: 'all' | 'failed' | null;
  confirmationNumber: string;
  userInputNumber: string;
  onUserInputChange: (value: string) => void;
}

// Search Recordings Section Component
const SearchRecordingsSection: React.FC<SearchSectionProps> = ({
  onSearch,
  isSearching,
  startTime,
  endTime,
  onStartTimeChange,
  onEndTimeChange,
}) => {
  const { t } = useTranslation();
  const [customInput, setCustomInput] = useState('');

  const calculateDateRange = (type: '24hour' | '7days' | '30days') => {
    const now = new Date();
    const startDate = new Date();

    switch (type) {
      case '24hour':
        startDate.setHours(now.getHours() - 24);
        break;
      case '7days':
        startDate.setDate(now.getDate() - 7);
        break;
      case '30days':
        startDate.setDate(now.getDate() - 30);
        break;
    }

    // Format for datetime-local input (YYYY-MM-DDTHH:MM)
    const formatForInput = (date: Date) => {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      return `${year}-${month}-${day}T${hours}:${minutes}`;
    };

    const newStartTime = formatForInput(startDate);
    const newEndTime = formatForInput(now);

    onStartTimeChange(newStartTime);
    onEndTimeChange(newEndTime);
  };

  const validateAndApplyCustomInput = () => {
    if (!customInput.trim()) {
      toast({
        variant: 'error',
        title: 'Validation Error',
        description: 'Please enter a number of days.',
      });
      return;
    }

    const days = parseInt(customInput.trim());

    if (isNaN(days) || days <= 0) {
      toast({
        variant: 'error',
        title: 'Validation Error',
        description: 'Please enter a valid positive number.',
      });
      return;
    }

    if (days > 180) {
      toast({
        variant: 'error',
        title: 'Validation Error',
        description: 'Search range cannot exceed 180 days.',
      });
      return;
    }

    const now = new Date();
    const startDate = new Date();
    startDate.setDate(now.getDate() - days);

    const formatForInput = (date: Date) => {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      return `${year}-${month}-${day}T${hours}:${minutes}`;
    };

    const newStartTime = formatForInput(startDate);
    const newEndTime = formatForInput(now);

    onStartTimeChange(newStartTime);
    onEndTimeChange(newEndTime);
    setCustomInput('');
  };

  const handleCustomInputKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      validateAndApplyCustomInput();
    }
  };

  const validateDateRange = (start: string, end: string) => {
    if (!start || !end) return null;

    const startDate = new Date(start);
    const endDate = new Date(end);
    const diffTime = Math.abs(endDate.getTime() - startDate.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    return diffDays;
  };

  const handleSearch = () => {
    // Validation
    if (!startTime || !endTime) {
      toast({
        variant: 'error',
        title: 'Validation Error',
        description: 'Please select both start time and end time.',
      });
      return;
    }

    if (new Date(startTime) >= new Date(endTime)) {
      toast({
        variant: 'error',
        title: 'Validation Error',
        description: 'Start Time must be before End Time.',
      });
      return;
    }

    // Check 180-day limit
    const daysDiff = validateDateRange(startTime, endTime);
    if (daysDiff && daysDiff > 180) {
      toast({
        variant: 'error',
        title: 'Validation Error',
        description: 'Search range cannot exceed 180 days.',
      });
      return;
    }

    onSearch({ startTime, endTime });
  };

  return (
    <div className="bg-white">
      <h2 className="text-t5 font-bold mb-4 flex items-center gap-2">
        <Search size={20} />
        {t('ctint-mf-cdss.qmAdmin.support.searchRecordings')}
      </h2>

      {/* 180 Days Limitation Notice */}
      <div className="bg-amber-50 border border-amber-200 rounded-lg p-4 mb-6">
        <div className="flex items-start gap-3">
          <AlertCircle
            size={20}
            className="text-amber-600 mt-0.5 flex-shrink-0"
          />
          <div>
            <h3 className="text-sm font-medium text-amber-800 mb-1">
              {t('ctint-mf-cdss.qmAdmin.support.searchLimitation')}
            </h3>
            <p className="text-sm text-amber-700">
              {t('ctint-mf-cdss.qmAdmin.support.searchWithin180Days')}
            </p>
          </div>
        </div>
      </div>

      {/* Quick Selection Buttons */}

      <div className="flex flex-row gap-3">
        <div>
          <h3 className="text-sm font-medium text-gray-700 mb-3">
            {t('ctint-mf-cdss.qmAdmin.support.quickSelect')}
          </h3>
          <div className="flex flex-wrap gap-3">
            <Button
              variant="secondary"
              size="s"
              onClick={() => calculateDateRange('24hour')}
              beforeIcon={
                <Clock
                  size={14}
                  className="mr-1"
                />
              }
            >
              {t('ctint-mf-cdss.qmAdmin.support.24hours')}
            </Button>
            <Button
              variant="secondary"
              size="s"
              onClick={() => calculateDateRange('7days')}
              beforeIcon={
                <Calendar
                  size={14}
                  className="mr-1"
                />
              }
            >
              {t('ctint-mf-cdss.qmAdmin.support.7days')}
            </Button>
            <Button
              variant="secondary"
              size="s"
              onClick={() => calculateDateRange('30days')}
              beforeIcon={
                <Calendar
                  size={14}
                  className="mr-1"
                />
              }
            >
              {t('ctint-mf-cdss.qmAdmin.support.30days')}
            </Button>
          </div>
        </div>

        {/* Custom Input */}
        <div className="mb-6">
          <h3 className="text-sm font-medium text-gray-700 mb-3">
            {t('ctint-mf-cdss.qmAdmin.support.customDays')}
          </h3>
          <div className="flex gap-3 items-start">
            <Input
              size="s"
              type="number"
              value={customInput}
              onChange={(value) => setCustomInput(value.toString())}
              placeholder={t(
                'ctint-mf-cdss.qmAdmin.support.enterDaysPlaceholder'
              )}
              onKeyPress={handleCustomInputKeyPress}
              min="1"
              max="180"
            />

            <Button
              variant="secondary"
              size="s"
              onClick={validateAndApplyCustomInput}
              disabled={!customInput.trim()}
            >
              {t('ctint-mf-cdss.qmAdmin.support.apply')}
            </Button>
          </div>
          <p className="text-xs text-gray-500 mt-2">
            {t('ctint-mf-cdss.qmAdmin.support.enterDaysHint')}
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
        <Field
          title={t('ctint-mf-cdss.qmAdmin.support.startTime')}
          icon={<Icon name="error" />}
          className="w-full"
        >
          <Input
            size="m"
            type="datetime-local"
            value={startTime}
            onChange={(value) => onStartTimeChange(value.toString())}
            placeholder={t('ctint-mf-cdss.qmAdmin.support.selectStartTime')}
          />
        </Field>

        <Field
          title={t('ctint-mf-cdss.qmAdmin.support.endTime')}
          icon={<Icon name="error" />}
          className="w-full"
        >
          <Input
            size="m"
            type="datetime-local"
            value={endTime}
            onChange={(value) => onEndTimeChange(value.toString())}
            placeholder={t('ctint-mf-cdss.qmAdmin.support.selectEndTime')}
          />
        </Field>

        <div className="flex items-end">
          <Button
            onClick={handleSearch}
            disabled={isSearching}
            beforeIcon={
              isSearching ? <Loader size={16} /> : <Search size={16} />
            }
            size="m"
            className=""
          >
            {isSearching
              ? t('ctint-mf-cdss.qmAdmin.support.searching')
              : t('ctint-mf-cdss.qmAdmin.support.search')}
          </Button>
        </div>
      </div>

      {/* Date Range Validation Indicator */}
      {startTime && endTime && (
        <div className="mt-4">
          {(() => {
            const daysDiff = validateDateRange(startTime, endTime);
            if (!daysDiff) return null;

            const isValid = daysDiff <= 180;
            return (
              <div
                className={`flex items-center gap-2 text-sm ${
                  isValid ? 'text-green-600' : 'text-red-600'
                }`}
              >
                <AlertCircle size={16} />
                <span>
                  {isValid
                    ? t('ctint-mf-cdss.qmAdmin.support.validRange', {
                        days: daysDiff,
                      })
                    : t('ctint-mf-cdss.qmAdmin.support.invalidRange', {
                        days: daysDiff,
                      })}
                </span>
              </div>
            );
          })()}
        </div>
      )}
    </div>
  );
};

// Statistics Section Component
const StatisticsSection: React.FC<StatisticsSectionProps> = ({
  recordingsData,
  onRerun,
  isRerunning,
}) => {
  const { t } = useTranslation();

  if (!recordingsData) return null;

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
      {/* Total Recordings Card */}
      <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6">
        <div className="flex items-center gap-4 mb-4">
          {/* Total Recordings Icon */}
          <div className="w-16 h-16 bg-purple-100 rounded-2xl flex items-center justify-center">
            <div className="w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center">
              <ScanText
                size={16}
                className="text-white"
              />
            </div>
          </div>

          {/* Total Recordings Content */}
          <div className="flex-1">
            <h3 className="text-sm font-medium text-gray-600 mb-1">
              {t('ctint-mf-cdss.qmAdmin.support.totalRecordings')}
            </h3>
            <p className="text-3xl font-bold text-gray-900">
              {recordingsData.totalCount.toLocaleString()}
            </p>
          </div>
        </div>

        <Button
          onClick={() => onRerun('all')}
          disabled={isRerunning || recordingsData.totalCount === 0}
          beforeIcon={
            isRerunning ? <Loader size={14} /> : <RotateCcw size={14} />
          }
          size="s"
          variant="secondary"
        >
          {isRerunning
            ? t('ctint-mf-cdss.qmAdmin.support.rerunning')
            : t('ctint-mf-cdss.qmAdmin.support.rerunAllJobs')}
        </Button>
      </div>

      {/* Failed Recordings Card */}
      <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6">
        <div className="flex items-center gap-4 mb-4">
          {/* Failed Recordings Icon */}
          <div className="w-16 h-16 bg-red-100 rounded-2xl flex items-center justify-center">
            <div className="w-8 h-8 bg-red-500 rounded-lg flex items-center justify-center">
              <X
                size={16}
                className="text-white"
              />
            </div>
          </div>

          {/* Failed Recordings Content */}
          <div className="flex-1">
            <h3 className="text-sm font-medium text-gray-600 mb-1">
              {t('ctint-mf-cdss.qmAdmin.support.failedRecordings')}
            </h3>
            <p className="text-3xl font-bold text-gray-900">
              {recordingsData.failCount.toLocaleString()}
            </p>
          </div>
        </div>

        <Button
          onClick={() => onRerun('failed')}
          disabled={isRerunning || recordingsData.failCount === 0}
          beforeIcon={
            isRerunning ? <Loader size={14} /> : <RotateCcw size={14} />
          }
          size="s"
          variant="secondary"
        >
          {isRerunning
            ? t('ctint-mf-cdss.qmAdmin.support.rerunning')
            : t('ctint-mf-cdss.qmAdmin.support.rerunFailedJobs')}
        </Button>
      </div>
    </div>
  );
};

// Confirmation Modal Component
const ConfirmationModal: React.FC<ConfirmationModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  jobType,
  confirmationNumber,
  userInputNumber,
  onUserInputChange,
}) => {
  const { t } = useTranslation();

  const handleConfirm = () => {
    if (userInputNumber !== confirmationNumber) {
      toast({
        variant: 'error',
        title: 'Verification Failed',
        description: 'The entered number does not match. Please try again.',
      });
      return;
    }
    onConfirm();
  };

  return (
    <Popup
      open={isOpen}
      onOpenChange={onClose}
    >
      <PopupContent
        title={t('ctint-mf-cdss.qmAdmin.support.confirmModal.title')}
        className="max-w-md"
      >
        <div className="p-6">
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-2 text-red-600">
              ⚠️{' '}
              {t(
                'ctint-mf-cdss.qmAdmin.support.confirmModal.highPerformanceAction'
              )}
            </h3>
            <p className="text-gray-700 mb-4">
              {t(
                'ctint-mf-cdss.qmAdmin.support.confirmModal.descriptionPrefix'
              )}{' '}
              <span className="font-bold">
                {jobType === 'all'
                  ? t('ctint-mf-cdss.qmAdmin.support.confirmModal.allJobs')
                  : t('ctint-mf-cdss.qmAdmin.support.confirmModal.failedJobs')}
              </span>
              。
              {t(
                'ctint-mf-cdss.qmAdmin.support.confirmModal.descriptionSuffix'
              )}
            </p>
            <p className="text-sm text-gray-600 mb-4">
              {t(
                'ctint-mf-cdss.qmAdmin.support.confirmModal.confirmInstruction'
              )}
            </p>

            {/* Verification Number Display */}
            <div className="bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg p-4 mb-4">
              <div className="text-center">
                <span className="text-sm text-gray-600">
                  {t(
                    'ctint-mf-cdss.qmAdmin.support.confirmModal.verificationNumber'
                  )}
                </span>
                <div
                  className="text-2xl font-bold font-mono text-gray-800 mt-1 select-none"
                  style={{
                    userSelect: 'none',
                    WebkitUserSelect: 'none',
                    MozUserSelect: 'none',
                    msUserSelect: 'none',
                    WebkitTouchCallout: 'none',
                    WebkitTapHighlightColor: 'transparent',
                  }}
                  onContextMenu={(e: React.MouseEvent) => e.preventDefault()}
                  onDragStart={(e: React.DragEvent) => e.preventDefault()}
                  onCopy={(e: React.ClipboardEvent) => e.preventDefault()}
                  onCut={(e: React.ClipboardEvent) => e.preventDefault()}
                >
                  {confirmationNumber}
                </div>
              </div>
            </div>

            {/* Input Field */}
            <Field
              title={t(
                'ctint-mf-cdss.qmAdmin.support.confirmModal.enterVerificationNumber'
              )}
              className="w-full"
            >
              <Input
                size="m"
                type="text"
                value={userInputNumber}
                onChange={(value) => onUserInputChange(value.toString())}
                placeholder={t(
                  'ctint-mf-cdss.qmAdmin.support.confirmModal.enterNumberPlaceholder'
                )}
                className="font-mono text-center text-lg"
              />
            </Field>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 justify-end">
            <Button
              variant="secondary"
              size="m"
              onClick={onClose}
            >
              {t('ctint-mf-cdss.qmAdmin.support.confirmModal.cancel')}
            </Button>
            <Button
              variant="primary"
              size="m"
              onClick={handleConfirm}
              disabled={
                !userInputNumber || userInputNumber !== confirmationNumber
              }
            >
              {t('ctint-mf-cdss.qmAdmin.support.confirmModal.confirmRerun')}
            </Button>
          </div>
        </div>
      </PopupContent>
    </Popup>
  );
};

// Main Component
export default function QMAdminSupport() {
  const { basePath } = useRouteHandler();
  const [confirmationOpen, setConfirmationOpen] = useState(false);
  const [confirmationNumber, setConfirmationNumber] = useState('');
  const [userInputNumber, setUserInputNumber] = useState('');
  const [pendingJobType, setPendingJobType] = useState<'all' | 'failed' | null>(
    null
  );
  const [searchParams, setSearchParams] = useState<SearchParams | null>(null);
  const [startTime, setStartTime] = useState('');
  const [endTime, setEndTime] = useState('');

  // Search recordings query
  const {
    data: recordingsData,
    isFetching: isSearching,
    error: searchError,
  } = useQuery({
    queryKey: ['recordings', searchParams?.startTime, searchParams?.endTime],
    queryFn: async () => {
      const response = await fireGetRecordingSummary(
        formatDateTimeToUTC(searchParams!.startTime),
        formatDateTimeToUTC(searchParams!.endTime),
        basePath
      );
      return response.data;
    },
    enabled: !!searchParams,
  });

  // Handle search error with toast
  useEffect(() => {
    if (searchError) {
      // Extract error message from API response if available
      let errorMessage = 'Failed to search recordings. Please try again.';

      if (searchError && typeof searchError === 'object') {
        // Handle case where error is the API response object
        if ('error' in searchError && typeof searchError.error === 'string') {
          errorMessage = searchError.error;
        }
        // Handle case where error is wrapped in a response object
        else if (
          'response' in searchError &&
          searchError.response &&
          typeof searchError.response === 'object' &&
          'data' in searchError.response &&
          searchError.response.data &&
          typeof searchError.response.data === 'object' &&
          'error' in searchError.response.data &&
          typeof searchError.response.data.error === 'string'
        ) {
          errorMessage = searchError.response.data.error;
        }
        // Handle standard Error object
        else if (
          'message' in searchError &&
          typeof searchError.message === 'string'
        ) {
          errorMessage = searchError.message;
        }
      }

      toast({
        variant: 'error',
        title: 'Search Error',
        description: errorMessage,
      });
    }
  }, [searchError]);

  // Re-run jobs mutation
  const rerunMutation = useMutation({
    mutationFn: async (params: {
      jobType: 'all' | 'failed';
      startTime: string;
      endTime: string;
    }) => {
      const state = params.jobType === 'failed' ? 'fail' : 'all';
      const response = await fireRerunRecordings(
        state,
        formatDateTimeToUTC(params.startTime),
        formatDateTimeToUTC(params.endTime),
        basePath
      );
      return response;
    },
    onSuccess: (data: RerunResponse) => {
      toast({
        variant: data.isSuccess ? 'success' : 'error',
        title: data.isSuccess ? 'Success' : 'Error',
        description: data.isSuccess
          ? 'Re-run job has been initiated successfully.'
          : data.error || 'Failed to initiate re-run job.',
      });
    },
    onError: (error) => {
      toast({
        variant: 'error',
        title: 'Error',
        description: 'An unexpected error occurred. Please try again.',
      });
    },
  });

  const generateRandomNumber = () => {
    return Math.floor(100000 + Math.random() * 900000).toString(); // 6-digit number
  };

  const handleSearch = (params: SearchParams) => {
    setSearchParams(params);
  };

  const handleRerun = (jobType: 'all' | 'failed') => {
    setPendingJobType(jobType);
    setConfirmationNumber(generateRandomNumber());
    setUserInputNumber('');
    setConfirmationOpen(true);
  };

  const handleConfirmRerun = () => {
    if (pendingJobType && searchParams) {
      rerunMutation.mutate({
        jobType: pendingJobType,
        startTime: searchParams.startTime,
        endTime: searchParams.endTime,
      });
      setConfirmationOpen(false);
      setPendingJobType(null);
      setUserInputNumber('');
      setConfirmationNumber('');
    }
  };

  const handleCancelConfirmation = () => {
    setConfirmationOpen(false);
    setPendingJobType(null);
    setUserInputNumber('');
    setConfirmationNumber('');
  };

  return (
    <Panel containerClassName="h-full overflow-hidden">
      <div className="p-6 h-full flex flex-col gap-6 overflow-y-auto">
        {/* Search Recordings Section */}
        <SearchRecordingsSection
          onSearch={handleSearch}
          isSearching={isSearching}
          startTime={startTime}
          endTime={endTime}
          onStartTimeChange={setStartTime}
          onEndTimeChange={setEndTime}
        />

        {/* Summary Statistics */}
        <StatisticsSection
          recordingsData={recordingsData}
          onRerun={handleRerun}
          isRerunning={rerunMutation.isPending}
        />
      </div>

      {/* Confirmation Modal */}
      <ConfirmationModal
        isOpen={confirmationOpen}
        onClose={handleCancelConfirmation}
        onConfirm={handleConfirmRerun}
        jobType={pendingJobType}
        confirmationNumber={confirmationNumber}
        userInputNumber={userInputNumber}
        onUserInputChange={setUserInputNumber}
      />
    </Panel>
  );
}
