/* eslint-disable @next/next/no-sync-scripts */
import { AppProps } from 'next/app';
import Head from 'next/head';

import './styles.css';
import Script from 'next/script';
import { initI18Next, addLocalesResources } from '@cdss-modules/design-system/i18n/client';
import { prepareLocales } from '../i18n/locales';

initI18Next((langauges: string[] = []) => {
  langauges.forEach((lang) => {
    prepareLocales((resultLocale: any) => addLocalesResources(resultLocale[lang], lang));
  });
});

function CustomApp({ Component, pageProps }: AppProps) {
  return (
    <>
      <Head>
        <title>CDSS 3.0 MF Announcer</title>
      </Head>
      <Script id="GLOBAL_ENV_VARS">
        {`const GLOBAL_ENV_VARS = ${JSON.stringify(pageProps.publicEnvVars)};`}
      </Script>
      <main className="app">
        <Component {...pageProps} />
      </main>
    </>
  );
}

export default CustomApp;
