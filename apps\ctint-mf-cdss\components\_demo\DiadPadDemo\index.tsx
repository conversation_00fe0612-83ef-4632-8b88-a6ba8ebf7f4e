'use client';

import CallControlButton from '@cdss-modules/design-system/components/_ui/CallControlButton';
import DialPad from '@cdss-modules/design-system/components/_ui/DialPad';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import { useState } from 'react';

const DialPadDemo = () => {
  const [padActive, setPadActive] = useState(false);
  const handlePadActive = () => {
    setPadActive(!padActive);
  };

  return (
    <div className="w-1/3">
      {padActive && <DialPad value={[]} />}
      <CallControlButton
        className=""
        icon={
          <Icon
            name="pad"
            size={64}
          />
        }
        label={'Pad'}
        active={padActive}
        handleOnChange={handlePadActive}
      />
    </div>
  );
};

export default DialPadDemo;
