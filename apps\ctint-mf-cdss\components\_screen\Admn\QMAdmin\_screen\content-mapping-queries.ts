import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from '@cdss-modules/design-system';
import { useRoute<PERSON>andler } from '@cdss-modules/design-system';

// Import fire functions
import {
  fireGetContentMappingList,
  fireCreateContentMappingItem,
  fireUpdateContentMappingItem,
  fireDeleteContentMappingItem,
  fireMultiCreateContentMappingItems,
} from '@cdss/lib/api';

// Import types from definition queries
import type { RelationshipType } from './definition-queries';

// ======================================
// CONTENT MAPPING TYPES AND INTERFACES
// ======================================

// Content mapping type for specific content APIs
export type ContentMappingType =
  | 'metadata'
  | 'dictionary'
  | 'sttKeyword'
  | 'sensitiveKeyword';

// Searchable field configurations for each content type
export interface SearchableField {
  value: string;
  label: string;
}

export const CONTENT_MAPPING_SEARCHABLE_FIELDS: Record<
  ContentMappingType,
  SearchableField[]
> = {
  dictionary: [
    { value: 'keyDefinition', label: 'Key' },
    { value: 'value', label: 'Value' },
    { value: 'regexFormula', label: 'Regex Formula' },
    { value: 'displayValue', label: 'Cantonese Display' },
    { value: 'englishDisplay', label: 'English Display' },
    { value: 'mandarinDisplay', label: 'Putonghua Display' },
  ],
  metadata: [
    { value: 'keyDefinition', label: 'Key' },
    { value: 'value', label: 'Value' },
    { value: 'displayValue', label: 'Cantonese Display' },
    { value: 'englishDisplay', label: 'English Display' },
    { value: 'mandarinDisplay', label: 'Putonghua Display' },
  ],
  sttKeyword: [
    { value: 'keyDefinition', label: 'Key' },
    { value: 'value', label: 'Value' },
    { value: 'displayValue', label: 'Cantonese Display' },
    { value: 'englishDisplay', label: 'English Display' },
    { value: 'mandarinDisplay', label: 'Putonghua Display' },
  ],
  sensitiveKeyword: [
    { value: 'keyDefinition', label: 'Key' },
    { value: 'value', label: 'Value' },
    { value: 'displayValue', label: 'Cantonese Display' },
    { value: 'englishDisplay', label: 'English Display' },
    { value: 'mandarinDisplay', label: 'Putonghua Display' },
  ],
};

// Helper function to get searchable fields for a content type
export const getSearchableFields = (
  contentType: ContentMappingType
): SearchableField[] => {
  return CONTENT_MAPPING_SEARCHABLE_FIELDS[contentType] || [];
};

// Mapping item interface based on the API response structure
export interface UnifiedMappingItem {
  entityId: string;
  standardScriptId: string;
  source: string;
  category: string;
  type: string;
  keyDefinition: string | null;
  value: string;
  createTime: string;
  updateTime: string | null;
  deleteTime: string | null;
  createBy: string;
  updateBy: string;
  platform: string;
  tenant: string;
  displayValue: string;
  englishDisplay: string;
  mandarinDisplay: string;
  formId: string;
  language: string;
  remark: string;
  relationshipId: string;
  regexFormula: string;
  useModel: boolean | null;
  createUser: string;
  updateUser: string;
}

// Unified request interface for content mapping listings
export interface UnifiedMappingRequest {
  page: number;
  pageSize: number;
  order: 'ASC' | 'DESC';
  orderBy: string;
  relationshipType: RelationshipType;
  relationshipId: string;
  status?: string;
  // Generic search field and value - can be any column
  searchField?: string;
  searchValue?: string;
}

// Unified mapping response interface - now uses specific UnifiedMappingItem type
export interface UnifiedMappingResponse {
  data: {
    items: UnifiedMappingItem[];
    totalCount: number;
    totalPage: number;
  };
  error: string;
  isSuccess: boolean;
}

// Unified create request interface for mapping items
export interface UnifiedMappingCreateRequest {
  type?: string;
  keyDefinition: string;
  value: string;
  language: string;
  displayValue: string;
  relationshipId: string;
  regexFormula?: string;
  useModel?: boolean;
  source?: string;
  category?: string;
  formId?: string;
  remark?: string;
  englishDisplay?: string;
  mandarinDisplay?: string;
  platform?: string;
}

// Unified update request interface for mapping items
export interface UnifiedMappingUpdateRequest {
  type?: string;
  keyDefinition: string;
  value: string;
  language: string;
  displayValue: string;
  relationshipId: string;
  regexFormula?: string;
  useModel?: boolean;
  source?: string;
  category?: string;
  formId?: string;
  remark?: string;
  englishDisplay?: string;
  mandarinDisplay?: string;
  platform?: string;
}

// Unified create response interface for mapping items
export interface UnifiedMappingCreateResponse {
  data: UnifiedMappingItem;
  error: string;
  isSuccess: boolean;
}

// Unified multi-create request interface for mapping items
export interface UnifiedMappingMultiCreateRequest {
  items: Array<{
    keyDefinition: string;
    value: string;
    displayValue: string;
    englishDisplay: string;
    mandarinDisplay: string;
    relationshipId: string;
    language?: string;
    regexFormula?: string;
    useModel?: boolean;
    source?: string;
    category?: string;
    formId?: string;
    remark?: string;
    platform?: string;
    type?: string;
  }>;
}

// Unified multi-create response interface for mapping items
export interface UnifiedMappingMultiCreateResponse {
  data: null;
  error: string;
  isSuccess: boolean;
}

// Unified update response interface for mapping items
export interface UnifiedMappingUpdateResponse {
  data: UnifiedMappingItem;
  error: string;
  isSuccess: boolean;
}

// Unified delete response interface for mapping items
export interface UnifiedMappingDeleteResponse {
  data: null;
  error: string;
  isSuccess: boolean;
}

// ======================================
// CONTENT MAPPING SPECIFIC API FUNCTIONS
// ======================================

// Fetch content mapping list using dedicated endpoints
export const fetchContentMappingList = async (
  contentType: ContentMappingType,
  request: UnifiedMappingRequest,
  basePath = ''
): Promise<UnifiedMappingResponse> => {
  try {
    console.log('Content mapping request:', contentType, request);

    const requestBody = {
      page: request.page,
      pageSize: request.pageSize,
      order: request.order,
      orderBy: request.orderBy,
      relationshipId: request.relationshipId,
      ...(request.status && { status: request.status }),
      // Generic search field support - dynamically add the search field and value
      ...(request.searchField &&
        request.searchValue && { [request.searchField]: request.searchValue }),
    };

    console.log('Content Mapping API request:', {
      contentType,
      body: requestBody,
    });

    const response = await fireGetContentMappingList(
      contentType,
      requestBody,
      basePath
    );
    const result: UnifiedMappingResponse = response.data;

    console.log('Content Mapping API response:', result);

    if (!result.isSuccess) {
      throw new Error(result.error || 'API request failed');
    }

    console.log('Processed content mapping API result:', {
      contentType,
      dataLength: result.data.items?.length || 0,
      totalItems: result.data.totalCount || 0,
      totalPages: result.data.totalPage || 0,
      requestedPage: request.page,
      requestedPageSize: request.pageSize,
    });

    return result;
  } catch (error) {
    console.error('Error fetching content mapping list:', error);
    throw error;
  }
};

// Create content mapping item using dedicated endpoints
export const createContentMappingItem = async (
  contentType: ContentMappingType,
  request: UnifiedMappingCreateRequest,
  basePath = ''
): Promise<UnifiedMappingCreateResponse> => {
  try {
    const requestBody = {
      type:
        request.type ||
        (contentType === 'dictionary' ? 'data_dict' : contentType),
      keyDefinition: request.keyDefinition,
      value: request.value,
      language: request.language,
      displayValue: request.displayValue,
      relationshipId: request.relationshipId,
      ...(request.regexFormula && { regexFormula: request.regexFormula }),
      ...(request.useModel !== undefined && { useModel: request.useModel }),
      ...(request.source && { source: request.source }),
      ...(request.category && { category: request.category }),
      ...(request.formId && { formId: request.formId }),
      ...(request.remark && { remark: request.remark }),
      ...(request.englishDisplay && { englishDisplay: request.englishDisplay }),
      ...(request.mandarinDisplay && {
        mandarinDisplay: request.mandarinDisplay,
      }),
      ...(request.platform && { platform: request.platform }),
    };

    console.log('Content Mapping Create API request:', {
      contentType,
      body: requestBody,
    });

    const response = await fireCreateContentMappingItem(
      contentType,
      requestBody,
      basePath
    );
    const result: UnifiedMappingCreateResponse = response.data;

    console.log('Content Mapping Create API response:', result);

    if (!result.isSuccess) {
      throw new Error(result.error || 'API request failed');
    }

    console.log('Processed content mapping create API result:', {
      contentType,
      createdId: result.data?.entityId || 'unknown',
    });

    return result;
  } catch (error) {
    console.error('Error creating content mapping item:', error);
    throw error;
  }
};

// Update content mapping item using dedicated endpoints
export const updateContentMappingItem = async (
  contentType: ContentMappingType,
  id: string,
  request: UnifiedMappingUpdateRequest,
  basePath = ''
): Promise<UnifiedMappingUpdateResponse> => {
  try {
    console.log('Content Mapping Update API request:', {
      contentType,
      id,
      request,
    });

    const requestBody = {
      keyDefinition: request.keyDefinition,
      value: request.value,
      language: request.language,
      displayValue: request.displayValue,
      relationshipId: request.relationshipId,
      ...(request.regexFormula && { regexFormula: request.regexFormula }),
      ...(request.useModel !== undefined && { useModel: request.useModel }),
      ...(request.source && { source: request.source }),
      ...(request.category && { category: request.category }),
      ...(request.formId && { formId: request.formId }),
      ...(request.remark && { remark: request.remark }),
      ...(request.englishDisplay && { englishDisplay: request.englishDisplay }),
      ...(request.mandarinDisplay && {
        mandarinDisplay: request.mandarinDisplay,
      }),
      ...(request.platform && { platform: request.platform }),
      ...(request.type && { type: request.type }),
    };

    console.log('Content Mapping Update API request:', {
      contentType,
      id,
      body: requestBody,
    });

    const response = await fireUpdateContentMappingItem(
      contentType,
      id,
      requestBody,
      basePath
    );
    const result: UnifiedMappingUpdateResponse = response.data;

    console.log('Content Mapping Update API response:', result);

    return result;
  } catch (error) {
    console.error('Error updating content mapping item:', error);
    throw error;
  }
};

// Delete content mapping item using dedicated endpoints
export const deleteContentMappingItem = async (
  contentType: ContentMappingType,
  id: string,
  basePath = ''
): Promise<UnifiedMappingDeleteResponse> => {
  try {
    console.log('Content Mapping Delete API request:', {
      contentType,
      id,
    });

    const response = await fireDeleteContentMappingItem(
      contentType,
      id,
      basePath
    );
    const result: UnifiedMappingDeleteResponse = response.data;

    console.log('Content Mapping Delete API response:', result);

    if (!result.isSuccess) {
      throw new Error(result.error || 'API request failed');
    }

    console.log('Processed content mapping delete API result:', {
      contentType,
      deletedId: id,
    });

    return result;
  } catch (error) {
    console.error('Error deleting content mapping item:', error);
    throw error;
  }
};

// Multi-create content mapping items using dedicated endpoints
export const multiCreateContentMappingItems = async (
  contentType: ContentMappingType,
  request: UnifiedMappingMultiCreateRequest,
  basePath = ''
): Promise<UnifiedMappingMultiCreateResponse> => {
  try {
    const requestBody = {
      items: request.items.map((item) => ({
        keyDefinition: item.keyDefinition,
        value: item.value,
        displayValue: item.displayValue,
        englishDisplay: item.englishDisplay,
        mandarinDisplay: item.mandarinDisplay,
        relationshipId: item.relationshipId,
        language: item.language || 'zh',
        ...(item.regexFormula && { regexFormula: item.regexFormula }),
        ...(item.useModel !== undefined && { useModel: item.useModel }),
        ...(item.source && { source: item.source }),
        ...(item.category && { category: item.category }),
        ...(item.formId && { formId: item.formId }),
        ...(item.remark && { remark: item.remark }),
        ...(item.platform && { platform: item.platform }),
        ...(item.type && { type: item.type }),
      })),
    };

    console.log('Content Mapping Multi-Create API request:', {
      contentType,
      body: requestBody,
    });

    const response = await fireMultiCreateContentMappingItems(
      contentType,
      requestBody,
      basePath
    );
    const result: UnifiedMappingMultiCreateResponse = response.data;

    console.log('Content Mapping Multi-Create API response:', result);

    if (!result.isSuccess) {
      throw new Error(result.error || 'API request failed');
    }

    console.log('Processed content mapping multi-create API result:', {
      contentType,
      itemsCount: request.items.length,
    });

    return result;
  } catch (error) {
    console.error('Error multi-creating content mapping items:', error);
    throw error;
  }
};

// ======================================
// CONTENT MAPPING SPECIFIC QUERY HOOKS
// ======================================

// Content mapping list query using dedicated endpoints
export const useContentMappingListQuery = (
  contentType: ContentMappingType,
  parentId: string,
  search = '',
  statusFilter = '',
  page = 1,
  pageSize = 10,
  orderBy = 'createTime',
  order: 'ASC' | 'DESC' = 'DESC',
  searchField = 'keyDefinition' // Default search field
) => {
  const { basePath } = useRouteHandler();

  return useQuery({
    queryKey: [
      'contentMappingList',
      contentType,
      parentId,
      search,
      statusFilter,
      page,
      pageSize,
      orderBy,
      order,
      searchField, // Include searchField in queryKey
    ],
    queryFn: async () => {
      const request: UnifiedMappingRequest = {
        page,
        pageSize,
        order,
        orderBy,
        relationshipType: contentType as RelationshipType, // Type assertion for compatibility
        relationshipId: parentId,
        ...(search && searchField && { searchField, searchValue: search }),
        ...(statusFilter && { status: statusFilter }),
      };

      return await fetchContentMappingList(contentType, request, basePath);
    },
    retry: 3,
    retryDelay: 1000,
    enabled: !!parentId && !!contentType, // Only run if both parentId and contentType are provided
  });
};

// ======================================
// CONTENT MAPPING SPECIFIC MUTATION HOOKS
// ======================================

// Content mapping create mutation using dedicated endpoints
export const useContentMappingCreateMutation = () => {
  const queryClient = useQueryClient();
  const { basePath } = useRouteHandler();

  return useMutation({
    mutationFn: ({
      contentType,
      request,
    }: {
      contentType: ContentMappingType;
      request: UnifiedMappingCreateRequest;
    }) => createContentMappingItem(contentType, request, basePath),
    onSuccess: (data, variables) => {
      // Invalidate and refetch the content mapping list for this content type
      queryClient.invalidateQueries({
        queryKey: ['contentMappingList', variables.contentType],
      });

      // Show success toast
      toast({
        variant: 'success',
        title: 'Success',
        description: `${variables.contentType} mapping item created successfully`,
      });
    },
    onError: (error: Error, variables) => {
      console.error(
        `Failed to create ${variables.contentType} mapping item:`,
        error
      );

      // Show error toast notification
      toast({
        variant: 'error',
        title: 'Error',
        description: `Failed to create ${variables.contentType} mapping item: ${error.message}`,
      });
    },
  });
};

// Content mapping update mutation using dedicated endpoints
export const useContentMappingUpdateMutation = () => {
  const queryClient = useQueryClient();
  const { basePath } = useRouteHandler();

  return useMutation({
    mutationFn: ({
      contentType,
      id,
      request,
    }: {
      contentType: ContentMappingType;
      id: string;
      request: UnifiedMappingUpdateRequest;
    }) => updateContentMappingItem(contentType, id, request, basePath),
    onSuccess: (data, variables) => {
      // Invalidate and refetch the content mapping list for this content type
      queryClient.invalidateQueries({
        queryKey: ['contentMappingList', variables.contentType],
      });

      // Show success toast
      toast({
        variant: 'success',
        title: 'Success',
        description: `${variables.contentType} mapping item updated successfully`,
      });
    },
    onError: (error: Error, variables) => {
      console.error(
        `Failed to update ${variables.contentType} mapping item:`,
        error
      );

      // Show error toast notification
      toast({
        variant: 'error',
        title: 'Error',
        description: `Failed to update ${variables.contentType} mapping item: ${error.message}`,
      });
    },
  });
};

// Content mapping delete mutation using dedicated endpoints
export const useContentMappingDeleteMutation = () => {
  const queryClient = useQueryClient();
  const { basePath } = useRouteHandler();

  return useMutation({
    mutationFn: ({
      contentType,
      id,
    }: {
      contentType: ContentMappingType;
      id: string;
    }) => deleteContentMappingItem(contentType, id, basePath),
    onSuccess: (data, variables) => {
      // Invalidate and refetch the content mapping list for this content type
      queryClient.invalidateQueries({
        queryKey: ['contentMappingList', variables.contentType],
      });

      // Show success toast
      toast({
        variant: 'success',
        title: 'Success',
        description: `${variables.contentType} mapping item deleted successfully`,
      });
    },
    onError: (error: Error, variables) => {
      console.error(
        `Failed to delete ${variables.contentType} mapping item:`,
        error
      );

      // Show error toast notification
      toast({
        variant: 'error',
        title: 'Error',
        description: `Failed to delete ${variables.contentType} mapping item: ${error.message}`,
      });
    },
  });
};

// Content mapping multi-create mutation using dedicated endpoints
export const useContentMappingMultiCreateMutation = () => {
  const queryClient = useQueryClient();
  const { basePath } = useRouteHandler();

  return useMutation({
    mutationFn: ({
      contentType,
      request,
    }: {
      contentType: ContentMappingType;
      request: UnifiedMappingMultiCreateRequest;
    }) => multiCreateContentMappingItems(contentType, request, basePath),
    onSuccess: (data, variables) => {
      // Invalidate and refetch the content mapping list for this content type
      queryClient.invalidateQueries({
        queryKey: ['contentMappingList', variables.contentType],
      });

      // Show success toast
      toast({
        variant: 'success',
        title: 'Success',
        description: `${variables.request.items.length} ${variables.contentType} mapping items created successfully`,
      });
    },
    onError: (error: Error, variables) => {
      console.error(
        `Failed to multi-create ${variables.contentType} mapping items:`,
        error
      );

      // Show error toast notification
      toast({
        variant: 'error',
        title: 'Error',
        description: `Failed to create ${variables.contentType} mapping items: ${error.message}`,
      });
    },
  });
};
