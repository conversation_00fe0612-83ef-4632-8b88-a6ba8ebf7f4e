import { loadGlobalConfig } from '@cdss-modules/design-system/lib/globalConfig';
import { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    const data = loadGlobalConfig('ctint-mf-announcer');
    res.status(200).json({
      isSuccess: true,
      data,
      error: null,
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      isSuccess: false,
      error: 'Failed to load config',
    });
  }
}
