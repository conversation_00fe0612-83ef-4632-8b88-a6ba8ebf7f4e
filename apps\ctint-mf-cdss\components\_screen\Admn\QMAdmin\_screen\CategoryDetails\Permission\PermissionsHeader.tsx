import Input from '@cdss-modules/design-system/components/_ui/Input';
import Button from '@cdss-modules/design-system/components/_ui/Button';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';
import { ReusablePopup, PopupField } from '@cdss/components/_ui/ReusablePopup';
import { useState } from 'react';
import { UserPlus, Users } from 'lucide-react';

export interface PermissionsHeaderProps {
  searchValue: string;
  setSearchValue: (value: string) => void;
  onCreateUser: (values: Record<string, string>) => void;
  onCreateGroup: (values: Record<string, string>) => void;
}

export const PermissionsHeader = ({
  searchValue,
  setSearchValue,
  onCreateUser,
  onCreateGroup,
}: PermissionsHeaderProps) => {
  const { t } = useTranslation();
  const [isUserModalOpen, setIsUserModalOpen] = useState(false);
  const [isGroupModalOpen, setIsGroupModalOpen] = useState(false);

  // Define form fields for creating a new user
  const userFields: PopupField[] = [
    {
      key: 'name',
      label: 'User Name',
      placeholder: 'Enter full name (e.g., John Smith)',
      required: true,
      type: 'text',
    },
    {
      key: 'email',
      label: 'Email',
      placeholder: 'Enter email address',
      required: true,
      type: 'email',
    },
    {
      key: 'department',
      label: 'Department',
      placeholder: 'Enter department (e.g., Quality Management)',
      required: false,
      type: 'text',
    },
    {
      key: 'permissions',
      label: 'Permissions',
      placeholder:
        'Create, Edit, Submit, Approve, View, Admin (comma separated)',
      required: true,
      type: 'text',
    },
  ];

  // Define form fields for creating a new group
  const groupFields: PopupField[] = [
    {
      key: 'name',
      label: 'Group Name',
      placeholder: 'Enter group name (e.g., QM Analysts Team)',
      required: true,
      type: 'text',
    },
    {
      key: 'description',
      label: 'Description',
      placeholder: 'Describe the group purpose...',
      required: false,
      type: 'text',
    },
    {
      key: 'department',
      label: 'Department',
      placeholder: 'Enter department',
      required: false,
      type: 'text',
    },
    {
      key: 'permissions',
      label: 'Group Permissions',
      placeholder:
        'Create, Edit, Submit, Approve, View, Admin (comma separated)',
      required: true,
      type: 'text',
    },
    {
      key: 'members',
      label: 'Initial Members',
      placeholder: 'Enter user names (comma separated) - optional',
      required: false,
      type: 'text',
    },
  ];

  return (
    <>
      <div className="flex flex-col justify-between gap-2 p-6 border-b border-gray-200">
        <span className="text-lg font-medium">User & Group Permissions</span>
        <div className="flex items-center justify-between">
          <div className="relative">
            <Input
              placeholder="Search users and groups..."
              value={searchValue}
              onChange={(value) => setSearchValue(value.toString())}
              className="w-[280px]"
              size="s"
            />
          </div>
          <div className="flex gap-2">
            <ReusablePopup
              isOpen={isUserModalOpen}
              onOpenChange={setIsUserModalOpen}
              title="Create New User"
              fields={userFields}
              onSubmitMultiple={onCreateUser}
              triggerButtonText=""
              submitButtonText="Create User"
              showTrigger={false}
            />
            <Button
              variant="secondary"
              size="s"
              onClick={() => setIsUserModalOpen(true)}
              beforeIcon={<UserPlus size={16} />}
            >
              Add User
            </Button>

            <ReusablePopup
              isOpen={isGroupModalOpen}
              onOpenChange={setIsGroupModalOpen}
              title="Create New Group"
              fields={groupFields}
              onSubmitMultiple={onCreateGroup}
              triggerButtonText=""
              submitButtonText="Create Group"
              showTrigger={false}
            />
            <Button
              variant="primary"
              size="s"
              onClick={() => setIsGroupModalOpen(true)}
              beforeIcon={<Users size={16} />}
            >
              Add Group
            </Button>
          </div>
        </div>
      </div>
    </>
  );
};
