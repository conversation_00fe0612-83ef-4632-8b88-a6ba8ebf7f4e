'use client';

import React from 'react';
import { StepMetadata } from '../../types';

interface MetadataViewProps {
  metadata: StepMetadata[];
}

export const MetadataView: React.FC<MetadataViewProps> = ({ metadata }) => {
  if (!metadata || metadata.length === 0) {
    return <div className="text-gray-500">No metadata available</div>;
  }

  return (
    <div className="space-y-2">
      {metadata.map((item, index) => (
        <div
          key={index}
          className="p-2 bg-white border rounded-md"
        >
          <div className="grid grid-cols-4 gap-2">
            <div>
              <span className="text-sm text-gray-500">Field 1:</span>
              <div>{item.field1}</div>
            </div>
            <div>
              <span className="text-sm text-gray-500">Field 2:</span>
              <div>{item.field2}</div>
            </div>
            <div>
              <span className="text-sm text-gray-500">Operator:</span>
              <div>{item.operator}</div>
            </div>
            <div>
              <span className="text-sm text-gray-500">Value:</span>
              <div>{item.value}</div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default MetadataView;
