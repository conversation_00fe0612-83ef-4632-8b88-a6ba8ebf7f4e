'use client';

import { ArrowDown, ChevronDown, Plus, PlusCircle, Trash2 } from 'lucide-react';
import { StepData, ScenarioData } from '../../../../sop-template-queries';
import { ScenarioItem } from './ScenarioItem';
import { cn } from '@cdss-modules/design-system/lib/utils';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';

interface StepItemProps {
  step: StepData;
  isActive: boolean;
  currentScenario: string;
  onStepChange: (stepId: number) => void;
  onScenarioChange: (scenarioId: string) => void;
  onAddScenario?: () => void;
  onRemoveStep?: () => void;
  onRemoveScenario?: (scenarioId: string, scenarioName: string) => void;
}

export const StepItem = ({
  step,
  isActive,
  currentScenario,
  onStepChange,
  onScenarioChange,
  onAddScenario,
  onRemoveStep,
  onRemoveScenario,
}: StepItemProps) => {
  const { t } = useTranslation();
  const handleStepClick = () => {
    // Toggle the step - if it's already active, close it; otherwise, open it
    if (isActive) {
      onStepChange(0); // Set to 0 or invalid ID to close
    } else {
      onStepChange(step.id);
    }
  };

  return (
    <div
      className={`border rounded transition-all duration-200 ${isActive ? '' : ''}`}
    >
      <button
        type="button"
        className="p-2 flex justify-between items-center hover:bg-primary-100 w-full"
        onClick={handleStepClick}
      >
        <h2 className="flex-grow text-left font-medium py-1">
          {t('ctint-mf-cdss.qmAdmin.sopTemplates.stepNavigator.step')}{' '}
          {step.name}
        </h2>
        <div className="flex">
          {isActive && onRemoveStep && (
            <button
              type="button"
              onClick={onRemoveStep}
              className="p-1 rounded hover:bg-gray-100 text-red-500 transition-colors duration-150"
              aria-label={t(
                'ctint-mf-cdss.qmAdmin.sopTemplates.stepNavigator.removeStep'
              )}
              title={t(
                'ctint-mf-cdss.qmAdmin.sopTemplates.stepNavigator.removeStep'
              )}
            >
              <Trash2 size={14} />
            </button>
          )}
          <button>
            <ChevronDown
              size={14}
              className={cn(
                'transition-transform duration-200 ml-1',
                isActive ? 'rotate-180' : ''
              )}
            />
          </button>
        </div>
      </button>
      {isActive && (
        <div className="">
          {step.scenarios.map((scenario) => (
            <ScenarioItem
              key={scenario.id}
              scenario={scenario}
              isActive={currentScenario === scenario.id}
              onScenarioChange={onScenarioChange}
              onRemoveScenario={onRemoveScenario}
            />
          ))}
          {onAddScenario && (
            <div className="min-h-[28px] flex items-center">
              <button
                type="button"
                onClick={onAddScenario}
                className="p-1 px-2  hover:bg-primary-200 flex flex-row items-center gap-2 w-full"
                aria-label={t(
                  'ctint-mf-cdss.qmAdmin.sopTemplates.stepNavigator.addScenario'
                )}
                title={t(
                  'ctint-mf-cdss.qmAdmin.sopTemplates.stepNavigator.addScenario'
                )}
              >
                <PlusCircle size={14} />
                <span className="text-sm">
                  {t(
                    'ctint-mf-cdss.qmAdmin.sopTemplates.stepNavigator.addScenario'
                  )}
                </span>
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};
