'use client';

import { Loader } from '@cdss-modules/design-system';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';
import { DataTable } from '@cdss-modules/design-system/components/_ui/DataTable';
import { ColumnDef } from '@tanstack/react-table';
import { useState } from 'react';
import Checkbox from '@cdss-modules/design-system/components/_ui/Checkbox';
import Pagination from '@cdss-modules/design-system/components/_ui/Pagination';
import { Table as TableType } from '@tanstack/react-table';

export interface BaseTableData {
  id: string;
  name: string;
  language: string;
  status: string;
  version: string;
  createUser?: string;
  approvedBy: string;
  platform?: string; // Optional platform field for STT keywords
  [key: string]: any; // Allow for additional properties
}

export interface ReusableTableProps<T extends BaseTableData> {
  data: T[] | undefined;
  columns: ColumnDef<T>[];
  isLoading: boolean;
  onRowClick?: (item: T) => void;
  currentPage?: number;
  perPage?: number;
  onPageChange?: (page: number) => void;
  onPerPageChange?: (perPage: number) => void;
  totalItems?: number;
  emptyMessage?: string;
  totalPages?: number;
}

export const ReusableTable = <T extends BaseTableData>({
  data,
  columns,
  isLoading,
  onRowClick,
  currentPage = 1,
  perPage = 10,
  onPageChange,
  onPerPageChange,
  totalItems = 0,
  emptyMessage = 'No data found',
  totalPages,
}: ReusableTableProps<T>) => {
  const { t } = useTranslation();
  const [rowSelection, setRowSelection] = useState({});
  const [tableInstance, setTableInstance] = useState<
    TableType<T> | undefined
  >();

  const handleRowClick = (item: T) => {
    if (onRowClick) {
      onRowClick(item);
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-10">
        <Loader size={64} />
      </div>
    );
  }

  return (
    <div className="flex-1 overflow-y-auto h-full flex flex-col">
      <div className="flex-1 h-full overflow-y-auto mb-4">
        <DataTable<T>
          data={data}
          columns={columns}
          loading={isLoading}
          emptyMessage={emptyMessage}
          resize={true}
          onClickRow={(row) => handleRowClick(row.original)}
          rowSelection={rowSelection}
          setRowSelection={setRowSelection}
          onTableSetUp={(instance) => setTableInstance(instance)}
          pageSizeOnChange={onPerPageChange}
        />
      </div>

      {/* Custom pagination control */}
      {totalItems > 0 && (
        <Pagination
          current={currentPage}
          perPage={perPage}
          total={totalPages ? Math.ceil(totalPages) : 0}
          totalCount={totalItems}
          onChange={(page) => onPageChange?.(page)}
          handleOnPrevious={() => {
            if (currentPage > 1) {
              onPageChange?.(currentPage - 1);
            }
          }}
          handleOnNext={() => {
            if (currentPage < Math.ceil(totalItems / perPage)) {
              onPageChange?.(currentPage + 1);
            }
          }}
          handlePerPageSetter={(newPerPage) => {
            if (onPerPageChange) {
              const pageSize =
                typeof newPerPage === 'string'
                  ? parseInt(newPerPage, 10)
                  : newPerPage;
              if (!isNaN(pageSize)) {
                onPerPageChange(pageSize);
              }
            }
          }}
        />
      )}
    </div>
  );
};
