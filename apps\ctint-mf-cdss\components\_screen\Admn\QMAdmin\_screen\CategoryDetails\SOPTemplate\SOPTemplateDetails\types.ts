// Types for SOPTemplateDetails component

import { SOPTemplateDetailedData } from '../../sop-template-queries';

// Import types from parent component

// Step Data Interface
export interface StepData {
  id: number;
  name: string;
  script: string; // Main script text (defaults to englishText from API)

  // Script language variations and similarity from API
  similarityPercentage?: number;
  englishText?: string;
  cantoneseText?: string;
  putonghuaText?: string;

  // Legacy fields (keeping for backwards compatibility)
  scriptChinesePutonghua?: string;
  scriptChineseCantonese?: string;
  scriptSimilarity?: number;

  // Other step fields
  criteria: string;
  clientResponse: string;
  jumpingCriteria: string;
  importantWords: string;
  metadata: StepMetadata[];
}

export interface StepMetadata {
  field1: string;
  field2: string;
  operator: string;
  value: string;
}

export interface StageData {
  id: number;
  name: string;
  description?: string;
  steps: StepData[];
}

// Permission Types
export type Status = 'Draft' | 'Pending Approval' | 'Approved' | 'Returned';

// Navigation Context Types
export interface NavigationContextType {
  currentStep: number;
  currentScenario: number;
  setCurrentStep: (stageId: number) => void;
  setCurrentScenario: (stepId: number) => void;
  stages: StageData[];
  currentStepData: StageData | undefined;
  currentScenarioData: StepData | undefined;
}

// Template Data Context Types
export interface TemplateDataContextType {
  templateData: SOPTemplateDetailedData | null;
  isLoading: boolean;
  isError: boolean;
  updateTemplateField: (
    field: keyof SOPTemplateDetailedData,
    value: any
  ) => void;
  updateStepField: (
    stageId: number,
    stepId: number,
    field: keyof StepData,
    value: any
  ) => void;
  addStep: () => void;
  addScenario: (stageId: number) => void;
  removeStep: (stepId: number) => boolean;
  removeScenario: (stepId: number, scenarioId: string) => boolean;
  saveTemplate: () => Promise<void>;
  submitForApproval: () => Promise<void>;
}

// Component Props Types
export interface SOPTemplateDetailsProps {
  templateId: string;
}

export interface DetailsContentProps {
  className?: string;
  testId?: string;
}

export interface ActionButtonsProps {
  onSave?: () => void;
  onSubmit?: () => void;
  onTest?: () => void;
  disabled?: boolean;
  className?: string;
}

export interface StepNavigatorProps {
  stages: StageData[];
  currentStep: number;
  currentScenario: number;
  onStageChange: (stageId: number) => void;
  onStepChange: (stepId: number) => void;
}

export interface ScenarioContentProps {
  stepData: StepData;
  isEditing: boolean;
}

export interface DateRangeControlsProps {
  startDate: string;
  endDate: string;
  isEditing: boolean;
  onStartDateChange: (value: string) => void;
  onEndDateChange: (value: string) => void;
}
