'use client';

import React, { useState, useEffect } from 'react';
import Input from '@cdss-modules/design-system/components/_ui/Input';
import EnhancedTextarea from './EnhancedTextarea';

interface ScriptSectionContentProps {
  script: string;
  scriptChinesePutonghua?: string;
  scriptChineseCantonese?: string;
  scriptSimilarity?: number;
  onScriptChange?: (newScript: string) => void;
  onScriptChinesePutonghuaChange?: (value: string) => void;
  onScriptChineseCantoneseChange?: (value: string) => void;
  onScriptSimilarityChange?: (value: number) => void;
  disabled?: boolean;
}

export const ScriptSectionContent: React.FC<ScriptSectionContentProps> = ({
  script,
  scriptChinesePutonghua,
  scriptChineseCantonese,
  scriptSimilarity,
  onScriptChange,
  onScriptChinesePutonghuaChange,
  onScriptChineseCantoneseChange,
  onScriptSimilarityChange,
  disabled = false,
}) => {
  const [englishValue, setEnglishValue] = useState(script || '');
  const [chinesePutonghuaValue, setChinesePutonghuaValue] = useState(
    scriptChinesePutonghua || ''
  );
  const [chineseCantoneseValue, setChineseCantoneseValue] = useState(
    scriptChineseCantonese || ''
  );
  // Convert from decimal (0-1) to percentage (0-100) for display
  const [similarity, setSimilarity] = useState(
    Math.round((scriptSimilarity || 0) * 100)
  );

  // Sync local state with prop changes when navigating between steps
  useEffect(() => {
    setEnglishValue(script || '');
  }, [script]);

  useEffect(() => {
    setChinesePutonghuaValue(scriptChinesePutonghua || '');
  }, [scriptChinesePutonghua]);

  useEffect(() => {
    setChineseCantoneseValue(scriptChineseCantonese || '');
  }, [scriptChineseCantonese]);

  useEffect(() => {
    // Convert from decimal (0-1) to percentage (0-100) for display
    setSimilarity(Math.round((scriptSimilarity || 0) * 100));
  }, [scriptSimilarity]);

  // Add console.log to track all values
  useEffect(() => {
    console.log('ScriptSectionContent - Current values:', {
      englishValue,
      chinesePutonghuaValue,
      chineseCantoneseValue,
      similarity,
      script: script,
      scriptChinesePutonghua,
      scriptChineseCantonese,
      scriptSimilarity,
    });
  }, [
    englishValue,
    chinesePutonghuaValue,
    chineseCantoneseValue,
    similarity,
    script,
    scriptChinesePutonghua,
    scriptChineseCantonese,
    scriptSimilarity,
  ]);

  const handleEnglishValueChange = (value: string) => {
    console.log(
      'ScriptSectionContent - English value changing from:',
      englishValue,
      'to:',
      value
    );
    setEnglishValue(value);
    // Notify parent component of changes immediately
    if (onScriptChange) {
      onScriptChange(value);
    }
  };

  const handleSimilarityChange = (value: string | number) => {
    if (!disabled) {
      const stringValue = value.toString();
      // Allow empty string or valid numbers
      if (
        stringValue === '' ||
        (/^\d+$/.test(stringValue) &&
          parseInt(stringValue) >= 0 &&
          parseInt(stringValue) <= 100)
      ) {
        const numValue = stringValue === '' ? 0 : parseInt(stringValue);
        console.log(
          'ScriptSectionContent - Similarity changing from:',
          similarity,
          'to:',
          numValue
        );
        setSimilarity(numValue);
        if (onScriptSimilarityChange) {
          // Convert from percentage (0-100) to decimal (0-1) for storage
          onScriptSimilarityChange(numValue / 100);
        }
      }
    }
  };

  const handleChinesePutonghuaChange = (value: string) => {
    console.log(
      'ScriptSectionContent - Chinese Putonghua changing from:',
      chinesePutonghuaValue,
      'to:',
      value
    );
    setChinesePutonghuaValue(value);
    if (onScriptChinesePutonghuaChange) {
      onScriptChinesePutonghuaChange(value);
    }
  };

  const handleChineseCantoneseChange = (value: string) => {
    console.log(
      'ScriptSectionContent - Chinese Cantonese changing from:',
      chineseCantoneseValue,
      'to:',
      value
    );
    setChineseCantoneseValue(value);
    if (onScriptChineseCantoneseChange) {
      onScriptChineseCantoneseChange(value);
    }
  };

  return (
    <div className="space-y-4 pt-4 px-2">
      <div className="flex justify-start items-center gap-2">
        <span className="text-sm font-medium">Similarity ≥</span>
        <div className="relative w-20">
          <Input
            type="text"
            value={similarity.toString()}
            onChange={handleSimilarityChange}
            disabled={disabled}
            className="pr-6 text-center"
            placeholder="80"
            min="0"
            max="100"
          />
          <span className="absolute right-2 top-1/2 transform -translate-y-1/2 text-sm text-gray-500 pointer-events-none">
            %
          </span>
        </div>
      </div>

      <EnhancedTextarea
        label="English"
        value={englishValue}
        onChange={handleEnglishValueChange}
        placeholder={disabled ? '' : 'Enter English script content below...'}
        disabled={disabled}
      />

      <EnhancedTextarea
        label="Chinese (Putonghua)"
        value={chinesePutonghuaValue}
        onChange={handleChinesePutonghuaChange}
        placeholder={
          disabled ? '' : 'Enter Chinese (Putonghua) script content below...'
        }
        disabled={disabled}
      />

      <EnhancedTextarea
        label="Chinese (Cantonese)"
        value={chineseCantoneseValue}
        onChange={handleChineseCantoneseChange}
        placeholder={
          disabled ? '' : 'Enter Chinese (Cantonese) script content below...'
        }
        disabled={disabled}
      />
    </div>
  );
};

export default ScriptSectionContent;
