import { addLoggingToAxios } from '@cdss-modules/design-system/lib/logging';
import { apiConfig } from './config';
import axios from 'axios';
import { v4 as uuidv4 } from 'uuid';

export const axiosInstance = axios.create({
  timeout: 20000,
  headers: {
    'Content-Type': 'application/json',
    traceId: uuidv4(),
    tenant: 'ccba',
    sourceId: 'ctint-mf-announcer',
    previousId: 'ctint-bff-cdss',
  },
});

axiosInstance.interceptors.request.use(
  (config) => {
    // TODO: should call the auth api and set the token when program initialize
    if (
      process.env.NODE_ENV === 'development' &&
      localStorage.getItem('cdss-auth-token') === null
    ) {
      localStorage.setItem('deviceId', uuidv4());
      localStorage.setItem(
        'gc-access-token',
        'ofROc0Va1i74x3CKxPJ-et8g9jwo08wFkRgCzCcIYDwdAEmJMCIh-HDn9Sgw_U17WxTvtwnngeOksDWN8Sbatw'
      );
      localStorage.setItem(
        'cdss-auth-token',
        'EdOImMlG47o8McBVtqY2QKHtIoT7OPznIInDv7cOXG+0DnbPZAcG6Ra+604VzhdT+fX3I2pbhPKWfQSrGpd+hZ5OuZOcptbvJ01GpJkpzCbiiT6TpuU='
      );
    }
    const cdssAuthToken = localStorage.getItem('cdss-auth-token') || '';
    const gcAccessToken = localStorage.getItem('gc-access-token') || '';
    const deviceId = localStorage.getItem('deviceId') || '';
    if (cdssAuthToken) {
      config.headers['cdss_authorization'] = 'Bearer ' + cdssAuthToken;
    }
    if (gcAccessToken) {
      config.headers['gc_authorization'] = 'Bearer ' + gcAccessToken;
    }
    if (deviceId) {
      config.headers['deviceId'] = deviceId;
    }

    if (config.url?.includes('/interaction/recordings/transcript/')) {
      config.headers['requester'] = 'agent1';
    }

    console.log('axiosInstance.interceptors.request', config);
    return config;
  },
  (error) => {
    Promise.reject(error);
  }
);

addLoggingToAxios(axiosInstance);

export const getAnnouncerMessageList = async (basePath = '') => {
  return await axiosInstance.get(`${basePath}${apiConfig.paths.get_announcer_message_list}`);
}

export const getCallNatureList = async (basePath = '') => {
  return await axiosInstance.get(`${basePath}${apiConfig.paths.get_call_nature_list}`);
}

export const saveCallNature = async (data: any, basePath = '') => {
  return await axiosInstance.post(`${basePath}${apiConfig.paths.save_call_nature}`, data);
}

export const playAnnouncerMessage = async (data: any, basePath = '') => {
  return await axiosInstance.post(`${basePath}${apiConfig.paths.play_announcer_message}`, data);
}

export const stopAnnouncerMessage = async (data: any, basePath = '') => {
  return await axiosInstance.post(`${basePath}${apiConfig.paths.stop_announcer_message}`, data);
}

export const createFaceToFace = async (data: any, basePath = '') => {
  return await axiosInstance.post(`${basePath}${apiConfig.paths.face_to_face}`, data);
}

export const getConversationActiveList = async (basePath = '') => { 
  return await axiosInstance.get(`${basePath}${apiConfig.paths.get_conversation_active_list}`);
}

export const getCtintCallControlStationsUsersByUserId = async (userId: string | undefined, basePath = '') => {
  return await axiosInstance.get(`${basePath}${apiConfig.paths.get_ctint_call_control_stations_users_by_user_id}/${userId}`);
}

export default axiosInstance;
