'use client';

import { useTranslation } from '@cdss-modules/design-system/i18n/client';
import React, { useMemo, useEffect } from 'react';
import { Tabs } from '@cdss-modules/design-system/components/_ui/Tabs';
import {
  useTabsContext,
  TabsProvider,
} from '@cdss-modules/design-system/context/TabsContext';
import { useRole } from '@cdss-modules/design-system/context/RoleContext';
import { usePermission } from '@cdss-modules/design-system/context/PremissionContext';
import { CommonPermission } from '@cdss-modules/design-system/@types/CommonPermission';

// Import tab content components
import SOPCategoriesScreen from './Categories';
import RerunScreen from './Rerun';
import ReportScreen from './Report';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

interface QMAdminProps {
  hasRerunPermission: boolean;
}

const QMAdminHeader: React.FC<QMAdminProps> = ({ hasRerunPermission }) => {
  const { t } = useTranslation();
  const { selected: activeTab, onChangeTab: setActiveTab } = useTabsContext();

  // Define available tabs based on permissions
  const availableTabs = useMemo(() => {
    const tabs = [
      {
        value: 'categories',
        label: t('ctint-mf-cdss.qmAdmin.tabs.sop'),
      },
    ];

    if (hasRerunPermission) {
      tabs.push(
        {
          value: 'rerun',
          label: t('ctint-mf-cdss.qmAdmin.tabs.rerun'),
        },
        {
          value: 'report',
          label: t('ctint-mf-cdss.qmAdmin.tabs.jobHistory'),
        }
      );
    }

    return tabs;
  }, [hasRerunPermission, t]);

  // Check if current tab is available, if not, switch to 'categories'
  useEffect(() => {
    if (activeTab && !availableTabs.some((tab) => tab.value === activeTab)) {
      setActiveTab('categories');
    }
  }, [activeTab, availableTabs, setActiveTab]);

  return (
    <div className="">
      {/* <div className="mb-4">
        <span className="text-lg font-semibold">
          {t('ctint-mf-cdss.qmAdmin.title')}
        </span>
      </div> */}
      <Tabs
        onChangeTabFunc={(tab) => setActiveTab(tab)}
        defaultTab={activeTab || 'categories'}
        triggers={availableTabs}
        triggerClassName="py-1 px-2 text-body"
      />
    </div>
  );
};

const QMAdminContent: React.FC<QMAdminProps> = ({ hasRerunPermission }) => {
  const { selected: activeTab } = useTabsContext();

  const renderTabContent = () => {
    switch (activeTab) {
      case 'rerun':
        return hasRerunPermission ? <RerunScreen /> : <SOPCategoriesScreen />;
      case 'report':
        return hasRerunPermission ? <ReportScreen /> : <SOPCategoriesScreen />;
      case 'categories':
      default:
        return <SOPCategoriesScreen />;
    }
  };

  return (
    <div className="flex flex-col h-full bg-white rounded-xl">
      <QMAdminHeader hasRerunPermission={hasRerunPermission} />
      <div className="flex-1 overflow-hidden">{renderTabContent()}</div>
    </div>
  );
};

const queryClient = new QueryClient();

const QMAdminScreens: React.FC = () => {
  const { globalConfig } = useRole();
  const { permissions } = usePermission();

  // Single permission check for the entire component
  const hasRerunPermission = useMemo(() => {
    if (!globalConfig || !permissions) return false;

    const permissionEnabled = new CommonPermission(
      globalConfig,
      permissions
    ).isPermissionEnabled('ctint-mf-interaction', 'qm-rerun-job', 'submit');

    console.log('QMAdmin Permission Debug:', {
      userPermissions: permissions,
      globalConfig: globalConfig,
      hasRerunPermission: permissionEnabled,
      checkingPermission: 'ctint-mf-interaction.qm-rerun-job.submit',
    });

    return permissionEnabled;
  }, [globalConfig, permissions]);

  return (
    <TabsProvider>
      <QueryClientProvider client={queryClient}>
        <QMAdminContent hasRerunPermission={hasRerunPermission} />
      </QueryClientProvider>
    </TabsProvider>
  );
};

export default QMAdminScreens;
