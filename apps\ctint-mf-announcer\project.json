{"name": "ctint-mf-announcer", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/ctint-mf-announcer", "projectType": "application", "tags": [], "targets": {"build": {"executor": "@nx/next:build", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"root": "apps/ctint-mf-announcer", "outputPath": "dist/apps/ctint-mf-announcer"}, "configurations": {"development": {}, "production": {}}}, "serve": {"executor": "@nx/next:server", "defaultConfiguration": "development", "options": {"buildTarget": "ctint-mf-announcer:build", "dev": true, "port": 4040, "host": "0.0.0.0"}, "configurations": {"development": {"buildTarget": "ctint-mf-announcer:build:development", "dev": true}, "production": {"buildTarget": "ctint-mf-announcer:build:production", "dev": false}}}, "export": {"executor": "@nx/next:export", "options": {"buildTarget": "ctint-mf-announcer:build:production"}}, "test": {"executor": "@nx/jest:jest", "outputs": ["coverage/apps/ctint-mf-announcer"], "options": {"jestConfig": "apps/ctint-mf-announcer/jest.config.ts", "passWithNoTests": true}}, "lint": {"executor": "@nx/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/ctint-mf-announcer/**/*.{ts,tsx,js,jsx}"]}}}}