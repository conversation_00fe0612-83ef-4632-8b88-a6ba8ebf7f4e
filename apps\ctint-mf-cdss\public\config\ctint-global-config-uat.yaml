auth:
  headers:
    - name: traceId
    - name: tenant
    - name: sourceId
    - name: previousId
    - name: cdss_authorization
logger:
  headers:
    - name: traceId
    - name: tenant
    - name: sourceId
    - name: previousId
pureengage:
  host: http://*************:8090
  recordsLimit: 200
genesys-cloud:
  environment: mypurecloud.com.au
  apiHost: https://api.mypurecloud.com.au
  authHost: https://login.mypurecloud.com.au
loginTotal: 1
missingTotal: 3
poolTotal: 10000
heartbeatTime: 30
socketType: CDSS
recording:
  mediaSource:
    - aws
ctint-dab:
  auth:
    host: http://ctint-dab-auth-service.cdss-data-ctint.svc.cluster.local:5000 #<service-name>.<namespace>.svc.cluster.local:port
    basepath: /graphql
    active: true
  config:
    host: http://ctint-dab-config-service.cdss-data-ctint.svc.cluster.local:5000 #<service-name>.<namespace>.svc.cluster.local:port
    basepath: /graphql
    active: true
  conv:
    host: http://ctint-dab-conv-service.cdss-data-ctint.svc.cluster.local:5000 #<service-name>.<namespace>.svc.cluster.local:port
    basepath:
    active: true
  user:
    host: http://ctint-dab-user-service.cdss-data-ctint.svc.cluster.local:5000 #<service-name>.<namespace>.svc.cluster.local:port
    basepath: /graphql
    active: true
  dataSync:
    host: http://ctint-dab-datasync-service.cdss-data-ctint.svc.cluster.local:5000 #<service-name>.<namespace>.svc.cluster.local:port
    basepath: /graphql
    active: true
  callControl:
    host: http://ctint-dab-call-control-service.cdss-data-ctint.svc.cluster.local:5000 #<service-name>.<namespace>.svc.cluster.local:port
    restBasepath: /api
    graphBasepath: /graphql
    active: true
  jobWorker:
    host: http://ctint-dab-job-service.cdss-data-ctint.svc.cluster.local:5000 #<service-name>.<namespace>.svc.cluster.local:port
    restBasepath: /api
    graphBasepath: /graphql
    active: true
  common:
    host: http://ctint-graphql-service.cdss-data-ctint.svc.cluster.local:8110 #<service-name>.<namespace>.svc.cluster.local:port
    basepath: /ctint/query
    active: true
  auditLog:
    host: http://ctint-dab-audit-log-service.cdss-data-ctint.svc.cluster.local:5000 #<service-name>.<namespace>.svc.cluster.local:port
    basepath: /graphql
    active: true
  qm:
    host: http://ctint-dab-qm-service.cdss-data-ctint.svc.cluster.local:5000 #<service-name>.<namespace>.svc.cluster.local:port
    basepath: /graphql
    active: true
ctint-state:
  auth:
    host: http://ctint-state-auth-service.cdss-data-ctint.svc.cluster.local:35000 #<service-name>.<namespace>.svc.cluster.local:port
    basepath: /v1.0/state/ctint-state-auth
    active: true
  cdssmf:
    host: http://ctint-state-cdssmf-service.cdss-data-ctint.svc.cluster.local:35010
    basepath: /v1.0/state/ctint-state-cdssmf
    active: true
  session-manager:
    host: http://ctint-state-session-manager-service.cdss-data-ctint.svc.cluster.local:35020
    basepath: /v1.0/state/ctint-state-session-manager
    active: true
portals:
  - ctint-mf-cpp
services:
  ctint-stt:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/stt
    active: true
    provider:
      - astri1.5
    healthcheck: /healthcheck
  ctint-nlp:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/nlp
    active: true
    provider:
      - astri
    healthcheck: /healthcheck
  ctint-auth:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/auth
    active: true
    provider: # pure_engage / genesys_cloud / ctint-dab-auth / ctint-state-auth
      - genesys-cloud
      - ctint-dab-auth
      - ctint-state-auth
    healthcheck: /healthcheck
  ctint-conv:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/conv
    active: true
    provider:
      - pureengage
      - ctint-dab-conv
    healthcheck: /healthcheck
    criteriaSearch:
      - labelEn: Type
        labelCh: 媒體類型
        value: mediaType
        filterType: select
        active: true
        isMetaData: false
      - labelEn: Users
        labelCh: 用戶資訊
        value: users
        filterType: input
        active: true
        isMetaData: false
      - labelEn: Start Time
        labelCh: 開始時間
        value: conversationStart
        filterType: date
        active: true
        isMetaData: false
      - labelEn: End Time
        labelCh: 結束時間
        value: conversationEnd
        filterType: date
        active: true
        isMetaData: false
      - labelEn: Conversation ID
        labelCh: 對話 ID
        value: conversationId
        filterType: input
        active: false
        isMetaData: false
      - labelEn: ANI
        labelCh: 來電號碼
        value: ani
        filterType: input
        active: false
        isMetaData: false
      - labelEn: DNIS
        labelCh: 撥號號碼
        value: dnis
        filterType: input
        active: false
        isMetaData: false
      - labelEn: Duration
        labelCh: 持續時間
        value: conversationDuration
        filterType: compare
        active: true
        isMetaData: false
      - labelEn: Queues
        labelCh: 队列
        value: queues
        filterType: input
        active: true
        isMetaData: false
      - labelEn: Media Source
        labelCh: 媒體來源
        value: recordingMediaType
        filterType: input
        active: false
        isMetaData: false
      - labelEn: Customer Remote
        labelCh: 客户远程
        value: customerRemote
        filterType: input
        active: true
        isMetaData: false
      - labelEn: Wrapups
        labelCh: 总结代码
        value: wrapups
        filterType: input
        active: true
        isMetaData: false
      - labelEn: Provider
        labelCh: 提供者
        value: provider
        filterType: input
        active: false
        isMetaData: false
      - labelEn: Recording
        labelCh: 是否录音
        value: recording
        filterType: bool
        active: false
        isMetaData: false
      - labelEn: Direction
        labelCh: 方向
        value: direction
        filterType: select
        active: true
        isMetaData: false
      - labelEn: FinalResult
        labelCh: 最终结果
        value: finalResult
        filterType: select
        active: true
        isMetaData: false
      - labelEn: accountId
        labelCh: 账号Id
        value: accountId
        filterType: input
        active: false
        isMetaData: true
  ctint-ccba-api:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/ccba-api
    active: true
    healthcheck: /healthcheck 
  ctint-ccba-call-table:
    host: http://localhost:3333
    basepath: /
    active: true
    provider:
      - pureengage
      - ctint-dab-conv
    healthcheck: /healthcheck
    criteriaSearch:
      - labelEn: User ID
        labelCh: 用戶 ID
        value: localUserId
        filterType: input
        active: true
        isMetaData: false
      - labelEn: User Name
        labelCh: 用戶名稱
        value: localName
        filterType: input
        active: true
        isMetaData: false
      - labelEn: Start Time
        labelCh: 開始時間
        value: connectedDateTimeGMT
        filterType: date
        active: true
        isMetaData: false
      - labelEn: End Time
        labelCh: 結束時間
        value: terminatedDateTimeGMT
        filterType: date
        active: true
        isMetaData: false
      - labelEn: Conversation ID
        labelCh: 對話 ID
        value: callId
        filterType: input
        active: true
        isMetaData: false
      - labelEn: Recording ID
        labelCh: 錄音 ID
        value: recordingId
        filterType: input
        active: true
        isMetaData: false
      - labelEn: ANI
        labelCh: 來電號碼
        value: ani
        filterType: input
        active: true
        isMetaData: false
      - labelEn: DNIS
        labelCh: 撥號號碼
        value: dnis
        filterType: input
        active: true
        isMetaData: false
      - labelEn: Duration
        labelCh: 持續時間
        value: duration
        filterType: compare
        active: true
        isMetaData: false
      - labelEn: Branch code
        labelCh: 分行代碼
        value: branchCode
        filterType: input
        active: true
        isMetaData: false
      - labelEn: Wrapups
        labelCh: 总结代码
        value: wrapups
        filterType: input
        active: true
        isMetaData: false
      - labelEn: Direction
        labelCh: 方向
        value: callDirection
        filterType: select
        active: true
        isMetaData: false
      - labelEn: Customer CIF
        labelCh: 客户CIF
        value: customerCIF
        filterType: input
        active: true
        isMetaData: false
      - labelEn: SA Reference Number
        labelCh: SA參考號碼
        value: saReferenceNumber
        filterType: input
        active: true
        isMetaData: false
      - labelEn: Investment Order Type
        labelCh: 投資訂單類型
        value: investmentOrderType
        filterType: input
        active: true
        isMetaData: false
  ctint-config:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/config
    active: true
    provider:
      - pureengage
      - ctint-dab-config
    healthcheck: /healthcheck
  ctint-session:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/session
    active: true
    provider:
      - ctint-state-cdssmf
    healthcheck: /healthcheck
  ctint-session-manager:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/session-manager
    active: true
    healthcheck: /healthcheck
  ctint-cdss-ws:
    host: https://test-ctint-cdss-ws-webapp.azurewebsites.net
    basepath: /ctint-cdss-ws
    active: true
    healthcheck: /healthcheck
  ctint-ccsp-ws:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/ccsp-ws
    active: true
    healthcheck: /healthcheck
  ctint-call-control:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/call-control
    active: true
    healthcheck: /healthcheck
  ctint-user:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/user
    active: true
    healthcheck: /healthcheck
  ctint-datasync-hub:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/datasync-hub
    active: true
    healthcheck: /healthcheck
  ctint-job-worker:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/job-worker
    active: true
    healthcheck: /healthcheck
  ctint-job-worker-auto-qm:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/job-worker-auto-qm
    active: true
    healthcheck: /healthcheck
  ctint-job-engine:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/job-engine
    active: true
    healthcheck: /healthcheck
  ctint-qm:
    host: http://************
    basepath: /ctint/qm
    active: true
    provider:
    healthcheck: /healthcheck
  ctint-audit-log:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/audit-log
    active: true
    healthcheck: /healthcheck
  ctint-report:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/report
    active: true
    provider:
    healthcheck: /healthcheck
microfrontends:
  tenant: ccba
  passwordLoginOnly: false
  loginLogo: none
  logoutOnRefresh: false
  gc-client-id: a901c981-b618-4cb1-b3ff-1aec8b777860
  gc-auth-url: https://login.mypurecloud.com.au/oauth/authorize

  ctint-mf-announcer:
    host: http://localhost:4040
    basepath: /ctint/mf-announcer
  ctint-mf-cdss:
    host: http://localhost:4000
    basepath: /ctint/mf-cdss
    user-tab-names:
      - labelEn: Name
        labelCh: 姓名
        value: name
        filterType: input #select, date, compare, bool
        readOnly: false
        active: true
        require: true
      - labelEn: Username
        labelCh: 用戶賬號
        value: email
        filterType: input
        readOnly: false
        active: true
        require: true
      - labelEn: State
        labelCh: 狀態
        value: state
        filterType: select
        readOnly: false
        active: true
        require: false
      - labelEn: Description
        labelCh: 描述
        value: description
        filterType: input
        readOnly: false
        active: true
        require: false
      - labelEn: User Groups
        labelCh: 用戶組
        value: groupNames
        filterType: multipleSelect
        readOnly: false
        active: true
        require: false
      - labelEn: Roles
        labelCh: 角色
        value: roleNames
        filterType: multipleSelect
        readOnly: false
        active: true
        require: false
      - labelEn: Division
        labelCh: 部門
        value: divisionName
        filterType: input
        readOnly: true
        active: true
        require: false
      - labelEn: Platform
        labelCh: 平台
        value: platform
        filterType: input
        readOnly: true
        active: true
        require: false
      - labelEn: Tenant
        labelCh: 租戶
        value: tenant
        readOnly: true
        filterType: input
        active: true
        require: false
      - labelEn: Organization
        labelCh: 組織
        value: organization
        filterType: input
        readOnly: true
        active: true
        require: false
      - labelEn: Created By
        labelCh: 創建用戶
        value: createBy
        filterType: input
        readOnly: true
        active: true
        require: false
      - labelEn: Create At
        labelCh: 創建時間
        value: createTime
        filterType: dateRange
        readOnly: true
        active: true
        require: false
      - labelEn: Update By
        labelCh: 更新用戶
        value: updateBy
        filterType: input
        readOnly: true
        active: true
        require: false
      - labelEn: Update At
        labelCh: 更新時間
        value: updateTime
        filterType: dateRange
        readOnly: true
        active: true
        require: false
    audit-tab-names:
      - labelEn: Event Type
        labelCh: 事件類型
        value: eventType
        filterType: input
        readOnly: false
        active: true
        sort: false
      - labelEn: Event Time
        labelCh: 事件時間
        value: eventTimestamp
        filterType: dateRange
        readOnly: false
        active: true
        sort: true
      - labelEn: IP Address
        labelCh: IP地址
        value: ipAddress
        filterType: input
        readOnly: false
        active: true
        sort: false
      - labelEn: User Agent
        labelCh: 用戶代理
        value: userAgent
        filterType:
        readOnly: true
        active: true
        sort: false
      - labelEn: Browser
        labelCh: 瀏覽器
        value: browser
        filterType: input
        readOnly: false
        active: true
        sort: false
      - labelEn: Operating System
        labelCh: 操作系統
        value: operatingSystem
        readOnly: true
        filterType: input
        active: true
        sort: false
      - labelEn: Device
        labelCh: 設備
        value: device
        filterType: input
        readOnly: true
        active: true
        sort: false
      - labelEn: Failure Reason
        labelCh: 失敗原因
        value: failureReason
        filterType: input
        readOnly: true
        active: true
        sort: false
      - labelEn: Additional Info
        labelCh: 附加信息
        value: additionalInfo
        filterType: input
        readOnly: true
        active: true
        sort: false
  ctint-mf-interaction:
    host: http://localhost:4900
    basepath: /ctint/mf-interaction
    metaDataMappingColumns:
    - labelEn: Key
      labelCh: 關鍵詞
      value: keyDescription
      filterType: null
      active: true
      isMetaData: true
    - labelEn: Source
      labelCh: 來源
      value: dataSource
      filterType: null
      active: true
      isMetaData: true
    - labelEn: Display
      labelCh: 顯示值
      value: valueDisplay
      filterType: null
      active: true
      isMetaData: true
  ctint-mf-report:
    host: http://localhost:4302
    basepath: /ctint/mf-report
    record-filter:
    - labelEn: Report Type
      labelCh: 報告類型
      value: reportMasterType
      filterType: input
      active: true
      isMetaData: false
    - labelEn: Report Name
      labelCh: 報告名稱
      value: reportMasterName
      filterType: input
      active: true
      isMetaData: false
    - labelEn: User Name
      labelCh: 用戶名稱
      value: userName
      filterType: input
      active: true
      isMetaData: false
    - labelEn: Workgroup
      labelCh: 工作組
      value: workgroup
      filterType: input
      active: true
      isMetaData: false
    - labelEn: Report Date
      labelCh: 報告日期
      value: createTime
      filterType: date
      active: true
      isMetaData: false
    - labelEn: Queue Name
      labelCh: 隊列名稱
      value: queueName
      filterType: input
      active: true
      isMetaData: false
    - labelEn: State
      labelCh: 狀態
      value: state
      filterType: input
      active: true
      isMetaData: false
languages:
  supportedLanguages:
    - en
    - zh-HK
  defaultLanguage: zh-HK
settings:
  defaultStorage: aws
aws:
  default:
    bucket: ctint-cpp-recording-bucket
    region: ap-east-1
    accessKeyIdVariableName: AWS_ACCESS_KEY_ID
    secretAccessKeyVariableName: AWS_SECRET_ACCESS_KEY
permissionRelease: true
