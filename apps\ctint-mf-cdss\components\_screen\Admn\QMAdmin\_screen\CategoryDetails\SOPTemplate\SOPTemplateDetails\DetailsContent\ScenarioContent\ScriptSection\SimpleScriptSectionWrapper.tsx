'use client';

import React from 'react';
import { useSOPTemplateStore } from '../../../../../../../_store/sopTemplateStore';
import SimpleScriptSection from './SimpleScriptSection';

export const SimpleScriptSectionWrapper = () => {
  const {
    getCurrentStepData,
    currentStep,
    currentScenario,
    updateScenarioField,
    isEditing,
    getCurrentScenarioData,
  } = useSOPTemplateStore();

  const stepData = getCurrentStepData();
  const scenarioData = getCurrentScenarioData();

  if (!stepData || !scenarioData) {
    return null;
  }

  const handleScriptChange = (newScript: string) => {
    // Use the main script field (English) for simplicity
    updateScenarioField(currentStep, currentScenario, 'scriptEng', newScript);
  };

  const handleSimilarityChange = (value: number) => {
    // Value is already in decimal format (0-1) from SimpleScriptSection
    updateScenarioField(
      currentStep,
      currentScenario,
      'similarityRequired',
      value
    );
  };

  return (
    <SimpleScriptSection
      script={scenarioData.script || ''} // Use English script as default
      scriptEng={scenarioData.scriptEng || ''}
      similarity={scenarioData.similarityRequired}
      onScriptChange={isEditing ? handleScriptChange : undefined}
      onSimilarityChange={isEditing ? handleSimilarityChange : undefined}
      disabled={!isEditing}
    />
  );
};

export default SimpleScriptSectionWrapper;
