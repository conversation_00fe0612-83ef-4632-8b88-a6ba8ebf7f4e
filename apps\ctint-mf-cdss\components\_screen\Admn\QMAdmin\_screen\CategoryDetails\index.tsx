'use client';

import { Panel, useRouteHandler } from '@cdss-modules/design-system';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useState } from 'react';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';
import { useSearchParams } from 'react-router-dom';

import { CategoriesListHeader } from '../../_ui/CategoriesDetails/CategoriesListHeader';

import { TabProvider, useTab } from '../../context/TabContext';
import { DictionaryTab } from './Dictionary';
import { PermissionsProvider } from '../../context/PermissionsContext';
import {
  TabsProvider,
  useTabsContext,
} from '@cdss-modules/design-system/context/TabsContext';

import { MetadataTab } from './Metadata';
import { SOPTemplateTab } from './SOPTemplate';
import { SensitiveKeywordsTab } from './SensitiveKeywords';
import { STTKeywordsTab } from './STTKeywords';
import { PermissionsTab } from './Permission';

// ======================================
// Main Component
// ======================================
export const CategoryDetailsBody = ({ categoryId }: { categoryId: string }) => {
  const { basePath } = useRouteHandler();

  const { t } = useTranslation();

  // Use TabsContext directly for activeTab
  const { selected: activeTab } = useTabsContext();

  // Use TabContext only for selectedItem
  const { selectedItem } = useTab();

  // Render content based on active tab
  const renderContent = () => {
    switch (activeTab) {
      case 'sopTemplate':
        return <SOPTemplateTab categoryId={categoryId} />;
      case 'metadata':
        return <MetadataTab categoryId={categoryId} />;
      case 'dictionary':
        return <DictionaryTab categoryId={categoryId} />;
      case 'sensitiveKeywords':
        return <SensitiveKeywordsTab categoryId={categoryId} />;
      case 'sttKeywords':
        return <STTKeywordsTab categoryId={categoryId} />;
      // case 'permissions':
      //   return <PermissionsTab categoryId={categoryId} />;
      default:
        return <SOPTemplateTab categoryId={categoryId} />; // Default to SOP Template
    }
  };

  return (
    <Panel containerClassName="h-full overflow-hidden flex flex-col">
      {!selectedItem.id && !selectedItem.type && <CategoriesListHeader />}
      {renderContent()}
    </Panel>
  );
};

// Create a client
const queryClient = new QueryClient();

const CategoryDetailsScreen = () => {
  const [searchParams] = useSearchParams();
  const categoryId = searchParams.get('id') || '1';

  return (
    <QueryClientProvider client={queryClient}>
      <TabsProvider>
        <PermissionsProvider categoryId={categoryId}>
          <TabProvider categoryId={categoryId}>
            <CategoryDetailsBody categoryId={categoryId} />
          </TabProvider>
        </PermissionsProvider>
      </TabsProvider>
    </QueryClientProvider>
  );
};

export default CategoryDetailsScreen;
